{"name": "mcms-onmisite-webapp", "version": "1.0.0", "description": "mcms onmisite plugin", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "repository": {"type": "git", "url": "https://git.misynergy.com/hnb-assurance/hnb-general-insurance/claim-management-system/mcms-onmisite-webapp.git"}, "author": "", "license": "ISC", "dependencies": {"@fortawesome/fontawesome-free": "^6.7.2", "bootstrap": "^5.3.3", "jquery": "^3.7.1", "lucide": "^0.483.0", "jquery.magnify": "^1.6.3"}}