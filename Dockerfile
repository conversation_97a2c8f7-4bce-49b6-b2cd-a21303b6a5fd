FROM registry.misynergy.com/hnb-mcms/library/maven:3.8.8-eclipse-temurin-21 AS builder

WORKDIR /app

COPY .  /app

RUN /usr/share/maven/bin/mvn -s /app/.m2/settings.xml --batch-mode -Dmaven.repo.local=/app/.m2/repository package -P qa -DskipTests=true

FROM proxy-registry/library/openjdk:21
WORKDIR /home/<USER>/app
RUN mkdir config
COPY --from=builder /app/target/common-service-api.jar /home/<USER>/app/common-service-api.jar
EXPOSE 8080
WORKDIR /home/<USER>/app/
RUN chmod +x /home/<USER>/app/common-service-api.jar
ENTRYPOINT ["java", "-jar", "/home/<USER>/app/common-service-api.jar"]
