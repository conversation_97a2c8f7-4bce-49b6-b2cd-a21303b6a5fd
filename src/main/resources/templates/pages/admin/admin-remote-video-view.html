<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" name="viewport"/>
    <link href="/static/assets/global/image/favico.png" rel="shortcut icon"
          th:href="@{/resources/assets/img/Mini-Logo.png}"
          type="images/favico.png"/>

    <title>OnMISite</title>
    <link href="main.css" rel="stylesheet" th:href="@{/resources/main.css}">
    <link href="main-styles.css" rel="stylesheet" th:href="@{/resources/main-styles.css}">

    <link href="node_modules/bootstrap/dist/css/bootstrap.min.css" rel="stylesheet" th:href="@{/css/bootstrap/bootstrap.min.css}">
    <script src="node_modules/bootstrap/dist/js/bootstrap.bundle.min.js" th:src="@{/js/bootstrap/bootstrap.bundle.min.js}"></script>

    <link href="node_modules/@fortawesome/fontawesome-free/css/all.min.css" rel="stylesheet" th:href="@{/css/fontawesome/all.min.css}">
    <script src="node_modules/@fortawesome/fontawesome-free/js/all.min.js" th:src="@{/js/fontawesome/all.min.js}"></script>

    <link href="assets/css/font.css" rel="stylesheet" th:href="@{/resources/assets/css/font.css}">
    <link href="assets/css/font.css" rel="stylesheet" th:href="@{/js/jquery.magnify/jquery.magnify.min.css}">
<!--    <link href="assets/fontawesome/css/all.css" rel="stylesheet" th:href="@{/resources/assets/fontawesome/css/all.css}">-->
<!--    <link href="assets/bootstrap/dist/css/bootstrap.min.css" rel="stylesheet" th:href="@{/resources/assets/bootstrap/dist/css/bootstrap.min.css}">-->
    <script src="assets/js/jquery.min.js" th:src="@{/js/jquery/jquery.js}"></script>
<!--    <script src="assets/js/fontawesome.js" th:src="@{/resources/assets/js/fontawesome.js}"></script>-->
<!--    <script src="assets/fontawesome/js/all.js" th:src="@{/resources/assets/fontawesome/js/all.js}"></script>-->
<!--    <script src="assets/bootstrap/dist/js/bootstrap.min.js" th:src="@{/resources/assets/bootstrap/dist/js/bootstrap.bundle.min.js}"></script>-->
    <script th:inline="javascript"> let contextRoot = /*[[@{/}]]*/ ''; </script>
    <style>.bot-map {
        position: absolute;
        bottom: -10px;
        left: -11px;
        z-index: 100;
    }

    .on-mi-alert-show-counter .toast {
        z-index: 99999;
        right: 0;
        top: 5px;
        position: absolute;
        left: 17px;
        display: block;
    }

    .blob {
        background: black;
        border-radius: 50%;
        box-shadow: 0 0 0 0 rgba(0, 0, 0, 1);
        margin: 0px;
        height: 20px;
        width: 20px;
        transform: scale(1);
        animation: pulse-black 2s infinite;
    }

    .blob.red {
        background: rgba(255, 82, 82, 1);
        box-shadow: 0 0 0 0 rgba(255, 82, 82, 1);
        animation: pulse-red 2s infinite;
    }

    @keyframes pulse-red {
        0% {
            transform: scale(0.95);
            box-shadow: 0 0 0 0 rgba(255, 82, 82, 0.7);
        }

        70% {
            transform: scale(1);
            box-shadow: 0 0 0 10px rgba(255, 82, 82, 0);
        }

        100% {
            transform: scale(0.95);
            box-shadow: 0 0 0 0 rgba(255, 82, 82, 0);
        }
    }

    .nav-scroller {
        position: relative;
        z-index: 2;

        /*overflow-y: hidden;*/
    }

    .nav-scroller .nav {
        display: -webkit-box;
        display: -ms-flexbox;
        display: flex;
        -ms-flex-wrap: nowrap;
        flex-wrap: nowrap;
        overflow-x: auto;
        white-space: nowrap;
        -webkit-overflow-scrolling: touch;
    }

    .contact {
        background-color: #fff;
        padding: 15px;
        max-width: 200px;
        margin: 30px auto;
    }

    .icon {
        width: 16px;
        height: 16px;
        padding: 0;
        margin: 0;
        vertical-align: middle;
    }

    /* video-play {
         height: calc(100% + 2px);
         left: -1px;
         pointer-events: none;
         position: absolute;
         top: -1px;
         width: calc(100% + 2px);
     }*/

    #pdfModal .modal-dialog {
        max-width: 70%; /* Adjust the maximum width as needed */
    }

    .drawing-pad-modal {
        display: none;
        position: fixed;
        z-index: 1000;
        padding-top: 100px;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        overflow: auto;
        background-color: rgb(0,0,0);
        background-color: rgba(0,0,0,0.4);
    }

    .drawing-pad-modal .drawing-pad-modal-content {
        /*position: relative;*/
        margin: auto;
        padding: 0;
        width: 680px;
        /*max-width: 1200px;*/
    }

    .drawing-pad-modal .close {
        position: absolute;
        right: 2px;
        top: -4px;
        color: #000;
        font-size: 30px;
        font-weight: bold;
        z-index: 100000;
    }

    .drawing-pad-modal .close:hover,
    .drawing-pad-modal .close:focus {
        color: red;
        cursor: pointer;
    }

    #drawingCanvas {
        /*position: relative;*/
        /*left: calc(50% - 390px);*/
        /*top: 50px;*/
        border: 1px dotted black;
        cursor: crosshair;
        background: #ecf0f1;
    }

    #commentInput {
        /*display: none;*/
        /*position: absolute;*/
        border: 1px solid #ccc;
        padding: 5px;
    }

    /*.toolbar {
        display: flex;
        align-items: center;
        gap: 10px;
    }

    .tool-button {
        border: none;
        background: none;
        font-size: 1.5em;
        padding: 5px;
    }

    .tool-select {
        width: 60px;
        margin-left: 10px;
    }*/

    .toolbar {
        display: flex;
        flex-direction: column;
        position: absolute;
        /*left: 10px;*/
        /*left: -84px;*/
        left: -75px;
        top: 50px;
        z-index: 1000;
        background-color: rgba(255, 255, 255, 0.9);
        padding: 10px;
        border-radius: 8px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    }

    .tool-button, .tool-select, .tool-color {
        margin-bottom: 5px;
        width: 100%;
    }

    #drawingCanvas {
        /*position: relative;*/
        left: 80px; /* Adjust this value to move the canvas to the right */
    }

    .modal-body {
        position: relative;
    }

    .selected {
        background-color: #d3d3d3; /* or any other highlight color */
    }

    .highlighted {
        background-color: yellow;
        opacity: 0.5;
        pointer-events: none;
    }

    .tool-button.active {
        background-color: #007bff;
        color: white;
    }

    </style>


</head>
<body style="overflow: hidden;">


<!--On MI Site Page Main Wrapper  -->
<div id="wrapper">


    <!-- Content-->
    <div class="d-flex flex-column" id="content-wrapper" style="background: #f7f7f7;">
        <input
                th:id="${_csrf.parameterName}"
                th:name="${_csrf.parameterName}"
                th:value="${_csrf.token}"
                type="hidden"/>
        <input
                id="remoteUser"
                name="remoteUser"
                th:value="${agentVideoForm.callDetailsDto.accessToken}"
                type="hidden"/>

        <input
                id="callId"
                name="callId"
                th:value="${agentVideoForm.callDetailsDto.callId}"
                type="hidden"/>

        <input
                id="localUser"
                name="localUser"
                th:value="${agentVideoForm.callDetailsDto.getCreatedBy()+'-'+agentVideoForm.callDetailsDto.accessToken}"
                type="hidden"/>

        <input id="latitude" name="latitude" type="hidden" value=""/>
        <input id="longitude" name="longitude" type="hidden" value=""/>

        <!-- Header Top Details-->
        <div th:insert="~{pages/admin/fragments/admin-remote-video-header-view :: header}"></div>
        <!-- End of Header Top Details-->

        <!-- Drawing Modal Container-->
        <div th:insert="~{pages/admin/fragments/drawing-modal-container :: drawingModalContainer}"></div>
        <!-- End of Drawing Modal Container-->

        <!-- Main Content -->
        <div th:insert="~{pages/admin/fragments/main-container :: mainContainer}"></div>
        <!-- On MI Site Page Main Wrapper -->
    </div>

    <div class="on-bot-camera">
        <div class="justify-content-center " id="localVideoContainer">
            <video autoplay controls height="150" id="localVideo" muted="muted" style="display: none"
                   width="150"></video>
        </div>
        <button class="btn btn-primary btn-sm"
                onclick="showLocalCamera()" style="border: 1px solid #428bfa; float: right; width: 34px; height: 34px;"
                data-bs-toggle="tooltip" data-bs-placement="left" data-bs-title="My Camera"
                type="button">
            <img class="mi-top-icn" src="assets/img/camera.svg" th:src="@{/resources/assets/img/camera.svg}">
        </button>
    </div>

    <div class="view-map-modal-container">
<!--        <button type="button" class="btn btn-outline-primary">-->
<!--            <i class="fa-solid fa-map-location-dot"></i>-->
<!--        </button>-->

        <button type="button" class="btn btn-primary" onclick="openMapModal()"
                style="width: 34px;height: 34px;display: flex;align-items: center;justify-content: center;"
                data-bs-toggle="tooltip" data-bs-placement="left" data-bs-title="View Map"
        >
            <i class="fa-solid fa-map-location-dot"></i>
        </button>
    </div>

    <!-- Map  Modal -->
    <div th:insert="~{pages/admin/fragments/map-modal-container :: mapModalContainer}"></div>
    <!-- End Map  Modal -->

    <!--Page loading-->
    <div class="loader-wrapper">
        <div class="spinner-border text-primary" role="status">
        </div>
    </div>
    <!--End of Page loading-->


    <!--Play Video view Modal-->
    <div aria-hidden="true" aria-labelledby="myLargeModalLabel" class="modal fade bd-example-modal-lg"
         id="showVideoPlayerModal"
         role="dialog" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <p class="modal-title" id="exampleModalLongTitle"></p>
                    <button aria-label="Close" class="close" data-dismiss="modal" type="button">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="container-fluid">
                        <div class="row">
                            <div class="col-md-12">
                                <div class="d-flex justify-content-center">
                                    <video controls height="480" id="playVideo" width="720"></video>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!--Play Video view Modal-->
    <div th:insert="~{pages/admin/fragments/video-view-modal-container :: videoViewModalContainer}"></div>
    <!--End Play Video view Modal-->

    <!-- Video Button Panel -->
    <div th:insert="~{pages/admin/fragments/video-button-panel-container :: videoButtonPanelContainer}"></div>
    <!-- End Video Button Panel -->
</div>
<!--Toast-->
<script>
    $(document).ready(function () {
        initTwilio();
        $(".toast").toast('show');

    });
</script>
<!--Boostrap tooltips enable-->
<script>
    document.addEventListener("DOMContentLoaded", function () {
      const tooltipTriggerList = document.querySelectorAll('[data-bs-toggle="tooltip"]');
      const tooltipList = [...tooltipTriggerList].map(tooltipTriggerEl => new bootstrap.Tooltip(tooltipTriggerEl));
    });
</script>


<!--bottom camera-->

<script th:inline="javascript">

    $(document).ready(function () {
        $('#btnNewInspectionModal').click(function () {
            $('#newInspectionModal').css("display", "block");
            $('#newInspectionModal').css("opacity", "1");
        });

        $('#inspectionModalClose').click(function () {
            $('#newInspectionModal').css("display", "none");
        });

        $('#inspectionModalClose2').click(function () {
            $('#newInspectionModal').css("display", "none");
        });
    });

    $(window).on("load", function () {
        $(".loader-wrapper").fadeOut("slow");


    });


    function onLogout() {
        updateCallEndDateTime();
        let url =/*[[@{/logout}]]*/ 'test';
        window.location.assign(url);
    }

    const showMapModal = (latitude, longitude) => {
        let url =/*[[@{/view-map}]]*/ 'test';
        url = url + "/" + latitude + "/" + longitude;
        document.getElementById("mapIframe").src = url;
        $('#videoMapModal').modal('show');
    }

    $(document).ready(function () {

        // getCurrentInspectionDetail();
      //  getInspectionList();

        // Add a submit event listener to the form
        $('#inspectionForm').submit(function (event) {
            // Prevent the default form submission behavior
            event.preventDefault();

            // Gather the form data
            var formData = $(this).serializeArray();

            console.log(" form data ", formData);

            // Call saveInspection function with formData
            saveInspection(formData);
        });
    });

    function saveInspection(formData) {

        console.log("click save button ")
        console.log("form data 1", formData)

        let url = '/misyn/api/v1/inspection-summary-service/save';
        // let viewCurrentInspectionUrl = '/misyn/api/v1/inspection-summary-service/view-current-inspection/';
        let viewCurrentInspectionUrl = /*[[@{/api/v1/inspection-summary-service/view-current-inspection/}]]*/ 'test';
        const callId = document.getElementById("callId").value;

        let csrfToken = document.getElementById('_csrf').value;

        // Convert formData to JSON
        /* let jsonData = {};
         $.each(formData, function(index, field) {
             jsonData[field.name] = field.value;
         });*/

        // const jsonData = JSON.stringify(formData);
        let serializedFormData = formData.map(item => `${encodeURIComponent(item.name)}=${encodeURIComponent(item.value)}`).join('&');


        let request = new Request(url, {
            method: 'POST',
            body: serializedFormData,
            headers: new Headers({
                'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8',
                'X-CSRF-TOKEN': csrfToken
            })
        });

        fetch(request)
            .then(response => {
                console.log("response ", response)
                if (!response.ok) {
                    if (response.status === 409) {
                        response.json().then(data => {
                            console.log("message ", data.message);
                            // alert(data.message);
                            showToast("Warning", data.message, TOAST_TYPE.WARNING);
                        }).catch(error => {
                            console.error('Error while parsing JSON:', error);
                        });
                        return;
                    }
                    throw new Error('Network response was not ok');
                }
                // alert("Record saved successfully");

                const inspectionButton = document.getElementById('btnNewInspectionModal');
                inspectionButton.textContent = 'Update Inspection';

                $('#newInspectionModal').modal('hide');
                $('#newInspectionModal').css("display", "none");

                showToast("Success", "Record saved successfully", TOAST_TYPE.SUCCESS);

                fetch(`${viewCurrentInspectionUrl}${callId}`).then(res => {
                    res.text().then(value => {
                        document.getElementById("div-current-inspection").innerHTML = value;
                    })
                })
                //getInspectionList();

            })
            .catch(error => {
                // Handle error
                console.error('There was a problem with the fetch operation:', error);
                // alert("Can not be Saved");
                showToast("Error", "Can not be Saved", TOAST_TYPE.ERROR);
            });

    }

    const getCurrentInspectionDetail = () => {
        const callId = document.getElementById("callId").value;
        let viewCurrentInspectionUrl = /*[[@{/api/v1/inspection-summary-service/view-current-inspection/}]]*/ 'test';
        fetch(`${viewCurrentInspectionUrl}${callId}`).then(res => {
            res.text().then(value => {
                document.getElementById("div-current-inspection").innerHTML = value;
            })
        })
    }

    function generatePDf() {
        console.log("click PDF ")

        /*let url = '/misyn/api/v1/house-inspection-generate-pdf/test';

        let viewCurrentInspectionUrl = /!*[[@{/api/v1/house-inspection-generate-pdf/test/}]]*!/ 'test';

        fetch(`${url}`).then(res => {
            res.text().then(value => {
                console.log(" res ", value)
            })
        })*/

        let url = '/misyn/api/v1/house-inspection-generate-pdf/test';

        fetch(url)
            .then(res => res.blob()) // Convert response to blob
            .then(blob => {
                // Create URL for blob object
                const blobUrl = URL.createObjectURL(blob);

                // Display PDF in iframe
                /*    const iframe = document.createElement('iframe');
                    iframe.style.width = '100%';
                    iframe.style.height = '100%';
                    iframe.src = blobUrl;
                    document.body.appendChild(iframe);*/

                // Or open PDF in new window
                window.open(blobUrl);
            })
            .catch(error => {
                console.error('Error fetching PDF:', error);
            });

    }

    function getInspectionList() {
        const callId = document.getElementById("jobId").value;
        let viewCurrentInspectionUrl = /*[[@{/api/v1/inspection-summary-service/view-job-inspection-list/}]]*/ 'test';
        fetch(`${viewCurrentInspectionUrl}${callId}`).then(res => {
            res.text().then(value => {
                document.getElementById("div-previous-inspection-list").innerHTML = value;
            })
        })
    }

    function generatePdf(inspectionCallId) {

        console.log("click generate button ", inspectionCallId)

        let url = /*[[@{/api/v1/house-inspection-generate-pdf/generate/}]]*/ 'test';
        // let url = '/misyn/api/v1/house-inspection-generate-pdf/test';


        fetch(`${url}${inspectionCallId}`)
            .then(res => res.blob()) // Convert response to blob
            .then(blob => {
                // Create URL for blob object
                const blobUrl = URL.createObjectURL(blob);

                // Display PDF in iframe
                /*    const iframe = document.createElement('iframe');
                    iframe.style.width = '100%';
                    iframe.style.height = '100%';
                    iframe.src = blobUrl;
                    document.body.appendChild(iframe);*/

                /*   // Display PDF in modal body
                   const modalBody = document.querySelector('#pdfModal .modal-body');
                   modalBody.innerHTML = `<embed src="${blobUrl}" style="width: 100%; height: 100%;">`;

                   // Open modal
                   $('#pdfModal').modal('show'); // Assuming you're using jQuery for Bootstrap Modal

                   // Or open PDF in new window
                   window.open(blobUrl);*/


                // Create a new embed element to load the PDF
                const embed = document.createElement('embed');
                embed.src = blobUrl;
                embed.style.width = '100%';
                embed.style.height = '100%';

                // Create a new div to contain the embed element
                const containerDiv = document.createElement('div');
                containerDiv.appendChild(embed);
                containerDiv.style.height = '100%';

                // Set the modal body content to the container div
                const modalBody = document.querySelector('#pdfModal .modal-body');
                modalBody.innerHTML = '';
                modalBody.appendChild(containerDiv);

                // Open modal
                $('#pdfModal').modal('show'); // Assuming you're using jQuery for Bootstrap Modal

                // After the PDF content is loaded into the modal, measure its height
                embed.onload = () => {
                    const pdfHeight = embed.scrollHeight;
                    modalBody.style.height = pdfHeight + 'px';
                };


            })
            .catch(error => {
                console.error('Error fetching PDF:', error);
            });
    }

</script>




<!--//copy clip board-->
<script th:inline="javascript">

    const videoTag = document.getElementById("playVideo");

    const showPlayVideoModal = (uploadVideoDetailsRefNo) => {
        let url =/*[[@{/api/v1/video/play}]]*/ 'test';
        url = url + "/" + uploadVideoDetailsRefNo;
        const myMediaSource = new MediaSource();
        const urlPlay = URL.createObjectURL(myMediaSource);
        videoTag.src = url;
        videoTag.play();
        $('#showVideoPlayerModal').modal('show');
    }

    $('#showVideoPlayerModal').on('hidden.bs.modal', function (e) {
        videoTag.pause();
    });

    function copyToClipboard(text, el) {
        var copyTest = document.queryCommandSupported('copy');
        var elOriginalText = el.attr('data-original-title');

        if (copyTest === true) {
            var copyTextArea = document.createElement("textarea");
            copyTextArea.value = text;
            document.body.appendChild(copyTextArea);
            copyTextArea.select();
            try {
                var successful = document.execCommand('copy');
                var msg = successful ? 'Copied!' : 'Whoops, not copied!';
                el.attr('data-original-title', msg).tooltip('show');
            } catch (err) {
                console.log('Oops, unable to copy');
            }
            document.body.removeChild(copyTextArea);
            el.attr('data-original-title', elOriginalText);
        } else {
            // Fallback if browser doesn't support .execCommand('copy')
            window.prompt("Copy to clipboard: Ctrl+C or Command+C, Enter", text);
        }
    }

    $(document).ready(function () {
        reloadImageViewer(document.getElementById("callId").value);
        $('.js-tooltip').tooltip();
        $('.js-copy').click(function () {
            var text = $(this).attr('data-copy');
            var el = $(this);
            copyToClipboard(text, el);
        });
    });

    /*<![CDATA[*/
    function setViewMapIframeSrc(latitude, longitude) {
        var latitude1 = /*[[${latitude}]]*/ '';
        var longitude1 = /*[[${longitude}]]*/ '';
        var iframe = document.getElementById('mapIframeLeftSide');
        iframe.src = '/misyn/view-map/' + latitude + '/' + longitude;
    }

    /*]]>*/


    /* $('#videoMapModal').on('shown.bs.modal',function(){     //correct here use 'shown.bs.modal' event which comes in bootstrap3
         alert("ok")
         $(this).find('iframe').attr('src','/misyn/view-map/6.901608599999999/80.0087746')
     })*/
</script>

<script>
    function openMapModal() {
      const modalElement = document.getElementById('rightSideMapModal');
      const modal = new bootstrap.Modal(modalElement, {
        backdrop: false,  // No backdrop
        keyboard: true    // Allow ESC to close
      });
      modal.show();
    }
</script>

<script src="//sdk.twilio.com/js/video/releases/2.8.0/twilio-video.min.js"></script>

<!--<script src="node_modules/bootstrap/dist/js/bootstrap.min.js"-->
<!--        th:src="@{/js/bootstrap/bootstrap.min.js}"></script>-->

<script src="node_modules/lucide/dist/umd/lucide.min.js"
        th:src="@{/js/lucide/lucide.min.js}"></script>

<!--<script src="node_modules/bootstrap/dist/js/bootstrap.bundle.js"-->
<!--        th:src="@{/js/bootstrap/bootstrap.bundle.js}"></script>-->

<script th:src="@{/js/jquery.magnify/jquery.magnify.min.js}" type="text/javascript"></script>
<script th:src="@{/resources/js/.env.js}" type="text/javascript"></script>
<script th:src="@{/resources/js/toast/toast.js}" type="text/javascript"></script>
<script th:src="@{/resources/js/web-rtc/local/signalingchannel.js}" type="text/javascript"></script>


<script th:src="@{/resources/js/web-rtc/recording-timer.js}" type="text/javascript"></script>
<script th:src="@{/resources/js/web-rtc/common.js}" type="text/javascript"></script>
<script th:src="@{/resources/js/web-rtc/update-backend.js}" type="text/javascript"></script>
<script th:src="@{/resources/js/web-rtc/local/video-capture.js}" type="text/javascript"></script>

<script th:src="@{/resources/js/web-rtc/local/twilio-admin.js}" type="text/javascript"></script>


<script th:src="@{/resources/js/web-rtc/local/image-capture.js}" type="text/javascript"></script>
<script th:src="@{/resources/js/image-edit/image-edit.js}" type="text/javascript"></script>

</body>
</html>
