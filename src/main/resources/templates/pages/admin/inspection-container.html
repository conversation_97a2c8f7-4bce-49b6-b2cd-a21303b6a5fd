<div class="card bg-light mb-1" xmlns:th="http://www.thymeleaf.org">
    <div class="card-body inspection-card-details">
        <div class="row">
            <div class="col-6">
                <small><span>Inspection Type:</span></small>
            </div>
            <div class="col-6 text-right">
                <small><span class="font-weight-bold" th:text="${currentInspection.inspectionTypeDesc}">On Site Inspection</span></small>
            </div>
            <div class="col-12">
                <hr style="margin-bottom: 8px !important; margin-top: 8px; !important;"/>
            </div>
        </div>
        <div class="row">
            <div class="col-6">
                <small><span>Mobile Number:</span></small>
            </div>
            <div class="col-6 text-right">
                <small><span class="font-weight-bold" th:text="${currentInspection.mobileNo}">0771234578</span></small>
            </div>
            <div class="col-12">
                <hr style="margin-bottom: 8px !important; margin-top: 8px; !important;"/>
            </div>
        </div>
        <div class="row">
            <div class="col-6">
                <small><span>Vehicle Number:</span></small>
            </div>
            <div class="col-6 text-right">
                <small><span class="font-weight-bold"
                             th:text="${currentInspection.vehicleNo}">WP ABC 1234</span></small>
            </div>
            <div class="col-12">
                <hr style="margin-bottom: 8px !important; margin-top: 8px; !important;"/>
            </div>
        </div>
        <div class="row">
            <div class="col-6">
                <small><span>Make:</span></small>
            </div>
            <div class="col-6 text-right">
                <small><span class="font-weight-bold" th:text="${currentInspection.vehicleMake}">Toyota</span></small>
            </div>
            <div class="col-12">
                <hr style="margin-bottom: 8px !important; margin-top: 8px; !important;"/>
            </div>
        </div>
        <div class="row">
            <div class="col-6">
                <small><span>Model:</span></small>
            </div>
            <div class="col-6 text-right">
                <small><span class="font-weight-bold" th:text="${currentInspection.vehicleModel}">Vitz</span></small>
            </div>
            <div class="col-12">
                <hr style="margin-bottom: 8px !important; margin-top: 8px; !important;"/>
            </div>
        </div>
        <div class="row">
            <div class="col-6">
                <small><span>Color:</span></small>
            </div>
            <div class="col-6 text-right">
                <small><span class="font-weight-bold" th:text="${currentInspection.vehicleColor}">White</span></small>
            </div>
            <div class="col-12">
                <hr style="margin-bottom: 8px !important; margin-top: 8px; !important;"/>
            </div>
        </div>
        <div class="row">
            <div class="col-6">
                <small><span>Remark:</span></small>
            </div>
            <div class="col-6 text-right">
                <small><span class="font-weight-bold"
                             th:text="${currentInspection.remark}">Sample text here</span></small>
            </div>
            <div class="col-12">
                <hr style="margin-bottom: 8px !important; margin-top: 8px; !important;"/>
            </div>
        </div>
        <div class="row">
            <div class="col-6">
                <small><img src="assets/img/user.svg"
                            style="width: 10px"
                            th:src="@{/resources/assets/img/user.svg}"/></small>
            </div>
            <div class="col-6 text-right">
                <small><span class="font-weight-bold" th:text="${currentInspection.modifiedBy}"
                >Jone</span></small>
            </div>
            <div class="col-12">
                <hr style="margin-bottom: 8px !important; margin-top: 8px; !important;"/>
            </div>
        </div>
        <div class="row">
            <div class="col-6">
                <small><img
                        src="assets/img/calendar.svg"
                        style="width: 10px"
                        th:src="@{/resources/assets/img/calendar.svg}"/></small>
            </div>
            <div class="col-6 text-right">
                <small><span class="font-weight-bold"
                             th:text="${#dates.format(currentInspection.modifiedDate, 'MMMM dd, yyyy hh:mm a')}"
                >Dec
                                                                                05,2020 | 9.59PM</span></small>
            </div>

        </div>

    </div>
</div>


