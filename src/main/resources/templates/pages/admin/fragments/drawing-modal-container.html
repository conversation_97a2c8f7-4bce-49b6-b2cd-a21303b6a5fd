<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<th:block th:fragment="drawingModalContainer">
<div id="drawingModal" class="modal drawing-pad-modal" style="padding-top: 0 !important;">
    <div class="modal-dialog" style="max-width: 850px !important; position: absolute;  right: 32px; top: 0px;">

        <div class="modal-content drawing-pad-modal-content" style="margin-bottom: 32px;     min-width: 688px;">
            <div class="row" style="margin: 22px 8px 0 8px;">
                <div class="col-12">
                    <select class="form-select" aria-label="Select Document Type" id="documentType">
                        <option selected disabled value="0">Select Document Type</option>
                        <option th:each="type : ${agentVideoForm.documnetTypeList}"
                                th:value="${type.value}"
                                th:text="${type.label}">
                        </option>
                    </select>
                </div>
            </div>
                    <span class="" onclick="closeDrawingModal()" style="
                            position: absolute;
                            right: 4px;
                            z-index: 1;
                            top: 4px;
                            width: 16px;
                            height: 16px;
                            display: flex;
                            align-content: center;
                            justify-content: center;
                            align-items: center;
                            cursor: pointer;
                            border-radius: 4px;
                                color: red;
                        ">
                        <i class="fa-solid fa-square-xmark"></i>
                    </span>
            <div class="modal-body" style="padding: 22px;">
                <div class="row">
                    <div class="col-lg-12">
                        <div class="toolbar" id="toolbar">
                            <button id="freehandButton" class="btn btn-light tool-button selected" title="Freehand">
                                <i class="fa fa-pencil-alt"></i>
                            </button>
                            <button id="lineButton" class="btn btn-light tool-button" title="Line">
                                <i class="fa fa-minus"></i>
                            </button>
                            <button id="rectangleButton" class="btn btn-light tool-button" title="Rectangle">
                                <i class="fa fa-square"></i>
                            </button>
                            <button id="circleButton" class="btn btn-light tool-button" title="Circle">
                                <i class="fa fa-circle"></i>
                            </button>
                            <!-- <button id="commentButton" class="btn btn-light tool-button" title="Comment">
                                 <i class="fa fa-comment"></i>
                             </button>-->
                            <input type="color" id="colorPicker" class="btn btn-light tool-color" title="Choose Color" value="#ff0000">

                            <button id="highlightButton" class="btn btn-light tool-button" title="Highlight">
                                <i class="fa fa-highlighter"></i>
                            </button>
                        </div>
                        <canvas id="drawingCanvas" width="600" height="460"></canvas>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <input type="text" class="form-control mb-3" style="width: 100%;" id="commentInput" data-toggle="tooltip" title="Enter your comment in the input box and click outside the box to place the comment on the image." data-placement="left" placeholder="Type your comment here">
                <!-- Undo and Redo buttons -->
                <button id="undoButton" class="btn btn-outline-secondary" title="Undo"><i class="fa-solid fa-rotate-left"></i></button>
                <button id="redoButton" class="btn btn-outline-secondary" title="Redo"><i class="fa-solid fa-rotate-right"></i></button>
                <button id="clearButton" class="btn btn-secondary">Clear</button>
                <button id="uploadButton" class="btn btn-primary">Update</button>

            </div>
        </div>
    </div>
</div>
</th:block>
</html>
