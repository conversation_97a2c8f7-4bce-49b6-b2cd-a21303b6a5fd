<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<th:block th:fragment="videoButtonPanelContainer">
    <div class="admin-floating-action-buttons" id="remoteVideoButtonPanel">
        <div class="buttons-row" style="display: flex; flex-direction: row;">
            <button class="btn btn-secondary" id="btnZoomIn"
                    data-bs-toggle="tooltip"
                    data-bs-title="Zoom In"
                    data-bs-placement="top"
                    data-toggle="tooltip"
                    type="button">
                <!--                <img alt="" id="ZoomInImg" style="width: 16px; height: 16px;"-->
                <!--                     th:src="@{/resources/assets/img/magnifying-glass-plus-solid.svg}">-->
                <i class="fa-solid fa-magnifying-glass-plus" id="ZoomInImg" style="width: 16px; height: 16px;" aria-hidden="true"></i>
            </button>
            <button class="btn btn-secondary" id="btnZoomOut"
                    data-bs-toggle="tooltip"
                    data-bs-title="Zoom Out"
                    data-bs-placement="top"
                    data-toggle="tooltip"
                    type="button">
                <!--                <img alt="" id="ZoomOutImg" style="width: 16px; height: 16px;"-->
                <!--                     th:src="@{/resources/assets/img/magnifying-glass-minus-solid.svg}">-->
                <i class="fa-solid fa-magnifying-glass-minus" id="ZoomOutImg" style="width: 16px; height: 16px;"></i>
            </button>
            <button class="btn btn-secondary switch-camera" id="btnSwitchCamera"
                    data-bs-toggle="tooltip"
                    data-bs-title="Switch Camera"
                    data-bs-placement="top"
                    data-toggle="tooltip"
                    type="button">
                <i class="fa-solid fa-camera-rotate" id="cameraRotateImg" style="width: 18px; height: 18px;"></i>
                <!--                <img alt="" id="cameraRotateImg" style="width: 16px; height: 16px;"-->
                <!--                     th:src="@{/resources/assets/img/camera-rotate-solid.svg}">-->
            </button>
            <button class="btn btn-secondary torch-on" id="btnTorch"
                    data-bs-toggle="tooltip"
                    data-bs-title="Turn On Flash"
                    data-bs-placement="top"
                    data-toggle="tooltip"
                    type="button">
                <!--                <i class="fa fa-bolt" aria-hidden="true"></i>-->
                <i class="fa-solid fa-bolt-lightning" style="width: 16px; height: 16px;"></i>
            </button>



            <div  class="remote-video-btns-col">
                <button class="btn btn-secondary on-mute"
                        data-bs-toggle="tooltip" data-bs-placement="top"
                        data-bs-title="Mute"
                        id="btnOnMute"
                >
                    <i class="fa-solid fa-microphone-lines" style="width: 16px; height: 16px;"></i>
                </button>
                <button class="btn btn-primary"
                        data-bs-toggle="tooltip"
                        data-bs-title="Take Photo"
                        data-bs-placement="top"
                        data-toggle="tooltip" id="btnTakePhoto"
                >
                    <i class="fas fa-camera" style="width: 16px; height: 16px;"></i>
                </button>
                <button class="btn btn-success on-record"
                        data-bs-toggle="tooltip" data-bs-placement="top"
                        data-bs-title="Start Recording" id="btnOnRecord"
                >
                    <i class="fa-solid fa-video" style="width: 16px; height: 16px;"></i>
                </button>

                <button class="btn btn-danger on-hangup"
                        data-bs-toggle="tooltip" data-bs-placement="top"
                        data-bs-title="End Session"
                        id="btnOnHangUp"
                >
                    <i class="fas fa-phone" style="transform: rotate(135deg); width: 16px; height: 16px; margin-top: 2px;"></i>
                </button>
            </div>

        </div>
    </div>
</th:block>
</html>
