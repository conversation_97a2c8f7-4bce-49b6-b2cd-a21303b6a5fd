<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script src="jquery/src/jquery.js" th:src="@{/js/jquery/jquery.js}"></script>
<th:block th:fragment="mainContainer">

    <input type="hidden" name="jobRef" id="jobRef" th:value="${agentVideoForm.callDetailsDto.inspectionTypeId}" />
    <input type="hidden" name="claimNo" id="claimNo" th:value="${claimDetail.claimNo}" />
    <input type="hidden" name="userId" id="userId" th:value="${userDto.userInfo.claims.preferred_username}" />


    <div class="on-site-main " id="content">


        <!-- Begin Page Content -->
        <div class="container-fluid">


            <!-- Page Heading -->

            <!-- Content Row -->

            <div class="row " style="margin-top: 92px;">

                <!-- Left Camera-->
                <div class=" col-xl-5 mb-3 col-lg-5  " style="position: fixed;">
                    <div class="card  shadow-sm mb-3 h-100" style="min-height: calc(100vh - 120px);">
                        <div class="card-body ">
                            <div class="row">
                                <!-- Direct Jobs -->
                                <div class=" col-xl-12 col-lg-12 ">
                                    <!--  Counter  -->
                                    <div class="on-mi-alert-show-counter" id="recordingTimerContainer"
                                         style="display: none">
                                        <div class="toast bg-dark" data-autohide="false">
                                            <div class="toast-body text-white">
                                                Recording: <span id="hour">00</span> :
                                                <span id="min">00</span> :
                                                <span id="sec">00</span> :
                                                <span id="milisec">00</span>
                                                <span style="float: right;"><div class="blob red"></div></span>
                                            </div>
                                        </div>
                                    </div>
                                    <!--   Counter -->
                                    <canvas id="canvas" style="display: none"></canvas>
                                    <div class="d-flex justify-content-center" id="remoteVideoContainer">
                                        <!--   Toast Alert -->
                                        <div class="on-mi-alert-show" id="toastContainer"></div>
                                        <!--   Toast Alert -->
                                        <div id="participants" style="width: 100%;height: 100%;"></div>

                                    </div>
                                    <!-- Connecting to Remote Camera-->
                                    <div class="d-flex justify-content-center p-5 mt-5 " id="remoteVideoLoader">
                                        <div class="d-flex flex-row">
                                            <div class="on-loader text-center">
                                                <div class="spinner-border text-primary"
                                                     role="status" style="width: 3rem; height: 3rem;">
                                                    <span class="sr-only"></span>
                                                </div>
                                                <div class="mt-3 text-primary" id="remoteVideoLoaderText"><P>
                                                    Connecting to Remote Camera...</P></div>

                                            </div>
                                        </div>
                                    </div>
                                    <!--           End of Connecting to Remote Camera-->

                                </div>
                            </div>


                        </div>
                    </div>
                </div>


                <!-- Modal -->
                <div class="modal fade" id="pdfModal" tabindex="-1" role="dialog" aria-labelledby="pdfModalLabel"
                     aria-hidden="true">
                    <div class="modal-dialog" role="document">
                        <div class="modal-content" style="height: 90vh;">
                            <div class="modal-header">
                                <h5 class="modal-title" id="pdfModalLabel">PDF Viewer</h5>
                                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                    <span aria-hidden="true">&times;</span>
                                </button>
                            </div>
                            <div class="modal-body" style="height: 100%;">
                                <!-- PDF content will be loaded here -->
                            </div>
                        </div>
                    </div>
                </div>


                <div class="col-xl-7 col-lg-7  mb-3 " style="margin-left: 41.66667%;">
                    <div class="card shadow-sm" style="height: calc(100vh - 120px);">
                        <div class="card-body " style="padding-top: 12px !important; overflow-y: auto;">
                            <!--   Link share-->
                            <div class="d-flex" style="justify-content: flex-end;">
                                <!--                                    <button type="button" onclick="generatePDf()" id="btnPdf">PDF </button>-->
                                <div class="d-flex">
                                    <div class="btn-group">

                                        <a class="btn btn-primary btn-sm" target="_blank"
                                           th:href="${agentVideoForm.getCallDetailsDto().getLink()}">Share Joining
                                            Link</a>
                                        <button class="btn btn-success btn-sm btn-copy js-tooltip js-copy"
                                                data-bs-toggle="tooltip" data-bs-placement="bottom"
                                                data-bs-title="Copy to clipboard"
                                                th:data-copy="${agentVideoForm.getCallDetailsDto().getLink()}"
                                                type="button">
                                            <i class="fa-solid fa-copy"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                            <!--   Link share-->

                            <!--                                <div class="mt-2">-->
                            <!--                                    <div class="row">-->
                            <!--                                        <div class="col-12">-->
                            <!--                                            <div class="card bg-light">-->
                            <!--                                                <div class="card-body">-->
                            <!--                                                    <iframe-->
                            <!--                                                            allowfullscreen="" aria-hidden="false"-->
                            <!--                                                            frameborder="0" height="200" id="mapIframeLeftSide"-->
                            <!--                                                            style="border:0;"-->
                            <!--                                                            tabindex="0"-->
                            <!--                                                            width="100%"></iframe>-->
                            <!--                                                </div>-->
                            <!--                                            </div>-->
                            <!--                                        </div>-->
                            <!--                                    </div>-->
                            <!--                                </div>-->
                            <div class="mt-2">

                                <div class="">
                                    <div class="mt-0">
                                        <div class="accordion" id="accordionExample">
                                            <!--                                                <div class="card">-->
                                            <!--                                                    <div class="card-header" id="headingZero">-->
                                            <!--                                                        <p class="mb-0">-->
                                            <!--                                                            <button aria-controls="collapseZero"-->
                                            <!--                                                                    aria-expanded="true"-->
                                            <!--                                                                    class="btn btn-sm btn-link btn-block text-left"-->
                                            <!--                                                                    data-bs-target="#collapseZero" data-bs-toggle="collapse"-->
                                            <!--                                                                    type="button" style="font-weight: 600;">-->
                                            <!--                                                                Current Inspection Details-->
                                            <!--                                                            </button>-->
                                            <!--                                                        </p>-->
                                            <!--                                                    </div>-->

                                            <!--                                                    <div aria-labelledby="headingZero" class="collapse"-->
                                            <!--                                                         data-parent="#accordionExample"-->
                                            <!--                                                         id="collapseZero">-->
                                            <!--                                                        <div class="card-body"-->
                                            <!--                                                             style="overflow-y: scroll; max-height: 400px;">-->
                                            <!--                                                            <div id="div-current-inspection"></div>-->
                                            <!--                                                        </div>-->
                                            <!--                                                    </div>-->
                                            <!--                                                </div>-->

                                            <!--                                                <div class="card">-->
                                            <!--                                                    <div class="card-header " id="headingPrevInsDetails">-->
                                            <!--                                                        <p class="mb-0">-->
                                            <!--                                                            <button aria-controls="collapsePrevInsDetails"-->
                                            <!--                                                                    aria-expanded="false"-->
                                            <!--                                                                    class="btn btn-sm btn-link btn-block text-left collapsed"-->
                                            <!--                                                                    data-bs-target="#collapsePrevInsDetails"-->
                                            <!--                                                                    data-bs-toggle="collapse"-->
                                            <!--                                                                    type="button" style="font-weight: 600;">-->
                                            <!--                                                                Previous Inspection Details-->
                                            <!--                                                            </button>-->
                                            <!--                                                        </p>-->

                                            <!--                                                    </div>-->
                                            <!--                                                    <div aria-labelledby="headingPrevInsDetails" class="collapse"-->
                                            <!--                                                         data-parent="#accordionExample"-->
                                            <!--                                                         id="collapsePrevInsDetails">-->
                                            <!--                                                        <div class="card-body"-->
                                            <!--                                                             style="overflow-y: scroll; max-height: 400px;">-->
                                            <!--                                                            <div id="div-previous-inspection-list"></div>-->
                                            <!--                                                        </div>-->
                                            <!--                                                    </div>-->


                                            <!--                                                </div>-->
                                            <div class="card">
                                                <div class="card-header" id="headingCustomer">
                                                    <p class="mb-0">
                                                        <button aria-controls="collapseCustomer"
                                                                aria-expanded="true"
                                                                class="btn btn-sm btn-link btn-block text-left"
                                                                data-bs-target="#collapseCustomer"
                                                                data-bs-toggle="collapse"
                                                                type="button" style="font-weight: 600;">
                                                            Underwriting Details
                                                        </button>
                                                    </p>

                                                </div>

                                                <div aria-labelledby="headingCustomer" class="collapse"
                                                     data-parent="#accordionExample"
                                                     id="collapseCustomer">
                                                    <div class="card-body">
                                                        <div class="card">
                                                            <div class="card-header px-2 py-1 sub-card-header-uw">
                                                                Customer Details
                                                            </div>
                                                            <div class="card-body p-2">
                                                                <div class="data-row-div"
                                                                     style="display: flex !important;flex-wrap: wrap;">

                                                                    <div class="data-container-div col-6">
                                                                            <span class="data-label-span">
                                                                                Customer Name
                                                                            </span>
                                                                        <span class="data-details-span"
                                                                              th:text="${claimDetail.policyDto.customerName}"></span>
                                                                    </div>

                                                                    <div class="data-container-div col-6">
                                                                            <span class="data-label-span">
                                                                                NIC No.
                                                                            </span>
                                                                        <span class="data-details-span"
                                                                              th:text="${claimDetail.policyDto.customerNic}">
                                                                            </span>
                                                                    </div>


                                                                    <div class="data-container-div col-6">
                                                                            <span class="data-label-span">
                                                                                Address
                                                                            </span>
                                                                        <span class="data-details-span"
                                                                              th:text="${claimDetail.policyDto.customerAddressLine1 + ' ' + claimDetail.policyDto.customerAddressLine2 + ' ' + claimDetail.policyDto.customerAddressLine3}">
                                                                            </span>
                                                                    </div>

                                                                    <div class="data-container-div col-6">
                                                                            <span class="data-label-span">
                                                                                Contact No.
                                                                            </span>
                                                                        <span class="data-details-span"
                                                                              th:text="${claimDetail.policyDto.customerMobileNo}">
                                                                            </span>
                                                                    </div>
                                                                </div>

                                                            </div>
                                                        </div>
                                                        <div class="card mt-3">
                                                            <div class="card-header px-2 py-1 sub-card-header-uw">
                                                                Vehicle Details
                                                            </div>
                                                            <div class="card-body p-2">
                                                            <div class="data-row-div" style="display: flex !important; flex-wrap: wrap;">

                                                                <div class="data-container-div col-6">
                                                                    <span class="data-label-span">Vehicle No</span>
                                                                    <span class="data-details-span" th:text="${claimDetail.policyDto.vehicleNumber}"></span>
                                                                </div>

                                                                <div class="data-container-div col-6">
                                                                    <span class="data-label-span">Policy Number</span>
                                                                    <span class="data-details-span" th:text="${claimDetail.policyDto.policyNumber}"></span>
                                                                </div>

                                                                <div class="data-container-div col-6">
                                                                    <span class="data-label-span">Model Code</span>
                                                                    <span class="data-details-span" th:text="${claimDetail.policyDto.vehicleModel}"></span>
                                                                </div>

                                                                <div class="data-container-div col-6">
                                                                    <span class="data-label-span">Make Code</span>
                                                                    <span class="data-details-span" th:text="${claimDetail.policyDto.vehicleMake}"></span>
                                                                </div>

                                                                <div class="data-container-div col-6">
                                                                    <span class="data-label-span">Vehicle Color</span>
                                                                    <span class="data-details-span" th:text="${claimDetail.policyDto.vehicleColor}"></span>
                                                                </div>

                                                                <div class="data-container-div col-6">
                                                                    <span class="data-label-span">Engine Number</span>
                                                                    <span class="data-details-span" th:text="${claimDetail.policyDto.engineNo}"></span>
                                                                </div>

                                                                <div class="data-container-div col-6">
                                                                    <span class="data-label-span">Chassis Number</span>
                                                                    <span class="data-details-span" th:text="${claimDetail.policyDto.chassisNo}"></span>
                                                                </div>

                                                                <div class="data-container-div col-6">
                                                                    <span class="data-label-span">Cover Note No</span>
                                                                    <span class="data-details-span" th:text="${claimDetail.policyDto.coverNoteNo}"></span>
                                                                </div>

                                                                <div class="data-container-div col-6">
                                                                    <span class="data-label-span">Body Type</span>
                                                                    <span class="data-details-span" th:text="${claimDetail.policyDto.bodyType}"></span>
                                                                </div>

                                                                <div class="data-container-div col-6">
                                                                    <span class="data-label-span">Fuel Type</span>
                                                                    <span class="data-details-span" th:text="${claimDetail.policyDto.fuelType}"></span>
                                                                </div>

                                                                <div class="data-container-div col-6">
                                                                    <span class="data-label-span">No of Seats</span>
                                                                    <span class="data-details-span" th:text="${claimDetail.policyDto.noOfSeat}"></span>
                                                                </div>

                                                                <div class="data-container-div col-6">
                                                                    <span class="data-label-span">Capacity Cylinder</span>
                                                                    <span class="data-details-span" th:text="${claimDetail.policyDto.engineCapacity}"></span>
                                                                </div>

                                                            </div>
                                                            </div>
                                                        </div>
                                                        <div class="card mt-3">
                                                            <div class="card-header px-2 py-1 sub-card-header-uw">
                                                                Policy Details
                                                            </div>
                                                            <div class="card-body p-2">
                                                                <div class="data-row-div"
                                                                     style="display: flex !important;flex-wrap: wrap;">

                                                                    <div class="data-container-div col-6">
                                                                        <span class="data-label-span">Policy Type</span>
                                                                        <span class="data-details-span"
                                                                              th:text="${claimDetail.policyDto.policyType}"></span>
                                                                    </div>

                                                                    <div class="data-container-div col-6">
                                                                        <span class="data-label-span">Usage</span>
                                                                        <span class="data-details-span"
                                                                              th:text="${claimDetail.policyDto.vehicleUsage}"></span>
                                                                    </div>

                                                                    <div class="data-container-div col-6">
                                                                        <span class="data-label-span">Cover Type</span>
                                                                        <span class="data-details-span"
                                                                              th:text="${claimDetail.policyDto.coverType}"></span>
                                                                    </div>

                                                                    <div class="data-container-div col-6">
                                                                        <span class="data-label-span">Sum Insured (Rs.)</span>
                                                                        <span class="data-details-span"
                                                                              th:text="${claimDetail.policyDto.sumInsured}"></span>
                                                                    </div>

                                                                    <div class="data-container-div col-6">
                                                                        <span class="data-label-span">Manufacture Year</span>
                                                                        <span class="data-details-span"
                                                                              th:text="${claimDetail.policyDto.manufactureYear}"></span>
                                                                    </div>

                                                                    <div class="data-container-div col-6">
                                                                        <span class="data-label-span">Vehicle Age</span>
                                                                        <span class="data-details-span"
                                                                              th:text="${claimDetail.policyDto.vehicleAge}"></span>
                                                                    </div>

                                                                    <div class="data-container-div col-6">
                                                                        <span class="data-label-span">Annual Premium (Rs.)</span>
                                                                        <span class="data-details-span"
                                                                              th:text="${claimDetail.policyDto.annualPremium}"></span>
                                                                    </div>

                                                                    <div class="data-container-div col-6">
                                                                        <span class="data-label-span">Outstanding Premium (Rs.)</span>
                                                                        <span class="data-details-span"
                                                                              th:text="${claimDetail.policyDto.totalPremiumOutstanding}"></span>

                                                                    </div>


                                                                    <div class="data-container-div col-6">
                                                                        <span class="data-label-span">Policy Status</span>
                                                                        <span class="data-details-span"
                                                                              th:text="${claimDetail.policyDto.policyStatus}"></span>
                                                                    </div>

                                                                    <div class="data-container-div col-6">
                                                                        <span class="data-label-span">NCB Rate</span>
                                                                        <span class="data-details-span"
                                                                              th:text="${claimDetail.policyDto.ncbRate}"></span>
                                                                    </div>



                                                                   <div class="data-container-div col-6"><span
                                                                           class="data-label-span">Policy Ref No</span><span
                                                                           class="data-details-span"
                                                                           th:text="${claimDetail.policyDto.policyRefNo}"></span>
                                                                    </div>
                                                                    <div class="data-container-div col-6"><span
                                                                            class="data-label-span">Renewal Count</span><span
                                                                            class="data-details-span"
                                                                            th:text="${claimDetail.policyDto.renewalCount}"></span>
                                                                    </div>
                                                                    <div class="data-container-div col-6"><span
                                                                            class="data-label-span">Endorsement Count</span><span
                                                                            class="data-details-span"
                                                                            th:text="${claimDetail.policyDto.endorsementCount}"></span>
                                                                    </div>
                                                                    <div class="data-container-div col-6"><span
                                                                            class="data-label-span">Expire Date</span><span
                                                                            class="data-details-span"
                                                                            th:text="${claimDetail.policyDto.expireDate}"></span>
                                                                    </div>
                                                                    <div class="data-container-div col-6"><span
                                                                            class="data-label-span">Inspection Date</span><span
                                                                            class="data-details-span"
                                                                            th:text="${claimDetail.policyDto.inspectionDate}"></span>
                                                                    </div>
                                                                    <div class="data-container-div col-6"><span
                                                                            class="data-label-span">Original Inspection Date</span><span
                                                                            class="data-details-span"
                                                                            th:text="${claimDetail.policyDto.originalInspectionDate}"></span>
                                                                    </div>
                                                                    <div class="data-container-div col-6"><span
                                                                            class="data-label-span">Customer Landline</span><span
                                                                            class="data-details-span"
                                                                            th:text="${claimDetail.policyDto.customerLandNo}"></span>
                                                                    </div>
                                                                    <div class="data-container-div col-6"><span
                                                                            class="data-label-span">Latest Claim Intimation</span><span
                                                                            class="data-details-span"
                                                                            th:text="${claimDetail.policyDto.latestClaimIntimationDate}"></span>
                                                                    </div>
                                                                    <div class="data-container-div col-6"><span
                                                                            class="data-label-span">Latest Claim Loss</span><span
                                                                            class="data-details-span"
                                                                            th:text="${claimDetail.policyDto.latestClaimLossDate}"></span>
                                                                    </div>
                                                                    <div class="data-container-div col-6"><span
                                                                            class="data-label-span">Create User</span><span
                                                                            class="data-details-span"
                                                                            th:text="${claimDetail.policyDto.createUser}"></span>
                                                                    </div>
                                                                    <div class="data-container-div col-6"><span
                                                                            class="data-label-span">Create Date</span><span
                                                                            class="data-details-span"
                                                                            th:text="${claimDetail.policyDto.createDate}"></span>
                                                                    </div>
                                                                    <div class="data-container-div col-6"><span
                                                                            class="data-label-span">Register Date</span><span
                                                                            class="data-details-span"
                                                                            th:text="${claimDetail.policyDto.registerDate}"></span>
                                                                    </div>
                                                                    <div class="data-container-div col-6"><span
                                                                            class="data-label-span">Workflow</span><span
                                                                            class="data-details-span"
                                                                            th:text="${claimDetail.policyDto.workflow}"></span>
                                                                    </div>
                                                                    <div class="data-container-div col-6"><span
                                                                            class="data-label-span">Category Description</span><span
                                                                            class="data-details-span"
                                                                            th:text="${claimDetail.policyDto.categoryDesc}"></span>
                                                                    </div>



                                                                    <div class="data-container-div col-6"><span
                                                                            class="data-label-span">Claim No</span><span
                                                                            class="data-details-span"
                                                                            th:text="${claimDetail.claimNo}" ></span>
                                                                    </div>
                                                                    <div class="data-container-div col-6"><span
                                                                            class="data-label-span">Intimation No</span><span
                                                                            class="data-details-span"
                                                                            th:text="${claimDetail.intimationNo}"></span>
                                                                    </div>
                                                                    <div class="data-container-div col-6"><span
                                                                            class="data-label-span">ISF Claim No</span><span
                                                                            class="data-details-span"
                                                                            th:text="${claimDetail.isfClaimNo}"></span>
                                                                    </div>
                                                                    <div class="data-container-div col-6"><span
                                                                            class="data-label-span">Policy Branch</span><span
                                                                            class="data-details-span"
                                                                            th:text="${claimDetail.policyBranch}"></span>
                                                                    </div>
                                                                    <div class="data-container-div col-6"><span
                                                                            class="data-label-span">Reporter Name</span><span
                                                                            class="data-details-span"
                                                                            th:text="${claimDetail.reporterName}"></span>
                                                                    </div>
                                                                    <div class="data-container-div col-6"><span
                                                                            class="data-label-span">Reporter ID</span><span
                                                                            class="data-details-span"
                                                                            th:text="${claimDetail.reporterId}"></span>
                                                                    </div>
                                                                    <div class="data-container-div col-6"><span
                                                                            class="data-label-span">Insured Mobile No</span><span
                                                                            class="data-details-span"
                                                                            th:text="${claimDetail.insurdMobNo}"></span>
                                                                    </div>
                                                                    <div class="data-container-div col-6"><span
                                                                            class="data-label-span">Accident Date</span><span
                                                                            class="data-details-span"
                                                                            th:text="${claimDetail.accidDate}"></span>
                                                                    </div>
                                                                    <div class="data-container-div col-6"><span
                                                                            class="data-label-span">Accident Time</span><span
                                                                            class="data-details-span"
                                                                            th:text="${claimDetail.accidTime}"></span>
                                                                    </div>
                                                                    <div class="data-container-div col-6"><span
                                                                            class="data-label-span">Date of Report</span><span
                                                                            class="data-details-span"
                                                                            th:text="${claimDetail.dateOfReport}"></span>
                                                                    </div>
                                                                    <div class="data-container-div col-6"><span
                                                                            class="data-label-span">Time of Report</span><span
                                                                            class="data-details-span"
                                                                            th:text="${claimDetail.timeOfReport}"></span>
                                                                    </div>
                                                                    <div class="data-container-div col-6"><span
                                                                            class="data-label-span">Place of Accident</span><span
                                                                            class="data-details-span"
                                                                            th:text="${claimDetail.placeOfAccid}"></span>
                                                                    </div>
                                                                    <div class="data-container-div col-6"><span
                                                                            class="data-label-span">Accident Description</span><span
                                                                            class="data-details-span"
                                                                            th:text="${claimDetail.accidDesc}"></span>
                                                                    </div>
                                                                    <div class="data-container-div col-6"><span
                                                                            class="data-label-span">Current Location</span><span
                                                                            class="data-details-span"
                                                                            th:text="${claimDetail.currentLocation}"></span>
                                                                    </div>
                                                                    <div class="data-container-div col-6"><span
                                                                            class="data-label-span">Driver Name</span><span
                                                                            class="data-details-span"
                                                                            th:text="${claimDetail.driverName}"></span>
                                                                    </div>
                                                                    <div class="data-container-div col-6"><span
                                                                            class="data-label-span">Driver NIC</span><span
                                                                            class="data-details-span"
                                                                            th:text="${claimDetail.driverNic}"></span>
                                                                    </div>
                                                                    <div class="data-container-div col-6"><span
                                                                            class="data-label-span">Driver License No</span><span
                                                                            class="data-details-span"
                                                                            th:text="${claimDetail.dlNo}"></span></div>
                                                                    <div class="data-container-div col-6"><span
                                                                            class="data-label-span">Driver License Type</span><span
                                                                            class="data-details-span"
                                                                            th:text="${claimDetail.driverLicenceType}"></span>
                                                                    </div>
                                                                    <div class="data-container-div col-6"><span
                                                                            class="data-label-span">Damage Remark</span><span
                                                                            class="data-details-span"
                                                                            th:text="${claimDetail.damageRemark}"></span>
                                                                    </div>
                                                                    <div class="data-container-div col-6"><span
                                                                            class="data-label-span">Draft Remark</span><span
                                                                            class="data-details-span"
                                                                            th:text="${claimDetail.draftRemark}"></span>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>

                                            <div class="card">
                                                <div class="card-header" id="headingInspectionReportDetails">
                                                    <p class="mb-0">
                                                        <button aria-controls="collapseInspectionReportDetails"
                                                                aria-expanded="true"
                                                                class="btn btn-sm btn-link btn-block text-left"
                                                                data-bs-target="#collapseInspectionReportDetails"
                                                                data-bs-toggle="collapse"
                                                                type="button" style="font-weight: 600;">
                                                            Inspection Report Details
                                                        </button>
                                                    </p>

                                                </div>

                                                <div aria-labelledby="headingInspectionReportDetails" class="collapse"
                                                     data-parent="#accordionExample"
                                                     id="collapseInspectionReportDetails">
                                                    <div class="card-body">
                                                        <div class="data-row-div row">
                                                            <div class="data-container-div col-6">
                                                                            <span class="data-label-span">
                                                                                Job No.
                                                                            </span>
                                                                <span class="data-details-span" th:text="${claimDetail.inspectionDto.jobNo}" id="jobNumber"></span>
                                                            </div>

                                                            <div class="data-container-div col-6">
                                                                            <span class="data-label-span">
                                                                                Customer Name
                                                                            </span>
                                                                <span class="data-details-span" th:text="${claimDetail.policyDto.customerName}" id="customerName"></span>
                                                            </div>

                                                            <div class="data-container-div col-6">
                                                                            <span class="data-label-span">
                                                                                Address
                                                                            </span>
                                                                <span class="data-details-span" th:text="${claimDetail.policyDto.customerAddressLine1 +' '+ claimDetail.policyDto.customerAddressLine2+' '+claimDetail.policyDto.customerAddressLine3}"></span>
                                                            </div>

                                                            <div class="data-container-div col-6">
                                                                            <span class="data-label-span" >
                                                                                Contact No.
                                                                            </span>
                                                                <span class="data-details-span" th:text="${claimDetail.policyDto.customerMobileNo}">

                                                                            </span>
                                                            </div>

                                                            <div class="data-container-div col-6">
                                                                    <span class="data-label-span">
                                                                        Make
                                                                    </span>
                                                                <span class="data-details-span" th:text="${claimDetail.policyDto.vehicleMake}">

                                                                    </span>
                                                                <div class="inspection-data-form">
                                                                    <div class="form-check">
                                                                        <input class="form-check-input" type="radio"
                                                                               name="flexRadioDefaultMake"
                                                                               id="flexRadioDefault1" value="C">
                                                                        <label class="form-check-label"
                                                                               for="flexRadioDefault1">
                                                                            Correct
                                                                        </label>
                                                                    </div>
                                                                    <div class="form-check">
                                                                        <input class="form-check-input" type="radio"
                                                                               name="flexRadioDefaultMake"
                                                                               id="flexRadioDefault2" value="W">
                                                                        <label class="form-check-label"
                                                                               for="flexRadioDefault2">
                                                                            Wrong
                                                                        </label>
                                                                    </div>
                                                                    <div class="form-check">
                                                                        <input class="form-check-input" type="radio"
                                                                               name="flexRadioDefaultMake"
                                                                               id="flexRadioDefault3" value="NC">
                                                                        <label class="form-check-label"
                                                                               for="flexRadioDefault3">
                                                                            Not Checked
                                                                        </label>
                                                                    </div>
                                                                </div>

                                                            </div>
                                                            <div class="data-container-div col-6">
                                                                    <span class="data-label-span">
                                                                        Model
                                                                    </span>
                                                                <span class="data-details-span" th:text="${claimDetail.policyDto.vehicleModel}">

                                                                    </span>
                                                                <div class="inspection-data-form">
                                                                    <div class="form-check">
                                                                        <input class="form-check-input" type="radio"
                                                                               name="flexRadioDefaultModel"
                                                                               id="flexRadioDefault4" value="C">
                                                                        <label class="form-check-label"
                                                                               for="flexRadioDefault4">
                                                                            Correct
                                                                        </label>
                                                                    </div>
                                                                    <div class="form-check">
                                                                        <input class="form-check-input" type="radio"
                                                                               name="flexRadioDefaultModel"
                                                                               id="flexRadioDefault5" value="W">
                                                                        <label class="form-check-label"
                                                                               for="flexRadioDefault5">
                                                                            Wrong
                                                                        </label>
                                                                    </div>
                                                                    <div class="form-check">
                                                                        <input class="form-check-input" type="radio"
                                                                               name="flexRadioDefaultModel"
                                                                               id="flexRadioDefault6" value="NC">
                                                                        <label class="form-check-label"
                                                                               for="flexRadioDefault6">
                                                                            Not Checked
                                                                        </label>
                                                                    </div>
                                                                </div>

                                                            </div>
                                                            <div class="data-container-div col-6">
                                                                    <span class="data-label-span">
                                                                        Year of Make
                                                                    </span>
                                                                <span class="data-details-span" th:text="${claimDetail.policyDto.manufactureYear}">

                                                                    </span>
                                                                <div class="inspection-data-form">
                                                                    <div class="form-check">
                                                                        <input class="form-check-input" type="radio"
                                                                               name="flexRadioDefaultMYear"
                                                                               id="flexRadioDefault7" value="C">
                                                                        <label class="form-check-label"
                                                                               for="flexRadioDefault7">
                                                                            Correct
                                                                        </label>
                                                                    </div>
                                                                    <div class="form-check">
                                                                        <input class="form-check-input" type="radio"
                                                                               name="flexRadioDefaultMYear"
                                                                               id="flexRadioDefault8" value="W">
                                                                        <label class="form-check-label"
                                                                               for="flexRadioDefault8">
                                                                            Wrong
                                                                        </label>
                                                                    </div>
                                                                    <div class="form-check">
                                                                        <input class="form-check-input" type="radio"
                                                                               name="flexRadioDefaultMYear"
                                                                               id="flexRadioDefault9" value="NC">
                                                                        <label class="form-check-label"
                                                                               for="flexRadioDefault9">
                                                                            Not Checked
                                                                        </label>
                                                                    </div>
                                                                </div>

                                                            </div>
                                                            <div class="data-container-div col-6">
                                                                    <span class="data-label-span">
                                                                        Engine No.
                                                                    </span>
                                                                <span class="data-details-span" th:text="${claimDetail.policyDto.engineNo}">

                                                                    </span>
                                                                <div class="inspection-data-form">
                                                                    <div class="form-check">
                                                                        <input class="form-check-input" type="radio"
                                                                               name="flexRadioDefaultEngine"
                                                                               id="flexRadioDefault10" value="C">
                                                                        <label class="form-check-label"
                                                                               for="flexRadioDefault10">
                                                                            Correct
                                                                        </label>
                                                                    </div>
                                                                    <div class="form-check">
                                                                        <input class="form-check-input" type="radio"
                                                                               name="flexRadioDefaultEngine"
                                                                               id="flexRadioDefault11" value="W">
                                                                        <label class="form-check-label"
                                                                               for="flexRadioDefault11">
                                                                            Wrong
                                                                        </label>
                                                                    </div>
                                                                    <div class="form-check">
                                                                        <input class="form-check-input" type="radio"
                                                                               name="flexRadioDefaultEngine"
                                                                               id="flexRadioDefault12" value="NC">
                                                                        <label class="form-check-label"
                                                                               for="flexRadioDefault12">
                                                                            Not Checked
                                                                        </label>
                                                                    </div>
                                                                </div>

                                                            </div>

                                                            <div class="data-container-div col-6">
                                                                            <span class="data-label-span">
                                                                                NCB (Rs.)
                                                                            </span>
                                                                <span class="data-details-span" th:text="${claimDetail.policyDto.ncbAmount}">

                                                                            </span>
                                                            </div>
                                                            <div class="data-container-div col-6">
                                                                            <span class="data-label-span">
                                                                                NCB Percentage
                                                                            </span>
                                                                <span class="data-details-span" th:text="${claimDetail.policyDto.ncbRate}">
                                                                            </span>
                                                            </div>

                                                            <div class="data-container-div col-6">
                                                                            <span class="data-label-span">
                                                                               Date of Inspection
                                                                            </span>
                                                                <input class="form-control form-control-sm mt-1"
                                                                       type="datetime-local" id="dateOfInspection">
                                                            </div>
                                                            <div class="data-container-div col-6">
                                                                            <span class="data-label-span">
                                                                               PAV (Rs.)
                                                                            </span>
                                                                <input class="form-control form-control-sm mt-1"
                                                                       type="text" id="pav">
                                                            </div>
                                                            <div class="data-container-div col-6">
                                                                            <span class="data-label-span" id="detailsOfDamage">
                                                                               Details of Damages
                                                                            </span>
                                                                <textarea
                                                                        class="form-control form-control-sm mt-1"></textarea>
                                                            </div>
                                                            <div class="data-container-div col-6">
                                                                            <span class="data-label-span" id="pad">
                                                                              PAD
                                                                            </span>
                                                                <textarea
                                                                        class="form-control form-control-sm mt-1"></textarea>
                                                            </div>
                                                            <div class="data-container-div col-6">
                                                                            <span class="data-label-span">
                                                                              Genuineness of the Accident
                                                                            </span>
                                                                <div class="inspection-data-form">
                                                                    <div class="form-check">
                                                                        <input class="form-check-input" type="radio"
                                                                               name="flexRadioDefaultGenAcc"
                                                                               id="flexRadioDefault13" value="C">
                                                                        <label class="form-check-label"
                                                                               for="flexRadioDefault13">
                                                                            Consistent
                                                                        </label>
                                                                    </div>
                                                                    <div class="form-check">
                                                                        <input class="form-check-input" type="radio"
                                                                               name="flexRadioDefaultGenAcc"
                                                                               id="flexRadioDefault14" value="N">
                                                                        <label class="form-check-label"
                                                                               for="flexRadioDefault14">
                                                                            Non Consistent
                                                                        </label>
                                                                    </div>
                                                                    <div class="form-check">
                                                                        <input class="form-check-input" type="radio"
                                                                               name="flexRadioDefaultGenAcc"
                                                                               id="flexRadioDefault15" value="D">
                                                                        <label class="form-check-label"
                                                                               for="flexRadioDefault15">
                                                                            Doubtful
                                                                        </label>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                            <div class="data-container-div col-6">
                                                                            <span class="data-label-span">
                                                                              1st statement required for Own Damage Settlement
                                                                            </span>
                                                                <div class="inspection-data-form">
                                                                    <div class="form-check">
                                                                        <input class="form-check-input" type="radio"
                                                                               name="flexRadioDefault1st"
                                                                               id="flexRadioDefault16" value="Y">
                                                                        <label class="form-check-label"
                                                                               for="flexRadioDefault16">
                                                                            Yes
                                                                        </label>
                                                                    </div>
                                                                    <div class="form-check">
                                                                        <input class="form-check-input" type="radio"
                                                                               name="flexRadioDefault1st"
                                                                               id="flexRadioDefault17" value="N">
                                                                        <label class="form-check-label"
                                                                               for="flexRadioDefault17">
                                                                            No
                                                                        </label>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                            <div class="data-container-div col-6">
                                                                    <span class="data-label-span">
                                                                       Reason
                                                                    </span>
                                                                <select class="form-select form-control-sm mt-1" id="reason" disabled>
                                                                    <option value="0">Please Select</option>
                                                                    <option value="1">Hit and Run - Third party not identified</option>
                                                                    <option value="2">Severe Damage</option>
                                                                    <option value="3">Suspicious Incident for Fadue Claim</option>
                                                                    <option value="4">Suspected for drunken driving</option>
                                                                    <option value="5">Suspected driver substitute</option>
                                                                    <option value="6">Incident is not consistent - Contradictory Statements</option>
                                                                    <option value="7">Vehicle was moved away from the accident site</option>
                                                                    <option value="8">Fire Damage</option>
                                                                    <option value="9">Malicious Damage</option>
                                                                    <option value="10">Strike Riot and Civil Commotion</option>
                                                                    <option value="11">Fatal Incident</option>
                                                                    <option value="12">Occupants of the vehicle were Hospitalized</option>
                                                                    <option value="13">Driver is not identified</option>
                                                                    <option value="14">Third Party Damages</option>
                                                                    <option value="15">Third Party Damages for Government property</option>
                                                                    <option value="16">Late Intimation</option>
                                                                    <option value="17">Theft of Vehicle</option>
                                                                    <option value="18">Theft of Parts</option>
                                                                    <option value="19">Any Other</option>
                                                                </select>
                                                            </div>
                                                            <div class="data-container-div col-6">
                                                                            <span class="data-label-span">
                                                                             Investigation Required
                                                                            </span>
                                                                <div class="inspection-data-form">
                                                                    <div class="form-check">
                                                                        <input class="form-check-input" type="radio"
                                                                               name="flexRadioDefaultInv"
                                                                               id="flexRadioDefault18" value="Y">
                                                                        <label class="form-check-label"
                                                                               for="flexRadioDefault18">
                                                                            Yes
                                                                        </label>
                                                                    </div>
                                                                    <div class="form-check">
                                                                        <input class="form-check-input" type="radio"
                                                                               name="flexRadioDefaultInv"
                                                                               id="flexRadioDefault19" value="N">
                                                                        <label class="form-check-label"
                                                                               for="flexRadioDefault19">
                                                                            No
                                                                        </label>
                                                                    </div>
                                                                </div>
                                                            </div>

                                                            <div class="data-container-div col-6">
                                                                    <span class="data-label-span">
                                                                      RTE Remarks
                                                                    </span>
                                                                <textarea
                                                                        class="form-control form-control-sm mt-1" id="RteRemark" disabled></textarea>
                                                            </div>
                                                            <div class="data-container-div col-6">
                                                                    <span class="data-label-span">
                                                                      Online Assessment Remarks
                                                                    </span>
                                                                <textarea
                                                                        class="form-control form-control-sm mt-1" id="onlineAssesmentRemark"></textarea>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="card-footer"
                                                         style="display: flex; justify-content: flex-end; align-items: center;  background: #ffffff; border-top: none;">
                                                        <button type="button" class="btn btn-primary btn-sm"
                                                                style="width: 92px;" id="inspectionDetailSaveBtn">Save
                                                        </button>
                                                    </div>
                                                </div>
                                            </div>

                                            <div class="card">
                                                <div class="card-header" id="headingTyres">
                                                    <p class="mb-0">
                                                        <button aria-controls="collapseTyres"
                                                                aria-expanded="true"
                                                                class="btn btn-sm btn-link btn-block text-left"
                                                                data-bs-target="#collapseTyres"
                                                                data-bs-toggle="collapse"
                                                                type="button" style="font-weight: 600;">
                                                            Condition of Tyres
                                                        </button>
                                                    </p>

                                                </div>

                                                <div aria-labelledby="headingTyres" class="collapse"
                                                     data-parent="#accordionExample"
                                                     id="collapseTyres">
                                                    <div class="card-body">
                                                        <table class="table align-middle tyre-condition-table"
                                                               style="border: 1px solid #e3e6f0 !important;">
                                                            <thead>
                                                            <tr>
                                                                <th colspan="8"
                                                                    style="border-bottom: 1px solid #e3e6f0 !important; vertical-align: middle;">
                                                                    Mortor Engineering
                                                                </th>
                                                            </tr>
                                                            </thead>
                                                            <thead>
                                                            <tr>
                                                                <th style="border: none; vertical-align: middle;">
                                                                    Position
                                                                </th>
                                                                <th style="text-align: center; border: none; vertical-align: middle;">
                                                                    RF
                                                                </th>
                                                                <th style="text-align: center; border: none; vertical-align: middle;">
                                                                    LF
                                                                </th>
                                                                <th style="text-align: center; border: none; vertical-align: middle;">
                                                                    RR
                                                                </th>
                                                                <th style="text-align: center; border: none; vertical-align: middle;">
                                                                    RL
                                                                </th>
                                                                <th style="text-align: center; border: none; vertical-align: middle;">
                                                                    RRI
                                                                </th>
                                                                <th style="text-align: center; border: none; vertical-align: middle;">
                                                                    LRI
                                                                </th>
                                                                <th style="text-align: center; border: none; vertical-align: middle;">
                                                                    Other
                                                                </th>
                                                            </tr>
                                                            </thead>
                                                            <tbody>
                                                            <tr>
                                                                <th style="border: none; vertical-align: middle;">Condition</th>
                                                                <td style="border: none;">
                                                                    <select class="form-select form-select-sm" data-type="condition" data-wheel="rf">
                                                                        <option th:value="'N/A'" th:selected="${claimDetail.tireConditionMeList[0].rightFront == 'N/A'}">N/A</option>
                                                                        <option th:value="'Good'" th:selected="${claimDetail.tireConditionMeList[0].rightFront == 'Good'}">Good</option>
                                                                        <option th:value="'Fair'" th:selected="${claimDetail.tireConditionMeList[0].rightFront == 'Fair'}">Fair</option>
                                                                        <option th:value="'Bald'" th:selected="${claimDetail.tireConditionMeList[0].rightFront == 'Bald'}">Bald</option>
                                                                    </select>
                                                                </td>
                                                                <td style="border: none;">
                                                                    <select class="form-select form-select-sm" data-type="condition" data-wheel="lf">
                                                                        <option th:value="'N/A'" th:selected="${claimDetail.tireConditionMeList[0].leftFront == 'N/A'}">N/A</option>
                                                                        <option th:value="'Good'" th:selected="${claimDetail.tireConditionMeList[0].leftFront == 'Good'}">Good</option>
                                                                        <option th:value="'Fair'" th:selected="${claimDetail.tireConditionMeList[0].leftFront == 'Fair'}">Fair</option>
                                                                        <option th:value="'Bald'" th:selected="${claimDetail.tireConditionMeList[0].leftFront == 'Bald'}">Bald</option>
                                                                    </select>
                                                                </td>
                                                                <td style="border: none;">
                                                                    <select class="form-select form-select-sm" data-type="condition" data-wheel="rr">
                                                                        <option th:value="'N/A'" th:selected="${claimDetail.tireConditionMeList[0].rightRear == 'N/A'}">N/A</option>
                                                                        <option th:value="'Good'" th:selected="${claimDetail.tireConditionMeList[0].rightRear == 'Good'}">Good</option>
                                                                        <option th:value="'Fair'" th:selected="${claimDetail.tireConditionMeList[0].rightRear == 'Fair'}">Fair</option>
                                                                        <option th:value="'Bald'" th:selected="${claimDetail.tireConditionMeList[0].rightRear == 'Bald'}">Bald</option>
                                                                    </select>
                                                                </td>
                                                                <td style="border: none;">
                                                                    <select class="form-select form-select-sm" data-type="condition" data-wheel="rl">
                                                                        <option th:value="'N/A'" th:selected="${claimDetail.tireConditionMeList[0].leftRear == 'N/A'}">N/A</option>
                                                                        <option th:value="'Good'" th:selected="${claimDetail.tireConditionMeList[0].leftRear == 'Good'}">Good</option>
                                                                        <option th:value="'Fair'" th:selected="${claimDetail.tireConditionMeList[0].leftRear == 'Fair'}">Fair</option>
                                                                        <option th:value="'Bald'" th:selected="${claimDetail.tireConditionMeList[0].leftRear == 'Bald'}">Bald</option>
                                                                    </select>
                                                                </td>
                                                                <td style="border: none;">
                                                                    <select class="form-select form-select-sm" data-type="condition" data-wheel="rri">
                                                                        <option th:value="'N/A'" th:selected="${claimDetail.tireConditionMeList[0].rightRearInner == 'N/A'}">N/A</option>
                                                                        <option th:value="'Good'" th:selected="${claimDetail.tireConditionMeList[0].rightRearInner == 'Good'}">Good</option>
                                                                        <option th:value="'Fair'" th:selected="${claimDetail.tireConditionMeList[0].rightRearInner == 'Fair'}">Fair</option>
                                                                        <option th:value="'Bald'" th:selected="${claimDetail.tireConditionMeList[0].rightRearInner == 'Bald'}">Bald</option>
                                                                    </select>
                                                                </td>
                                                                <td style="border: none;">
                                                                    <select class="form-select form-select-sm" data-type="condition" data-wheel="lri">
                                                                        <option th:value="'N/A'" th:selected="${claimDetail.tireConditionMeList[0].leftRearInner == 'N/A'}">N/A</option>
                                                                        <option th:value="'Good'" th:selected="${claimDetail.tireConditionMeList[0].leftRearInner == 'Good'}">Good</option>
                                                                        <option th:value="'Fair'" th:selected="${claimDetail.tireConditionMeList[0].leftRearInner == 'Fair'}">Fair</option>
                                                                        <option th:value="'Bald'" th:selected="${claimDetail.tireConditionMeList[0].leftRearInner == 'Bald'}">Bald</option>
                                                                    </select>
                                                                </td>
                                                                <td style="border: none;">
                                                                    <select class="form-select form-select-sm" data-type="condition" data-wheel="other">
                                                                        <option th:value="'N/A'" th:selected="${claimDetail.tireConditionMeList[0].other == 'N/A'}">N/A</option>
                                                                        <option th:value="'Good'" th:selected="${claimDetail.tireConditionMeList[0].other == 'Good'}">Good</option>
                                                                        <option th:value="'Fair'" th:selected="${claimDetail.tireConditionMeList[0].other == 'Fair'}">Fair</option>
                                                                        <option th:value="'Bald'" th:selected="${claimDetail.tireConditionMeList[0].other == 'Bald'}">Bald</option>
                                                                    </select>
                                                                </td>
                                                            </tr>
                                                            <tr>
                                                                <th style="border: none; vertical-align: middle;">Size</th>
                                                                <td style="border: none;">
                                                                    <input type="text" class="form-control form-control-sm" data-type="size" data-wheel="rf" th:value="${claimDetail.tireConditionMeList[1].rightFront }">
                                                                </td>
                                                                <td style="border: none;">
                                                                    <input type="text" class="form-control form-control-sm" data-type="size" data-wheel="lf" th:value="${claimDetail.tireConditionMeList[1].leftFront }">
                                                                </td>
                                                                <td style="border: none;">
                                                                    <input type="text" class="form-control form-control-sm" data-type="size" data-wheel="rr" th:value="${claimDetail.tireConditionMeList[1].rightRear }">
                                                                </td>
                                                                <td style="border: none;">
                                                                    <input type="text" class="form-control form-control-sm" data-type="size" data-wheel="rl" th:value="${claimDetail.tireConditionMeList[1].leftRear }">
                                                                </td>
                                                                <td style="border: none;">
                                                                    <input type="text" class="form-control form-control-sm" data-type="size" data-wheel="rri" th:value="${claimDetail.tireConditionMeList[1].rightRearInner }">
                                                                </td>
                                                                <td style="border: none;">
                                                                    <input type="text" class="form-control form-control-sm" data-type="size" data-wheel="lri" th:value="${claimDetail.tireConditionMeList[1].leftRearInner }">
                                                                </td>
                                                                <td style="border: none;">
                                                                    <input type="text" class="form-control form-control-sm" data-type="size" data-wheel="other" th:value="${claimDetail.tireConditionMeList[1].other }">
                                                                </td>
                                                            </tr>
                                                            <tr>
                                                                <th style="border: none; vertical-align: middle;">Make</th>
                                                                <td style="border: none;">
                                                                    <input type="text" class="form-control form-control-sm" data-type="make" data-wheel="rf" th:value="${claimDetail.tireConditionMeList[2].rightFront }">
                                                                </td>
                                                                <td style="border: none;">
                                                                    <input type="text" class="form-control form-control-sm" data-type="make" data-wheel="lf" th:value="${claimDetail.tireConditionMeList[2].leftFront }">
                                                                </td>
                                                                <td style="border: none;">
                                                                    <input type="text" class="form-control form-control-sm" data-type="make" data-wheel="rr" th:value="${claimDetail.tireConditionMeList[2].rightRear }">
                                                                </td>
                                                                <td style="border: none;">
                                                                    <input type="text" class="form-control form-control-sm" data-type="make" data-wheel="rl" th:value="${claimDetail.tireConditionMeList[2].leftRear }">
                                                                </td>
                                                                <td style="border: none;">
                                                                    <input type="text" class="form-control form-control-sm" data-type="make" data-wheel="rri" th:value="${claimDetail.tireConditionMeList[2].rightRearInner }">
                                                                </td>
                                                                <td style="border: none;">
                                                                    <input type="text" class="form-control form-control-sm" data-type="make" data-wheel="lri" th:value="${claimDetail.tireConditionMeList[2].leftRearInner }">
                                                                </td>
                                                                <td style="border: none;">
                                                                    <input type="text" class="form-control form-control-sm" data-type="make" data-wheel="other" th:value="${claimDetail.tireConditionMeList[2].other }">
                                                                </td>
                                                            </tr>
                                                            <tr>
                                                                <th style="border: none; vertical-align: middle;">New / RB</th>
                                                                <td style="border: none;">
                                                                    <input type="text" class="form-control form-control-sm" data-type="newRb" data-wheel="rf" th:value="${claimDetail.tireConditionMeList[3].rightFront }">
                                                                </td>
                                                                <td style="border: none;">
                                                                    <input type="text" class="form-control form-control-sm" data-type="newRb" data-wheel="lf" th:value="${claimDetail.tireConditionMeList[3].leftFront }">
                                                                </td>
                                                                <td style="border: none;">
                                                                    <input type="text" class="form-control form-control-sm" data-type="newRb" data-wheel="rr" th:value="${claimDetail.tireConditionMeList[3].rightRear }">
                                                                </td>
                                                                <td style="border: none;">
                                                                    <input type="text" class="form-control form-control-sm" data-type="newRb" data-wheel="rl" th:value="${claimDetail.tireConditionMeList[3].leftRear }">
                                                                </td>
                                                                <td style="border: none;">
                                                                    <input type="text" class="form-control form-control-sm" data-type="newRb" data-wheel="rri" th:value="${claimDetail.tireConditionMeList[3].rightRearInner }">
                                                                </td>
                                                                <td style="border: none;">
                                                                    <input type="text" class="form-control form-control-sm" data-type="newRb" data-wheel="lri" th:value="${claimDetail.tireConditionMeList[3].leftRearInner }">
                                                                </td>
                                                                <td style="border: none;">
                                                                    <input type="text" class="form-control form-control-sm" data-type="newRb" data-wheel="other" th:value="${claimDetail.tireConditionMeList[3].other }">
                                                                </td>
                                                            </tr>
                                                            </tbody>
                                                        </table>

                                                        <div class="mb-3">
                                                            <label for="specialRemarks" class="form-label"
                                                                   style="font-size: 14px;color: rgba(0, 0, 0, 0.55);line-height: 15px;">Special
                                                                remarks :</label>
                                                            <textarea class="form-control form-control-sm"
                                                                      id="specialRemarks" rows="3"></textarea>
                                                        </div>

                                                        <div class="card-footer p-0 mt-4"
                                                             style="display: flex; justify-content: flex-end; align-items: center;  background: #ffffff; border-top: none;">
                                                            <button type="button" id="submitBtnTyre" class="btn btn-primary btn-sm"
                                                                    style="width: 92px;"
                                                                    th:if="${claimDetail.inspectionDto.vehicleAvailable == 'Y'}">Save</button>

                                                        </div>
                                                    </div>
                                                </div>
                                            </div>

                                            <div class="card">
                                                <div class="card-header" id="headingDesktopAssessment">
                                                    <p class="mb-0">
                                                        <button aria-controls="collapseDesktopAssessment"
                                                                aria-expanded="true"
                                                                class="btn btn-sm btn-link btn-block text-left"
                                                                data-bs-target="#collapseDesktopAssessment"
                                                                data-bs-toggle="collapse"
                                                                type="button" style="font-weight: 600;">
                                                            Online Assessment Review
                                                        </button>
                                                    </p>

                                                </div>
                                                <div aria-labelledby="headingDesktopAssessment" class="collapse"
                                                     data-parent="#accordionExample"
                                                     id="collapseDesktopAssessment">
                                                    <div class="card-body">
                                                        <div class="data-row-div row">
                                                            <div class="data-container-div col-12">
                                                                            <span class="data-label-span">
                                                                                Inspection Type
                                                                            </span>
                                                                <span class="data-details-span">
                                                                               Online Assessment
                                                                            </span>
                                                            </div>
                                                        </div>

                                                        <div class="card mt-3">
                                                            <div class="card-header px-2 py-1 sub-card-header-uw">
                                                                Desktop Review
                                                            </div>
                                                            <div class="card-body p-2" id="reviewDetail">
                                                                <div class="data-row-div"
                                                                     style="display: flex !important;flex-wrap: wrap;">

                                                                    <div class="data-container-div col-6">
                                                                            <span class="data-label-span">
                                                                                Total Approve ACR (Rs.)
                                                                            </span>
                                                                        <span class="data-details-span" id="approvedAcr" th:text="${claimDetail.claimAssignClaimHandlerDto.totalAcrApproved}">
                                                                            </span>
                                                                    </div>

                                                                    <div class="data-container-div col-6">
                                                                            <span class="data-label-span">
                                                                                ACR (Rs.)
                                                                            </span>
                                                                        <span class="data-details-span">
                                                                                <input class="form-control form-control-sm"
                                                                                       type="text" id="acr">
                                                                            </span>
                                                                    </div>

                                                                    <div class="data-container-div col-6">
                                                                            <span class="data-label-span">
                                                                                Excess (Rs.)
                                                                            </span>
                                                                        <span class="data-details-span">
                                                                                 <input class="form-control form-control-sm"
                                                                                        type="text" id="excess">
                                                                            </span>
                                                                    </div>

                                                                    <div class="data-container-div col-6">
                                                                            <span class="data-label-span">
                                                                                Bald Tyre Penalty
                                                                            </span>
                                                                        <span class="data-details-span">
                                                                                <input class="form-control form-control-sm"
                                                                                       type="text" id="boldTirePenalty">
                                                                            </span>
                                                                    </div>

                                                                    <div class="data-container-div col-6">
                                                                            <span class="data-label-span">
                                                                                Under Insurance Penalty
                                                                            </span>
                                                                        <span class="data-details-span">
                                                                                <input class="form-control form-control-sm"
                                                                                       type="text" id="underInsurancePanelty">
                                                                            </span>
                                                                    </div>

                                                                    <div class="data-container-div col-6">
                                                                            <span class="data-label-span">
                                                                                Payable Amount (Rs.)
                                                                            </span>
                                                                        <span class="data-details-span">
                                                                                <input class="form-control form-control-sm"
                                                                                       type="text" id="payeble">
                                                                            </span>
                                                                    </div>

                                                                    <div class="data-container-div col-6">
                                                                            <span class="data-label-span">
                                                                                Current Approved Advance Amount (Rs.)
                                                                            </span>
                                                                        <span class="data-details-span" th:text="${claimDetail.claimAssignClaimHandlerDto.aprvAdvanceAmount}" id="approvedAdvance">

                                                                            </span>
                                                                    </div>

                                                                    <div class="data-container-div col-6">
                                                                            <span class="data-label-span">
                                                                                Advance Amount (Rs.)
                                                                            </span>
                                                                        <span class="data-details-span">
                                                                                <input class="form-control form-control-sm"
                                                                                       type="text" id="advance">
                                                                            </span>
                                                                    </div>

                                                                    <div class="data-container-div col-6">
                                                                            <span class="data-label-span">
                                                                                Paid Advance Amount (Rs.)
                                                                            </span>
                                                                        <span class="data-details-span">
                                                                                <input class="form-control form-control-sm"
                                                                                       type="text" id="paidAdvance">
                                                                            </span>
                                                                    </div>

                                                                    <div class="data-container-div col-6">
                                                                            <span class="data-label-span">
                                                                                Balance Advance Amount (Rs.)
                                                                            </span>
                                                                        <span class="data-details-span">
                                                                                <input class="form-control form-control-sm"
                                                                                       type="text" id="balanceAdvance">
                                                                            </span>
                                                                    </div>

                                                                    <div class="data-container-div col-6">
                                                                            <span class="data-label-span">
                                                                                Total Approved Advance Amount (Rs.)
                                                                            </span>
                                                                        <span class="data-details-span">
                                                                                <input class="form-control form-control-sm"
                                                                                       type="text" id="totalApprovedAdvance">
                                                                            </span>
                                                                    </div>

                                                                    <div class="data-container-div col-6">
                                                                            <span class="data-label-span">
                                                                                Current Provision (Rs.)
                                                                            </span>
                                                                        <span class="data-details-span">
                                                                                <input class="form-control form-control-sm"
                                                                                       type="text" id="CurrentProvision">
                                                                            </span>
                                                                    </div>

                                                                    <div class="data-container-div col-6">
                                                                            <span class="data-label-span">
                                                                                Desktop Offer
                                                                            </span>
                                                                        <span class="data-details-span">
                                                                                <div class="inspection-data-form">
                                                                                    <div class="form-check">
                                                                                        <input class="form-check-input"
                                                                                               type="radio"
                                                                                               name="flexRadioDefaultOffer"
                                                                                               id="flexRadioDefaultOffer1" value="Y">
                                                                                        <label class="form-check-label">
                                                                                            Yes
                                                                                        </label>
                                                                                    </div>
                                                                                    <div class="form-check">
                                                                                        <input class="form-check-input"
                                                                                               type="radio"
                                                                                               name="flexRadioDefaultOffer"
                                                                                               id="flexRadioDefaultOffer2" value="N">
                                                                                        <label class="form-check-label">
                                                                                            No
                                                                                        </label>
                                                                                    </div>
                                                                                </div>
                                                                            </span>
                                                                    </div>

                                                                    <div class="data-container-div col-6">
                                                                            <span class="data-label-span">
                                                                               ARI Salvage
                                                                            </span>
                                                                        <span class="data-details-span">
                                                                                <div class="inspection-data-form">
                                                                                    <div class="form-check">
                                                                                        <input class="form-check-input"
                                                                                               type="radio"
                                                                                               name="flexRadioDefaultAri"
                                                                                               id="flexRadioDefaultAri1" value="Y">
                                                                                        <label class="form-check-label">
                                                                                            Yes
                                                                                        </label>
                                                                                    </div>
                                                                                    <div class="form-check">
                                                                                        <input class="form-check-input"
                                                                                               type="radio"
                                                                                               name="flexRadioDefaultAri"
                                                                                               id="flexRadioDefaultAri2" value="N">
                                                                                        <label class="form-check-label">
                                                                                            No
                                                                                        </label>
                                                                                    </div>
                                                                                </div>
                                                                            </span>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>

                                                        <div class="card mt-3">
                                                            <div class="card-header px-2 py-1 sub-card-header-uw">
                                                                Assessor Remarks
                                                            </div>
                                                            <div class="card-body p-2">
                                                                <div class="data-row-div"
                                                                     style="display: flex !important;flex-wrap: wrap;">
                                                                    <div class="data-container-div col-12">
                                                                            <span class="data-label-span">
                                                                                Settlement Methods
                                                                            </span>
                                                                        <span class="data-details-span">
                                                                            <select class="form-select form-control-sm mt-1" id="settelementMethod">
                                                                                <option value="0" disabled="disabled">-- Please Select --</option>
                                                                                <option value="1" selected="selected">Partial Loss</option>
                                                                                <option value="2" disabled="disabled">Total Loss</option>
                                                                                <option value="3" disabled="disabled">Cash In Lieu</option>
                                                                                <option value="4" disabled="disabled">Full & Final</option>
                                                                            </select>
                                                                            </span>
                                                                    </div>

                                                                    <div class="data-container-div col-6">
                                                                            <span class="data-label-span">
                                                                                Inspection Remarks
                                                                            </span>
                                                                        <span class="data-details-span">
                                                                                <textarea
                                                                                        class="form-control form-control-sm" id="inspectionRemark"></textarea>
                                                                            </span>
                                                                    </div>


                                                                    <div class="data-container-div col-6">
                                                                            <span class="data-label-span">
                                                                                Special Remarks
                                                                            </span>
                                                                        <span class="data-details-span">
                                                                                <textarea
                                                                                        class="form-control form-control-sm" id="specialRemark"></textarea>
                                                                            </span>
                                                                    </div>
                                                                    <div class="data-container-div col-6">
                                                                            <span class="data-label-span">
                                                                               Police Report Request
                                                                            </span>
                                                                        <span class="data-details-span">
                                                                                <div class="inspection-data-form">
                                                                                    <div class="form-check">
                                                                                        <input class="form-check-input"
                                                                                               type="radio"
                                                                                               name="flexRadioDefault"
                                                                                               id="flexRadioDefault24">
                                                                                        <label class="form-check-label"
                                                                                               for="flexRadioDefault24">
                                                                                            Yes
                                                                                        </label>
                                                                                    </div>
                                                                                    <div class="form-check">
                                                                                        <input class="form-check-input"
                                                                                               type="radio"
                                                                                               name="flexRadioDefault"
                                                                                               id="flexRadioDefault25">
                                                                                        <label class="form-check-label"
                                                                                               for="flexRadioDefault25">
                                                                                            No
                                                                                        </label>
                                                                                    </div>
                                                                                </div>
                                                                            </span>
                                                                    </div>

                                                                    <div class="data-container-div col-6">
                                                                            <span class="data-label-span">
                                                                               Investigate Claim
                                                                            </span>
                                                                        <span class="data-details-span">
                                                                                <div class="inspection-data-form">
                                                                                    <div class="form-check">
                                                                                        <input class="form-check-input"
                                                                                               type="radio"
                                                                                               name="flexRadioDefault"
                                                                                               id="flexRadioDefault26">
                                                                                        <label class="form-check-label"
                                                                                               for="flexRadioDefault26">
                                                                                            Yes
                                                                                        </label>
                                                                                    </div>
                                                                                    <div class="form-check">
                                                                                        <input class="form-check-input"
                                                                                               type="radio"
                                                                                               name="flexRadioDefault"
                                                                                               id="flexRadioDefault27">
                                                                                        <label class="form-check-label"
                                                                                               for="flexRadioDefault27">
                                                                                            No
                                                                                        </label>
                                                                                    </div>
                                                                                </div>
                                                                            </span>
                                                                    </div>

                                                                </div>
                                                            </div>
                                                        </div>

                                                        <div class="card mt-3">
                                                            <div class="card-body p-2">
                                                                <div class="data-row-div"
                                                                     style="display: flex !important;flex-wrap: wrap;">
                                                                    <div class="data-container-div col-6">
                                                                            <span class="data-label-span">
                                                                                Inform To Garage - Name
                                                                            </span>
                                                                        <span class="data-details-span">
                                                                                <input class="form-control form-control-sm"
                                                                                       type="text" id="informGarageName">
                                                                            </span>
                                                                    </div>

                                                                    <div class="data-container-div col-6">
                                                                            <span class="data-label-span">
                                                                                Inform To Garage - Contact
                                                                            </span>
                                                                        <span class="data-details-span">
                                                                                <input class="form-control form-control-sm"
                                                                                       type="text">
                                                                                <div class="inspection-data-form">
                                                                                    <div class="form-check">
                                                                                        <input class="form-check-input"
                                                                                               type="radio"
                                                                                               name="flexRadioDefault"
                                                                                               id="flexRadioDefault28">
                                                                                        <label class="form-check-label"
                                                                                               for="flexRadioDefault28">
                                                                                            Agree
                                                                                        </label>
                                                                                    </div>
                                                                                    <div class="form-check">
                                                                                        <input class="form-check-input"
                                                                                               type="radio"
                                                                                               name="flexRadioDefault"
                                                                                               id="flexRadioDefault29">
                                                                                        <label class="form-check-label"
                                                                                               for="flexRadioDefault29">
                                                                                            Disagree
                                                                                        </label>
                                                                                    </div>
                                                                                </div>
                                                                            </span>
                                                                    </div>

                                                                    <div class="data-container-div col-6">
                                                                            <span class="data-label-span">
                                                                                Inform To Customer - Name
                                                                            </span>
                                                                        <span class="data-details-span">
                                                                                <input class="form-control form-control-sm"
                                                                                       type="text">
                                                                            </span>
                                                                    </div>

                                                                    <div class="data-container-div col-6">
                                                                            <span class="data-label-span">
                                                                                Inform To Customer - Contact
                                                                            </span>
                                                                        <span class="data-details-span">
                                                                                <input class="form-control form-control-sm"
                                                                                       type="text">
                                                                                <div class="inspection-data-form">
                                                                                    <div class="form-check">
                                                                                        <input class="form-check-input"
                                                                                               type="radio"
                                                                                               name="flexRadioDefault"
                                                                                               id="flexRadioDefault30">
                                                                                        <label class="form-check-label"
                                                                                               for="flexRadioDefault30">
                                                                                            Agree
                                                                                        </label>
                                                                                    </div>
                                                                                    <div class="form-check">
                                                                                        <input class="form-check-input"
                                                                                               type="radio"
                                                                                               name="flexRadioDefault"
                                                                                               id="flexRadioDefault31">
                                                                                        <label class="form-check-label"
                                                                                               for="flexRadioDefault31">
                                                                                            Disagree
                                                                                        </label>
                                                                                    </div>
                                                                                </div>
                                                                            </span>
                                                                    </div>

                                                                    <div class="data-container-div col-6">
                                                                            <span class="data-label-span">
                                                                                    Comment
                                                                                </span>
                                                                        <span class="data-details-span">
                                                                                    <textarea
                                                                                            class="form-control form-control-sm"></textarea>
                                                                                </span>
                                                                    </div>
                                                                    <div class="data-container-div col-6">
                                                                            <span class="data-label-span">
                                                                                    Reason
                                                                                </span>
                                                                        <span class="data-details-span">
                                                                                    <textarea
                                                                                            class="form-control form-control-sm"></textarea>
                                                                                </span>
                                                                    </div>


                                                                </div>
                                                            </div>
                                                        </div>


                                                    </div>
                                                    <div class="card-footer"
                                                         style="display: flex; justify-content: flex-end; align-items: center;  background: #ffffff; border-top: none;">
                                                        <button type="button" id="saveInspectionBtn" class="btn btn-primary btn-sm"
                                                                style="width: 92px;">Save
                                                        </button>
                                                    </div>
                                                </div>

                                            </div>

                                            <div class="card">
                                                <div class="card-header" id="headingOne">
                                                    <p class="mb-0">
                                                        <button aria-controls="collapseOne"
                                                                aria-expanded="true"
                                                                class="btn btn-sm btn-link btn-block text-left"
                                                                data-bs-target="#collapseOne" data-bs-toggle="collapse"
                                                                type="button" style="font-weight: 600;">
                                                            Current Photos
                                                        </button>
                                                    </p>
                                                </div>

                                                <div aria-labelledby="headingOne" class="collapse"
                                                     data-parent="#accordionExample"
                                                     id="collapseOne">
                                                    <div class="card-body"
                                                         style="overflow-y: scroll; height: 300px;">

                                                        <div class="card bg-light">
                                                            <div class="card-body" >
                                                                <div style="display: flex;flex-direction: row; flex-wrap: wrap; gap: 18px; align-items: center; width: 100%">
                                                                    <div style="flex: 0 0 30%;" class="d-flex">
                                                                        <div class="mr-auto p-0">
                                                                            <small> <i class="fa-solid fa-list" style="width: 12px;height: 12px;color: #1d4ca0;"></i>
                                                                                <!--                                                                                <span th:text="${agentVideoForm.getCallDetailsDto().getCallId()}">Current Call Id</span>-->
                                                                                <span style="font-size: 12px; font-weight: 600; color: #1d4ca0; margin-left: 8px;">Current Inspection Detail</span>
                                                                            </small>
                                                                        </div>

                                                                    </div>
                                                                    <div style="flex: 0 0 30%;" class="d-flex">
                                                                        <div class="mr-auto p-0">
                                                                            <small> <i class="fa-solid fa-user" style="width: 12px;height: 12px;color: #1d4ca0;"></i>
                                                                                <span style="font-size: 12px; font-weight: 600; color: #1d4ca0; margin-left: 8px;"
                                                                                      th:text="${agentVideoForm.getJobDetailDto().getCreatedBy()}">jhone
                                                                                                                                Doe</span>

                                                                            </small>
                                                                        </div>
                                                                    </div>
                                                                    <div style="flex: 0 0 30%;" class="d-flex">
                                                                        <div class="p-0">
                                                                            <small> <i class="fa-solid fa-calendar-days" style="width: 12px;height: 12px;color: #1d4ca0;"></i>
                                                                                <span style="font-size: 12px; font-weight: 600; color: #1d4ca0; margin-left: 8px;"
                                                                                      th:text="${#temporals.format(agentVideoForm.callDetailsDto.tokenGenerateDateTime, 'MMM dd, yyyy hh:mm a')}">Dec
                                                                                                                                05,2020 | 9.59PM</span>
                                                                            </small>
                                                                        </div>
                                                                    </div>
                                                                </div>

                                                                <hr>
                                                                <div id="imageContainer">
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>

                                            <div id="previousImageContainer" th:insert="~{pages/admin/fragments/previous-image-container :: previousImageContainer}"></div>


                                        </div>
                                    </div>

                                </div>
                            </div>
                        </div>
                    </div>


                    <!-- Content Row -->

                </div>
                <!-- /.container -->

            </div>
            <!-- End of Main Content -->


            <!--Bottom Camera action-->

            <!--                <div class="row" id="remoteVideoButtonPanel_1">-->
            <!--                    <div class=" col-xl-5 col-lg-7  ">-->
            <!--                        <div class="d-flex justify-content-center ">-->
            <!--                            <div class="on-bot-crd ">-->
            <!--                                <div class="on-bot-sub">-->
            <!--                                    <div class="d-flex justify-content-center mt-0">-->
            <!--                                        <div class="d-flex flex-row">-->
            <!--&lt;!&ndash;                                            <div class="p-2">&ndash;&gt;-->
            <!--&lt;!&ndash;                                                <button class="btn btn-light btn-circle btn-circle-lg m-1 on-mute"&ndash;&gt;-->
            <!--&lt;!&ndash;                                                        data-bs-toggle="tooltip" data-bs-placement="top"&ndash;&gt;-->
            <!--&lt;!&ndash;                                                        data-bs-custom-class="custom-tooltip"&ndash;&gt;-->
            <!--&lt;!&ndash;                                                        data-bs-title="Mute / Un-Mute"&ndash;&gt;-->
            <!--&lt;!&ndash;                                                        id="btnOnMute"&ndash;&gt;-->
            <!--&lt;!&ndash;                                                >&ndash;&gt;-->
            <!--&lt;!&ndash;                                                    <i class="fas fa-microphone-alt"></i>&ndash;&gt;-->
            <!--&lt;!&ndash;                                                </button>&ndash;&gt;-->
            <!--&lt;!&ndash;                                            </div>&ndash;&gt;-->
            <!--&lt;!&ndash;                                            <div class="p-2">&ndash;&gt;-->
            <!--&lt;!&ndash;                                                <button class="btn btn-success btn-circle btn-circle-lg m-1 on-record"&ndash;&gt;-->
            <!--&lt;!&ndash;                                                        data-bs-toggle="tooltip" data-bs-placement="top"&ndash;&gt;-->
            <!--&lt;!&ndash;                                                        data-bs-custom-class="custom-tooltip"&ndash;&gt;-->
            <!--&lt;!&ndash;                                                        data-bs-title="Start / Stop Recording" id="btnOnRecord"&ndash;&gt;-->
            <!--&lt;!&ndash;                                                >&ndash;&gt;-->
            <!--&lt;!&ndash;                                                    <i class="fas fa-play-circle text-white"></i>&ndash;&gt;-->
            <!--&lt;!&ndash;                                                </button>&ndash;&gt;-->
            <!--&lt;!&ndash;                                            </div>&ndash;&gt;-->
            <!--&lt;!&ndash;                                            <div class="p-2">&ndash;&gt;-->
            <!--&lt;!&ndash;                                                <button class="btn btn-light btn-circle btn-circle-lg m-1 "&ndash;&gt;-->
            <!--&lt;!&ndash;                                                        data-bs-toggle="tooltip" data-bs-placement="top"&ndash;&gt;-->
            <!--&lt;!&ndash;                                                        data-bs-custom-class="custom-tooltip"&ndash;&gt;-->
            <!--&lt;!&ndash;                                                        data-bs-title="Take Photo"&ndash;&gt;-->
            <!--&lt;!&ndash;                                                        data-placement="top"&ndash;&gt;-->
            <!--&lt;!&ndash;                                                        data-toggle="tooltip" id="btnTakePhoto"&ndash;&gt;-->
            <!--&lt;!&ndash;                                                >&ndash;&gt;-->
            <!--&lt;!&ndash;                                                    <i class="fas fa-camera"></i>&ndash;&gt;-->
            <!--&lt;!&ndash;                                                </button>&ndash;&gt;-->
            <!--&lt;!&ndash;                                            </div>&ndash;&gt;-->
            <!--&lt;!&ndash;                                            <div class="p-2">&ndash;&gt;-->
            <!--&lt;!&ndash;                                                <button class="btn btn-danger btn-circle btn-circle-lg m-1 on-hangup"&ndash;&gt;-->
            <!--&lt;!&ndash;                                                        data-bs-toggle="tooltip" data-bs-placement="top"&ndash;&gt;-->
            <!--&lt;!&ndash;                                                        data-bs-custom-class="custom-tooltip"&ndash;&gt;-->
            <!--&lt;!&ndash;                                                        data-bs-title="End Session"&ndash;&gt;-->
            <!--&lt;!&ndash;                                                        id="btnOnHangUp"&ndash;&gt;-->
            <!--&lt;!&ndash;                                                >&ndash;&gt;-->
            <!--&lt;!&ndash;                                                    <i class="fas fa-phone"></i>&ndash;&gt;-->
            <!--&lt;!&ndash;                                                </button>&ndash;&gt;-->
            <!--&lt;!&ndash;                                            </div>&ndash;&gt;-->

            <!--                                        </div>-->
            <!--                                    </div>-->
            <!--                                </div>-->
            <!--                            </div>-->
            <!--                        </div>-->
            <!--                    </div>-->
            <!--                    <div class="col-xl-7 col-lg-7  ">-->
            <!--                    </div>-->
            <!--                    &lt;!&ndash; Content Row &ndash;&gt;-->
            <!--                </div>-->

            <!--End of Bottom Camera action-->

        </div>
        <!-- End of Content  -->
        <script th:inline="javascript">

            $('#saveInspectionBtn').on('click', function () {
                const finalJsonData = collectFullClaimReviewData();

                console.log("Collected Data:", finalJsonData);

            });


            window.addEventListener('DOMContentLoaded', () => {
                const now = new Date();
                const formattedNow = now.toISOString().slice(0, 16); // Format: YYYY-MM-DDTHH:MM
                document.getElementById('dateOfInspection').value = formattedNow;
            });

            $(document).ready(function () {
                //set date
                const now = new Date();
                const formatted = now.toISOString().slice(0,16); // format to 'YYYY-MM-DDTHH:mm'
                $('#dateOfInspection').val(formatted);

                //disable default fields
                $('input[name="flexRadioDefault1st"]').on('change', function () {
                    if ($(this).val() === 'Y') {
                        $('#reason').prop('disabled', false);
                    } else {
                        $('#reason').prop('disabled', true).val('');                     }
                });
                //disable default fields
                $('input[name="flexRadioDefaultInv"]').on('change', function () {
                    if ($(this).val() === 'Y') {
                        $('#RteRemark').prop('disabled', false);
                    } else {
                        $('#RteRemark').prop('disabled', true).val('');
                    }
                });
            });

        /*<![CDATA[*/
            $(document).ready(function () {
                $('#submitBtnTyre').on('click', function () {
                    // Create temporary indexed map
                    let tyreMap = {};

                    $('select[data-type], input[data-type]').each(function () {
                        const type = $(this).data('type');   // condition, size, etc.
                        const wheel = $(this).data('wheel'); // 0, 1, 2, ...
                        const value = $(this).val();

                        if (!tyreMap[wheel]) {
                            tyreMap[wheel] = {};
                        }

                        tyreMap[wheel][type] = value;
                    });

                    // Convert map to array
                    let tyreDetails = Object.keys(tyreMap).sort().map(wheel => tyreMap[wheel]);

                    // Final request payload
                    let requestBody = {
                        tyreDetails: tyreDetails,
                        remarks: $('#specialRemarks').val(),
                        claimNo: $('#claimNo').val(),
                        jobRef: $('#jobRef').val(),
                        userId: $('#userId').val(),
                    };

                    let url = '/misyn/api/v1/jobs/saveTyreDetail';
                    let csrfToken = document.getElementById('_csrf').value;
                    let request = new Request(url, {
                        method: 'POST',
                        body: JSON.stringify(requestBody),
                        headers: new Headers({
                            'Content-Type': 'application/json',
                            'X-CSRF-TOKEN': csrfToken
                        })
                    });
                    fetch(request)
                        .then(response => {
                            console.log("response ", response)
                            console.log("response ", response.status)
                            alert("Record saved successfully");
                        })
                        .catch(error => {
                            console.error('There was a problem with the fetch operation:', error);
                            alert("Can not be Saved");
                        });
                });

                // $('#submitBtnTyre').on('click', function () {
                //     console.log("Tyre Save Clicked");
                //     // Create temporary indexed map
                //     let tyreMap = {};
                //
                //     $('select[data-type], input[data-type]').each(function () {
                //         const type = $(this).data('type');   // condition, size, etc.
                //         const wheel = $(this).data('wheel'); // 0, 1, 2, ...
                //         const value = $(this).val();
                //
                //         if (!tyreMap[wheel]) {
                //             tyreMap[wheel] = {};
                //         }
                //
                //         tyreMap[wheel][type] = value;
                //     });
                //
                //     // Convert map to array
                //     let tyreDetails = Object.keys(tyreMap).sort().map(wheel => tyreMap[wheel]);
                //
                //     // Final request payload
                //     let requestBody = {
                //         tyreDetails: tyreDetails,
                //         remarks: $('#specialRemarks').val(),
                //         claimNo: $('#claimNo').val(),
                //         jobRef: $('#jobRef').val(),
                //         userId: $('#userId').val(),
                //     };
                //
                //     let url = '/misyn/api/v1/jobs/saveTyreDetail';
                //     let csrfToken = document.getElementById('_csrf').value;
                //     let request = new Request(url, {
                //         method: 'POST',
                //         body: JSON.stringify(requestBody),
                //         headers: new Headers({
                //             'Content-Type': 'application/json',
                //             'X-CSRF-TOKEN': csrfToken
                //         })
                //     });
                //     fetch(request)
                //         .then(response => {
                //             console.log("response ", response)
                //             console.log("response ", response.status)
                //             alert("Record saved successfully");
                //         })
                //         .catch(error => {
                //             console.error('There was a problem with the fetch operation:', error);
                //             showToast("Error", "Can not be Saved", TOAST_TYPE.ERROR);
                //         });
                // });
            });


            $('#inspectionDetailSaveBtn').click(function() {

                // Collect all the data from the form
                const inspectionData = {
                    jobNo: $('#jobNumber').text(),
                    claimNo: $('#claimNo').val(),
                    refNo: $('#jobRef').val(),
                    makeConfirm: document.querySelector('input[name="flexRadioDefaultMake"]:checked')?.value,
                    modelConfirm:document.querySelector('input[name="flexRadioDefaultModel"]:checked')?.value,
                    yearMakeConfirm: document.querySelector('input[name="flexRadioDefaultMYear"]:checked')?.value,
                    engNoConfirm: document.querySelector('input[name="flexRadioDefaultEngine"]:checked')?.value,
                    inspectDatetime: $('#dateOfInspection').val(),
                    pav: $('#pav').val(),
                    damageDetails: $('#detailsOfDamage').next('textarea').val(),
                    pad: $('#pad').next('textarea').val(),
                    genunOfAccid: document.querySelector('input[name="flexRadioDefaultGenAcc"]:checked')?.value,
                    firstStatementRqed: document.querySelector('input[name="flexRadioDefault1st"]:checked')?.value,
                    firstStatementReqReason: $('#reason').val(),
                    investRqed: document.querySelector('input[name="flexRadioDefaultInv"]:checked')?.value,
                    assessorRemark: $('#assessorRemark').val(),
                    rteRemark: $('#RteRemark').val(),
                    onlineAssessmentRemark: $('#onlineAssesmentRemark').val()
                };

                // AJAX call to save the data
                let url = '/misyn/api/v1/jobs/saveInspectionDetail';
                let csrfToken = document.getElementById('_csrf').value;
                let request = new Request(url, {
                    method: 'POST',
                    body: JSON.stringify(inspectionData),
                    headers: new Headers({
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': csrfToken
                    })
                });
                fetch(request)
                    .then(response => {
                        console.log("response ", response)
                        console.log("response ", response.status)
                        if(response.status){
                            alert("Record saved successfully");
                        }else{
                            alert("Can not be Saved");
                        }
                    })
                    .catch(error => {
                        console.error('There was a problem with the fetch operation:', error);
                        alert("Can not be Saved");
                    });
            });


            function collectFullClaimReviewData() {
                const data = {
                    // === Desktop Review ===
                    approvedAcr: $('#approvedAcr').text().trim(),
                    acr: $('#acr').val().trim(),
                    excess: $('#excess').val().trim(),
                    baldTyrePenalty: $('#boldTirePenalty').val().trim(),
                    underInsurancePenalty: $('#underInsurancePanelty').val().trim(),
                    payable: $('#payeble').val().trim(),
                    approvedAdvance: $('#approvedAdvance').text().trim(),
                    advance: $('#advance').val().trim(),
                    paidAdvance: $('#paidAdvance').val().trim(),
                    balanceAdvance: $('#balanceAdvance').val().trim(),
                    totalApprovedAdvance: $('#totalApprovedAdvance').val().trim(),
                    currentProvision: $('#CurrentProvision').val().trim(),
                    desktopOffer: $('input[name="flexRadioDefaultOffer"]:checked').val() || null,
                    ariSalvage: $('input[name="flexRadioDefaultAri"]:checked').val() || null,

                    // === Settlement Section ===
                    settlementMethod: $('#settelementMethod option:selected').text().trim(),
                    inspectionRemark: $('#inspectionRemark').val().trim(),
                    specialRemark: $('#specialRemark').val().trim(),
                    policeReportRequest: $('input[id="flexRadioDefault24"]:checked').val() ||
                        $('input[id="flexRadioDefault25"]:checked').val() || null,
                    investigateClaim: $('input[id="flexRadioDefault26"]:checked').val() ||
                        $('input[id="flexRadioDefault27"]:checked').val() || null,

                    // === Inform to Garage Section ===
                    informToGarage: [
                        {
                            name: $('#informGarageName').val().trim(),
                            contact: $('.data-container-div input[type="text"]').eq(1).val().trim(),
                            agreement: $('input[id="flexRadioDefault28"]:checked').val() ||
                                $('input[id="flexRadioDefault29"]:checked').val() || null
                        },
                        {
                            name: $('.data-container-div input[type="text"]').eq(2).val().trim(),
                            contact: $('.data-container-div input[type="text"]').eq(3).val().trim(),
                            agreement: $('input[id="flexRadioDefault30"]:checked').val() ||
                                $('input[id="flexRadioDefault31"]:checked').val() || null
                        }
                    ],

                    // === Comment & Reason ===
                    comment: $('textarea').eq(2).val().trim(),
                    reason: $('textarea').eq(3).val().trim()
                };

                return data;
            }
        </script>

    </div>
</th:block>


</html>
