<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<th:block th:fragment="header">
<div class="nav-scroller py-0 mb-2 " style="position: fixed; width: 100%; z-index: 1000 !important;">
    <div class="shadow-sm">
        <nav class="navbar p-0" style="background: #ffffff;">
            <div style="display: flex; flex-direction: row; justify-content: flex-start; align-items: center; flex: 0 0 calc(100% - 164px);">
                <div class="navbar-brand p-0" style="padding-left: 8px !important;">
                    <img src="assets/img/hnblogo.png"
                         style="width: 70px;"
                         th:src="@{/resources/assets/img/hnblogo.png}"/>
                </div>
                <div class="navbar-nav" style="display: flex; flex-direction: row; align-items: center; gap: 16px; justify-content: space-between;">

                    <div class="nav-info-container">
                        <div class="nav-info-img">
                            <i class="fa-solid fa-tag nav-icon" style="transform: rotate(90deg);"></i>
                        </div>

                        <div class="nav-info-child-container">
                                    <span class="info-label">
                                         Claim No.
                                    </span>
                            <span class="info-detail" th:text="${agentVideoForm.jobDetailDto.jobNumber}" >
                                        1234567890
                                    </span>
                        </div>
                    </div>

                    <div class="nav-info-container">
                        <div class="nav-info-img">
                            <i class="fa-solid fa-car nav-icon"></i>
                        </div>
                        <div class="nav-info-child-container">
                                    <span class="info-label">
                                        Vehicle No.
                                    </span>
                            <span class="info-detail" th:text="${agentVideoForm.jobDetailDto.vehicleNo}">123456789V</span>
                        </div>
                    </div>

                    <div class="nav-info-container">
                        <div class="nav-info-img">
                            <i class="fa-solid fa-car nav-icon"></i>
                        </div>
                        <div class="nav-info-child-container">
                                    <span class="info-label">
                                        Vehicle Make
                                    </span>
                            <span class="info-detail" th:text="${agentVideoForm.jobDetailDto.vehicleMake}">123456789V</span>
                        </div>
                    </div>

                    <div class="nav-info-container">
                        <div class="nav-info-img">
                            <i class="fa-solid fa-car nav-icon"></i>
                        </div>
                        <div class="nav-info-child-container">
                                    <span class="info-label">
                                        Vehicle Model.
                                    </span>
                            <span class="info-detail" th:text="${agentVideoForm.jobDetailDto.vehicleModel}">123456789V</span>
                        </div>
                    </div>

                    <div class="nav-info-container">
                        <div class="nav-info-img">
                            <i class="fa-solid fa-user nav-icon"></i>
                        </div>
                        <div class="nav-info-child-container">
                                <span class="info-label">
                                    Insured Name
                                </span>
                            <span class="info-detail" th:text="${agentVideoForm.jobDetailDto.insuredName}">
                                   Chinthaka Perera
                                </span>
                        </div>
                    </div>

                    <div class="nav-info-container">
                        <div class="nav-info-img">
                            <i class="fa-solid fa-phone nav-icon"></i>
                        </div>
                        <div class="nav-info-child-container">
                                <span class="info-label">
                                    Mobile No.
                                </span>
                            <span class="info-detail" th:text="${agentVideoForm.jobDetailDto.insuredMobileNo}">
                                    0781212356
                                </span>
                        </div>
                    </div>

                    <div class="nav-info-container">
                        <div class="nav-info-img">
                            <i class="fa-solid fa-calendar-day nav-icon"></i>
                        </div>
                        <div class="nav-info-child-container">
                                <span class="info-label">
                                    Reported Date
                                </span>

                            <span class="info-detail"
                                  th:text="${#temporals.format(agentVideoForm.callDetailsDto.tokenGenerateDateTime, 'MMMM dd, yyyy hh:mm a')}">
</span>
                        </div>
                    </div>

                    <div class="nav-info-container">
                        <div class="nav-info-img">
                            <i class="fa-solid fa-location-crosshairs nav-icon"></i>
                        </div>
                        <div class="nav-info-child-container">
                                <span class="info-label">
                                    Latitude
                                </span>
                            <span class="info-detail" id="spanLatitude">
                                </span>
                        </div>
                    </div>

                    <div class="nav-info-container">
                        <div class="nav-info-img">
                            <i class="fa-solid fa-location-crosshairs nav-icon"></i>
                        </div>
                        <div class="nav-info-child-container">
                                <span class="info-label">
                                    Longitude
                                </span>
                            <span class="info-detail" id="spanLongitude">
                                </span>
                        </div>
                    </div>
                </div>
            </div>
            <div style="display: flex; flex-direction: row; align-items: center; gap: 12px; justify-content: space-between;">
                <div style="display: flex; flex-direction: column; align-items: center; gap: 6px; justify-content: center;">
                            <span class="badge text-bg-warning" id="connectionStateText" style="width: 104px; font-size: 11px !important;height: 20px; display: flex; flex-direction: row; justify-content: center; align-items: center;">
                                Pending
                            </span>
                    <span class="badge text-bg-success" id="signalInfoBadge" style="width: 104px; font-size: 11px !important;height: 20px; display: flex; flex-direction: row; justify-content: center; align-items: center;">
                                <span id="signalInfoText" style="font-size: 11px !important;">NA</span>
                                <img src="assets/img/signal.svg" style="width: 12px; height: 12px; margin-left: 4px;"
                                     th:src="@{/resources/assets/img/signal.svg}"/>
                            </span>
                </div>

                <div style="padding-right: 12px;">
                    <a href="#">
                        <button class="btn btn-light btn-sm"
                                style=" width: 20px;
                                                padding: 0;
                                                height: 20px;
                                                display: flex;
                                                justify-content: center;
                                                align-items: center;
                                                border-radius: 50%;
                                                margin-left: 8px;"
                                th:onclick="|onLogout()|" type="button">
                            <img class="mi-top-icn" src="assets/img/cancel.svg"  style="width: 20px; height: 20px;"
                                 th:src="@{/resources/assets/img/cancel.svg}">
                        </button>
                    </a>
                </div>
            </div>
        </nav>
    </div>
</div>
</th:block>
</html>
