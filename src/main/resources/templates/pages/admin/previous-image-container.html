<div xmlns:th="http://www.thymeleaf.org">
    <div class="card">
        <div class="card-header" id="headingTwo">
            <p class="mb-0">
                <button aria-controls="collapseTwo"
                        aria-expanded="false"
                        class="btn btn-sm btn-link btn-block text-left collapsed"
                        data-bs-target="#collapseTwo"
                        data-bs-toggle="collapse"
                        type="button" style="font-weight: 600;">
                    Previous Photos
                </button>
            </p>
        </div>
        <div aria-labelledby="headingTwo" class="collapse"
             data-parent="#accordionExample" id="collapseTwo">
            <div class="card-body"
                 style="overflow-y: scroll; height: 400px;">
                <!-- Previous Photos by Inspection Type (from claim) -->
                <div th:if="${previousPhotos != null and !previousPhotos.inspectionTypes.isEmpty()}" style="margin-bottom: 12px;">
                    <div style="background: #e8f4fd; padding: 8px; border-radius: 4px; margin-bottom: 8px;">
                        <h6 style="margin: 0; color: #1d4ca0; font-size: 14px;">
                            <i class="fa-solid fa-camera" style="margin-right: 8px;"></i>
                            Previous Photos by Inspection Type (Claim #<span th:text="${previousPhotos.claimNumber}"></span>)
                        </h6>
                    </div>
                    <div th:each="inspectionType,inspectionStat: ${previousPhotos.inspectionTypes}" style="margin-bottom: 8px;">
                        <div class="card">
                            <div class="card-header" style="background: #f8f9fa; padding: 8px 12px;">
                                <h6 style="margin: 0; color: #495057; font-size: 13px;">
                                    <i class="fa-solid fa-clipboard-check" style="margin-right: 6px; color: #28a745;"></i>
                                    <span th:text="${inspectionType.inspectionType}">Inspection Type</span>
                                    <span style="color: #6c757d; font-size: 11px;">(<span th:text="${#lists.size(inspectionType.photos)}">0</span> photos)</span>
                                </h6>
                            </div>
                            <div class="card-body" style="padding: 8px;">
                                <div style="display: flex; flex-wrap: wrap; gap: 8px;">
                                    <div th:each="photo,photoStat: ${inspectionType.photos}" style="flex: 0 0 calc(25% - 6px); min-width: 120px;">
                                        <div class="card" style="border: 1px solid #dee2e6;">
                                            <div class="card-body" style="padding: 6px; text-align: center;">
                                                <div style="margin-bottom: 4px;">
                                                    <img src="assets/img/image.png"
                                                         alt="Previous Photo"
                                                         class="img-thumbnail"
                                                         style="width: 80px; height: 60px; object-fit: cover; cursor: pointer;"
                                                         th:onclick="|window.open('${photo.documentPath}', '_blank')|">
                                                </div>
                                                <small style="font-size: 10px; color: #6c757d; display: block;" th:text="${photo.documentTypeName}">Document Type</small>
                                                <small style="font-size: 9px; color: #adb5bd;" th:text="'Ref: ' + ${photo.referenceNumber}">Ref: 123</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Existing Previous Call Photos -->
                <div th:each="prevCallImageVideo,iStat: ${agentVideoForm.getPrevCallImageVideoList()}"
                     style="margin-bottom: 6px;">
                    <div id="accordionImage">
                        <div class="card">
                            <div class="card-header"
                                 id="accordionImageHeadingOne">
                                <div data-toggle="collapse"
                                     style="cursor: pointer;"
                                     th:data-target="'#accordionImageCollapse' + ${iStat.index}"
                                     aria-expanded="true"
                                     th:aria-controls="'accordionImageCollapse' + ${iStat.index}">
                                    <div style="width: 100%; display: flex; flex-direction: row; justify-content: space-between;">
                                        <div style="display: flex;flex-direction: row; flex-wrap: wrap; gap: 18px; align-items: center; width: 100%">
                                            <div style="flex: 0 0 30%;" class="d-flex">
                                                <div class="mr-auto p-0">
                                                    <small>
                                                        <i class="fa-solid fa-list" style="width: 12px;height: 12px;color: #1d4ca0;"></i>
                                                        <span style="font-size: 12px; font-weight: 600; color: #1d4ca0; margin-left: 8px;"
                                                              th:text="${prevCallImageVideo.getTitle()}">1st Call Photos</span>
                                                        <!--                                                                                                &nbsp;<span th:text="${prevCallImageVideo.getTitle()+' Call Detail'}">1st Call Photos</span>-->
                                                    </small>
                                                </div>
                                            </div>
                                            <div style="flex: 0 0 30%;" class="d-flex">
                                                <div class="mr-auto p-0">
                                                    <small>
                                                        <i class="fa-solid fa-user" style="width: 12px;height: 12px;color: #1d4ca0;"></i>
                                                        <span style="font-size: 12px; font-weight: 600; color: #1d4ca0; margin-left: 8px;"
                                                              th:text="${prevCallImageVideo.getCallDetailsDto().getCreatedBy()}">jhone
                                                                                                                                    Doe</span>
                                                    </small>
                                                </div>


                                            </div>
                                            <div style="flex: 0 0 30%;" class="d-flex">
                                                <div class="p-0">
                                                    <small>
                                                        <i class="fa-solid fa-calendar-days" style="width: 12px;height: 12px;color: #1d4ca0;"></i>
                                                        <span style="font-size: 12px; font-weight: 600; color: #1d4ca0; margin-left: 8px;"
                                                              th:text="${#temporals.format(prevCallImageVideo.getCallDetailsDto().tokenGenerateDateTime, 'MMM dd, yyyy hh:mm a')}">Dec
                                                                                                                                05,2020 | 9.59PM</span>
                                                    </small>
                                                </div>
                                            </div>
                                        </div>

                                    </div>
                                </div>
                            </div>

                            <div style="display: flex;flex-wrap: wrap; gap: 12px; padding: 8px;">
                                <div style="flex: 0 0 calc(50% - 6px);"
                                     th:each="uploadDocumentTypeDto,uploadDocumentTypeStat: ${prevCallImageVideo.getUploadDocumentTypeList()}"
                                     th:id="${'prevImageContainer'+prevCallImageVideo.getCallIndex()}">
                                    <div class="card-body" style="padding: 4px 12px 12px 12px !important;background: #fafafa;border-radius: 4px;" >
                                        <span class="data-details-span" th:text="${uploadDocumentTypeDto.getDocumentTypeName()}" style="font-weight: 600;font-size: 13px; color: #575757;">Passport</span>
                                        <div class="inspection-img-row">
                                            <div class="inspection-img-div"
                                                 th:each="uploadImage,imageStat: ${uploadDocumentTypeDto.getUploadImageList()}">
                                                <!-- Delete icon-->
                                                <div class="img-wrap">
                                                                                <span class="close"
                                                                                      th:onclick="|deleteBackendImage('${uploadImage.uploadImageDetailsRefNo}','${prevCallImageVideo.getCallIndex()}')|"><img
                                                                                        src="assets/img/close.svg"
                                                                                        style="width: 16px; text-align: center"
                                                                                        th:src="@{/resources/assets/img/close.svg}"/></span>
                                                    <a data-magnify='gallery'
                                                       th:href="@{'/api/v1/image/view-original-image/'+${uploadImage.getUploadImageDetailsRefNo()}}"
                                                       th:data-ref-no="${uploadImage.uploadImageDetailsRefNo}">

                                                        <div class="card ">
                                                            <div class="">
                                                                <img alt="Responsive image"
                                                                     class="img-thumbnail"
                                                                     style=""
                                                                     src='assets/img/image.png'
                                                                     th:src="@{'/api/v1/image/view-thumb-image/'+${uploadImage.uploadImageDetailsRefNo}}"
                                                                     th:data-ref-no="${uploadImage.uploadImageDetailsRefNo}"
                                                                >
                                                            </div>
                                                        </div>

                                                        <!--  edit icon-->
                                                        <a style="color: white">
                                                                                             <span class="edit-img-icon"
                                                                                                   th:onclick="|openDrawingModal('${uploadImage.uploadImageDetailsRefNo}','${uploadDocumentTypeDto.documentTypeId}')|">
                                                                                                   <i class="fa fa-pencil"
                                                                                                      aria-hidden="true"></i>
                                                                                             </span>
                                                        </a>

                                                        <!--map icon-->
                                                        <a href="#"
                                                           style="color: white"
                                                           th:onclick="|showMapModal('${uploadImage.getLatitudeVal()}','${uploadImage.getLongitudeVal()}')|">
                                                                                        <span class="bot-map"><img
                                                                                                src='assets/img/map-g-1.png'
                                                                                                th:src="@{${'/resources/assets/img/'+uploadImage.getMapIcon()}}"
                                                                                                class=''
                                                                                                th:title="${uploadImage.getMapTitle()}"
                                                                                                alt="bot_img"
                                                                                                style="width:28px;
                                                                                                height:28px;"
                                                                                        ></span>
                                                        </a>
                                                        <!--map icon-->
                                                    </a>
                                                </div>


                                            </div>
                                        </div>

                                    </div>


                                </div>
                            </div>

                        </div>

                    </div>

                </div>

            </div>
        </div>
    </div>

</div>

