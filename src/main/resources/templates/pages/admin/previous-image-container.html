<div xmlns:th="http://www.thymeleaf.org">
    <div class="card">
        <div class="card-header" id="headingTwo">
            <p class="mb-0">
                <button aria-controls="collapseTwo"
                        aria-expanded="false"
                        class="btn btn-sm btn-link btn-block text-left collapsed"
                        data-bs-target="#collapseTwo"
                        data-bs-toggle="collapse"
                        type="button" style="font-weight: 600;">
                    Previous Photos
                </button>
            </p>
        </div>
        <div aria-labelledby="headingTwo" class="collapse"
             data-parent="#accordionExample" id="collapseTwo">
            <div class="card-body"
                 style="overflow-y: scroll; height: 400px;">
                <div th:each="prevCallImageVideo,iStat: ${agentVideoForm.getPrevCallImageVideoList()}"
                     style="margin-bottom: 6px;">
                    <div id="accordionImage">
                        <div class="card">
                            <div class="card-header"
                                 id="accordionImageHeadingOne">
                                <div data-toggle="collapse"
                                     style="cursor: pointer;"
                                     th:data-target="'#accordionImageCollapse' + ${iStat.index}"
                                     aria-expanded="true"
                                     th:aria-controls="'accordionImageCollapse' + ${iStat.index}">
                                    <div style="width: 100%; display: flex; flex-direction: row; justify-content: space-between;">
                                        <div style="display: flex;flex-direction: row; flex-wrap: wrap; gap: 18px; align-items: center; width: 100%">
                                            <div style="flex: 0 0 30%;" class="d-flex">
                                                <div class="mr-auto p-0">
                                                    <small>
                                                        <i class="fa-solid fa-list" style="width: 12px;height: 12px;color: #1d4ca0;"></i>
                                                        <span style="font-size: 12px; font-weight: 600; color: #1d4ca0; margin-left: 8px;"
                                                              th:text="${prevCallImageVideo.getTitle()}">1st Call Photos</span>
                                                        <!--                                                                                                &nbsp;<span th:text="${prevCallImageVideo.getTitle()+' Call Detail'}">1st Call Photos</span>-->
                                                    </small>
                                                </div>
                                            </div>
                                            <div style="flex: 0 0 30%;" class="d-flex">
                                                <div class="mr-auto p-0">
                                                    <small>
                                                        <i class="fa-solid fa-user" style="width: 12px;height: 12px;color: #1d4ca0;"></i>
                                                        <span style="font-size: 12px; font-weight: 600; color: #1d4ca0; margin-left: 8px;"
                                                              th:text="${prevCallImageVideo.getCallDetailsDto().getCreatedBy()}">jhone
                                                                                                                                    Doe</span>
                                                    </small>
                                                </div>


                                            </div>
                                            <div style="flex: 0 0 30%;" class="d-flex">
                                                <div class="p-0">
                                                    <small>
                                                        <i class="fa-solid fa-calendar-days" style="width: 12px;height: 12px;color: #1d4ca0;"></i>
                                                        <span style="font-size: 12px; font-weight: 600; color: #1d4ca0; margin-left: 8px;"
                                                              th:text="${#temporals.format(prevCallImageVideo.getCallDetailsDto().tokenGenerateDateTime, 'MMM dd, yyyy hh:mm a')}">Dec
                                                                                                                                05,2020 | 9.59PM</span>
                                                    </small>
                                                </div>
                                            </div>
                                        </div>

                                    </div>
                                </div>
                            </div>

                            <div style="display: flex;flex-wrap: wrap; gap: 12px; padding: 8px;">
                                <div style="flex: 0 0 calc(50% - 6px);"
                                     th:each="uploadDocumentTypeDto,uploadDocumentTypeStat: ${prevCallImageVideo.getUploadDocumentTypeList()}"
                                     th:id="${'prevImageContainer'+prevCallImageVideo.getCallIndex()}">
                                    <div class="card-body" style="padding: 4px 12px 12px 12px !important;background: #fafafa;border-radius: 4px;" >
                                        <span class="data-details-span" th:text="${uploadDocumentTypeDto.getDocumentTypeName()}" style="font-weight: 600;font-size: 13px; color: #575757;">Passport</span>
                                        <div class="inspection-img-row">
                                            <div class="inspection-img-div"
                                                 th:each="uploadImage,imageStat: ${uploadDocumentTypeDto.getUploadImageList()}">
                                                <!-- Delete icon-->
                                                <div class="img-wrap">
                                                                                <span class="close"
                                                                                      th:onclick="|deleteBackendImage('${uploadImage.uploadImageDetailsRefNo}','${prevCallImageVideo.getCallIndex()}')|"><img
                                                                                        src="assets/img/close.svg"
                                                                                        style="width: 16px; text-align: center"
                                                                                        th:src="@{/resources/assets/img/close.svg}"/></span>
                                                    <a data-magnify='gallery'
                                                       th:href="@{'/api/v1/image/view-original-image/'+${uploadImage.getUploadImageDetailsRefNo()}}"
                                                       th:data-ref-no="${uploadImage.uploadImageDetailsRefNo}">

                                                        <div class="card ">
                                                            <div class="">
                                                                <img alt="Responsive image"
                                                                     class="img-thumbnail"
                                                                     style=""
                                                                     src='assets/img/image.png'
                                                                     th:src="@{'/api/v1/image/view-thumb-image/'+${uploadImage.uploadImageDetailsRefNo}}"
                                                                     th:data-ref-no="${uploadImage.uploadImageDetailsRefNo}"
                                                                >
                                                            </div>
                                                        </div>

                                                        <!--  edit icon-->
                                                        <a style="color: white">
                                                                                             <span class="edit-img-icon"
                                                                                                   th:onclick="|openDrawingModal('${uploadImage.uploadImageDetailsRefNo}','${uploadDocumentTypeDto.documentTypeId}')|">
                                                                                                   <i class="fa fa-pencil"
                                                                                                      aria-hidden="true"></i>
                                                                                             </span>
                                                        </a>

                                                        <!--map icon-->
                                                        <a href="#"
                                                           style="color: white"
                                                           th:onclick="|showMapModal('${uploadImage.getLatitudeVal()}','${uploadImage.getLongitudeVal()}')|">
                                                                                        <span class="bot-map"><img
                                                                                                src='assets/img/map-g-1.png'
                                                                                                th:src="@{${'/resources/assets/img/'+uploadImage.getMapIcon()}}"
                                                                                                class=''
                                                                                                th:title="${uploadImage.getMapTitle()}"
                                                                                                alt="bot_img"
                                                                                                style="width:28px;
                                                                                                height:28px;"
                                                                                        ></span>
                                                        </a>
                                                        <!--map icon-->
                                                    </a>
                                                </div>


                                            </div>
                                        </div>

                                    </div>


                                </div>
                            </div>

                        </div>

                    </div>

                </div>

            </div>
        </div>
    </div>

</div>

