<div xmlns:th="http://www.thymeleaf.org">
    <div th:each="uploadDocumentTypeDto,uploadDocumentTypeStat: ${prevCallImageVideo.getUploadDocumentTypeList()}">
        <span class="data-details-span" th:text="${uploadDocumentTypeDto.getDocumentTypeName()}" style="font-weight: 600;font-size: 13px; color: #575757;">Passport</span>
        <div class="inspection-img-row">
            <div class="inspection-img-div"
                 th:each="uploadImage,imageStat: ${uploadDocumentTypeDto.getUploadImageList()}">
                <!-- Delete icon-->

                <div class="img-wrap">
                                                                                <span class="close"
                                                                                      th:data-ref-no="${uploadImage.uploadImageDetailsRefNo}"
                                                                                      th:data-sub-document-type-id="${prevCallImageVideo.callIndex}"
                                                                                      th:onclick="|deleteBackendImage('${uploadImage.uploadImageDetailsRefNo}','${prevCallImageVideo.getCallIndex()}')|"><img
                                                                                        src="assets/img/close.svg"
                                                                                        style="width: 16px; text-align: center"
                                                                                        th:src="@{/resources/assets/img/close.svg}"/></span>
                    <a data-magnify='gallery'
                       th:href="@{'/api/v1/image/view-original-image/'+${uploadImage.getUploadImageDetailsRefNo()}}"
                       th:data-ref-no="${uploadImage.uploadImageDetailsRefNo}">
                        <div class="card ">
                            <div class="card-body">
                                <img alt="Responsive image"
                                     class="img-fluid"
                                     src='assets/img/image.png'
                                     th:src="@{'/api/v1/image/view-thumb-image/'+${uploadImage.getUploadImageDetailsRefNo()}}"
                                     th:data-ref-no="${uploadImage.uploadImageDetailsRefNo}"
                                >
                            </div>
                        </div>

                        <!--  edit icon-->
                        <a style="color: white">
                         <span class="edit-img-icon"
                               th:onclick="|openDrawingModal('${uploadImage.uploadImageDetailsRefNo}','${uploadDocumentTypeDto.documentTypeId}')|">
                               <i class="fa fa-pencil" aria-hidden="true"></i>
                         </span>
                        </a>

                        <!--map icon-->
                        <a href="#"
                           style="color: white"
                           th:onclick="|showMapModal('${uploadImage.latitudeVal}','${uploadImage.longitudeVal}')|">
                         <span class="bot-map">
                             <img
                                     src='assets/img/map-g-1.png'
                                     th:src="@{${'/resources/assets/img/'+uploadImage.mapIcon}}"
                                     class='map-icon'
                                     th:title="${uploadImage.mapTitle}"
                                     alt="bot_img"
                                     style="width:28px;
                                                                                                height:28px;">
                         </span>
                        </a>
                        <!--map icon-->

                    </a>
                </div>
            </div>
        </div>
        <hr>
    </div>

</div>

