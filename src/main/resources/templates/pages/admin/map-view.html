<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
    <style>
        /* Set the size of the div element that contains the map */
        #map {
            height: 400px; /* The height is 400 pixels */
            width: 100%; /* The width is the width of the web page */
        }
    </style>
</head>
<body>

<!--The div element for the map -->
<div id="map"></div>
<input id="latitude" name="latitude" th:value="${latitude}" type="hidden"/>
<input id="longitude" name="longitude" th:value="${longitude}" type="hidden"/>
<script>
    // Initialize and add the map
    function initMap() {
        let latitude = document.getElementById("latitude").value;
        let longitude = document.getElementById("longitude").value;
        let uluru = {lat: Number(latitude), lng: Number(longitude)};
        let map = new google.maps.Map(document.getElementById('map'), {zoom: 15, center: uluru});
        let marker = new google.maps.Marker({position: uluru, map: map});
    }
</script>

<script async defer
        src="https://maps.googleapis.com/maps/api/js?key=AIzaSyC18dN7JBN3vtEqLepEWaC8itfDYw8uWhY&callback=initMap"
        th:inline="javascript">
</script>
</body>
</html>
