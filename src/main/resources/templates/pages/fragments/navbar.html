<!doctype html>
<html lang="en" xmlns:sec="http://www.w3.org/1999/xhtml" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" name="viewport"/>

    <title>OnMISite</title>
    <link href="main.css" rel="stylesheet" th:href="@{/resources/main.css}">
    <link href="assets/css/font.css" rel="stylesheet" th:href="@{/resources/assets/css/font.css}">

    <script src="assets/js/jquery.min.js" th:src="@{/js/jquery/jquery.js}"></script>

    <style> .topbar-divider {
        width: 0;
        border-right: 1px solid #e3e6f0;
        height: calc(4.375rem - 2rem);
        margin: auto 1rem;
    }</style>

</head>


<th:block th:fragment="navbar">

    <!-- Navigation Bar -->
    <nav class="navbar navbar-expand-lg navbar-light bg-white  shadow fixed-top ">

        <!-- Mini Logo -->

        <div class="d-sm-inline-block  mr-auto  mw-100  img-left "><img src="assets/img/onMi-Logo.png"
                                                                        style="width: 70%;"
                                                                        th:src="@{/resources/assets/img/on_misite_logo.png}">


        </div>
        <button class="navbar-toggler" type="button" data-toggle="collapse" data-target="#navbarSupportedContent" aria-controls="navbarSupportedContent" aria-expanded="false" aria-label="Toggle navigation">
            <span class="navbar-toggler-icon"></span>
        </button>
        <!-- Header Navigation -->
        <div class="collapse navbar-collapse justify-content-end" id="navbarSupportedContent">
        <ul class="navbar-nav  ml-auto  ">

            <li class="nav-item  mx-lg-5 mt-2 " data-toggle="tooltip"
                sec:authorize="hasAnyRole('ROLE_SUPER_ADMIN','ROLE_COMPANY_ADMIN','ROLE_AGENT_USER')" title="Home">
                <button class="btn btn-light btn-sm" onclick="onLoadHome()" style="border: 1px solid #428bfa"
                        type="button">
                    <img class="mi-top-icn" data-placement="top" src="assets/img/home.svg"
                         th:src="@{/resources/assets/img/home.svg}">
                </button>

            </li>


            <!-- Add User -->
            <li class="nav-item  mx-3 mt-2 " data-toggle="tooltip"
                sec:authorize="hasAnyRole('ROLE_SUPER_ADMIN','ROLE_COMPANY_ADMIN')" title="Add New User">
                <button class="btn btn-light btn-sm" onclick="onLoadUser()" type="button">
                    Add New User
                    <!--<span class="sr-only">unread messages</span>-->
                </button>
            </li>

            <li class="nav-item  mx-3 mt-2 " data-toggle="tooltip"
                sec:authorize="hasAnyRole('ROLE_SUPER_ADMIN','ROLE_COMPANY_ADMIN')" title="View User List">
                <button class="btn btn-light btn-sm" onclick="onLoadUserList()" type="button"> User List
                    <span class="badge badge-danger rounded-pill" id="userCountSpan">5</span>
                </button>
            </li>


            <!-- Direct Jobs -->
            <li class="nav-item  mx-3 mt-2" data-toggle="tooltip"
                sec:authorize="hasAnyRole('ROLE_SUPER_ADMIN','ROLE_COMPANY_ADMIN','ROLE_AGENT_USER')"
                title="Add Direct Job">
                <button class="btn btn-light btn-sm" onclick="onLoadDirectJob()" type="button">Add Direct Job</button>
            </li>
            <!-- Direct Jobs List -->
            <li class="nav-item  mx-3 mt-2" data-toggle="tooltip"
                sec:authorize="hasAnyRole('ROLE_SUPER_ADMIN','ROLE_COMPANY_ADMIN','ROLE_AGENT_USER')"
                title="View Job List">
                <button class="btn btn-light btn-sm" onclick="onLoadDirectJobList()" type="button">View Job List
                    <span class="badge badge-danger rounded-pill" id="jobCountSpan">5</span>
                </button>
            </li>

            <div class="topbar-divider d-none d-sm-block ml-3"></div>

            <!-- Right Side Admin details -->
            <li class="nav-item dropdown no-arrow"
                sec:authorize="hasAnyRole('ROLE_SUPER_ADMIN','ROLE_COMPANY_ADMIN','ROLE_AGENT_USER')">
                <a aria-expanded="false" aria-haspopup="true" class="nav-link dropdown-toggle" data-toggle="dropdown"
                   href="#" id="userDropdown" role="button">

                    <img class="img-profile rounded-circle"
                         src="assets/img/onMiUser.png" th:src="@{/resources/assets/img/onMiUser.png}"
                         style="width: 35px">
                    <span class="mr-3 d-none d-lg-inline text-gray-600 small"
                          th:text="${session?.user?.getUsername()}">&nbsp;Super Admin</span>
                </a>
                <!-- Profile-->
                <div aria-labelledby="userDropdown"
                     class="dropdown-menu dropdown-menu-right shadow animated--grow-in">
                    <a class="dropdown-item" href="#">
                        <i class="fas fa-user fa-sm fa-fw mr-2 text-gray-400"></i>
                        Profile
                    </a>
                    <a class="dropdown-item" href="#" th:href="@{/api/v1/users/password/change}">
                        <i class="fas fa-lock fa-sm fa-fw mr-2 text-gray-400"></i>
                        Password
                    </a>
                    <!-- Logout-->
                    <div class="dropdown-divider"></div>
                    <form id="formLogout" method="post">
                        <input
                                th:name="${_csrf.parameterName}"
                                id="_csrf"
                                th:value="${_csrf.token}"
                                type="hidden"/>
                        <a class="dropdown-item" href="#" onclick="logout()">
                            <i class="fas fa-sign-out-alt fa-sm fa-fw mr-2 text-gray-400"></i>
                            Sign Out
                        </a>
                    </form>
                </div>
            </li>

        </ul>

</div>
    </nav>

    <script>$(function () {
        $('[data-toggle="tooltip"]').tooltip()
    })</script>
    <!-- End of Topbar -->

    <script th:inline="javascript">
        /*<![CDATA[*/
        function onLoadHome() {
            let url =/*[[@{/home}]]*/ 'test';
            window.location.assign(url);
        }

        function onLoadDirectJob() {
            let url =/*[[@{/api/v1/jobs/detail}]]*/ 'test';
            window.location.assign(url);
        }


        function onLoadUsage() {
            let url =/*[[@{/api/v1/test/detail}]]*/ 'test';
            window.location.assign(url);
        }
        function onLoadDirectJobList() {
            let url =/*[[@{/api/v1/jobs/list}]]*/ 'test';
            window.location.assign(url);
        }

        function onLoadUser() {
            let url =/*[[@{/api/v1/users/detail}]]*/ 'test';
            window.location.assign(url);
        }

        function onLoadUserList() {
            let url =/*[[@{/api/v1/users/list}]]*/ 'test';
            window.location.assign(url);
        }

        function logout() {
            let url =/*[[@{/logout}]]*/ 'test';
            document.getElementById("formLogout").action = url;
            document.getElementById("formLogout").submit();
        }

        const setCountDetails = () => {
            let contextRoot = /*[[@{/}]]*/ '';
            let csrfToken = document.getElementById('_csrf').value;
            let url = contextRoot + 'api/v1/dashboard/count';
            let data = {}
            let request = new Request(url, {
                method: 'POST',
                body: JSON.stringify(data),
                headers: new Headers({
                    'Accept': 'application/json',
                    'Content-Type': 'application/json; charset=UTF-8;x-www-form-urlencoded',
                    'X-CSRF-TOKEN': csrfToken
                })
            });

            fetch(request)
                .then(function (res) {
                    // Handle response you get from the server
                    res.json().then(dto => {
                        $('#jobCountSpan').text(dto.jobCount)
                        $('#userCountSpan').text(dto.userCount)
                    })

                });

        }
        setCountDetails();

        /*]]>*/
    </script>

</th:block>
<script src="node_modules/bootstrap/dist/js/bootstrap.bundle.js" th:src="@{/js/bootstrap/bootstrap.bundle.js}"></script>

</html>
