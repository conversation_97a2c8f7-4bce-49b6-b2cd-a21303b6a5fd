<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" name="viewport"/>
    <link href="/static/assets/global/image/favico.png" rel="shortcut icon"
          th:href="@{/resources/assets/img/Mini-Logo.png}"
          type="images/favico.png"/>

    <title>OnMISite</title>
    <link href="main-styles.css" rel="stylesheet" th:href="@{/resources/main-styles.css}">

    <link href="node_modules/bootstrap/dist/css/bootstrap.min.css" rel="stylesheet" th:href="@{/css/bootstrap/bootstrap.min.css}">
    <script src="node_modules/bootstrap/dist/js/bootstrap.bundle.min.js" th:src="@{/js/bootstrap/bootstrap.bundle.min.js}"></script>

    <link href="node_modules/@fortawesome/fontawesome-free/css/all.min.css" rel="stylesheet" th:href="@{/css/fontawesome/all.min.css}">
    <script src="node_modules/@fortawesome/fontawesome-free/js/all.min.js" th:src="@{/js/fontawesome/all.min.js}"></script>


    <link href="main.css" rel="stylesheet" th:href="@{/resources/main.css}">
    <link href="assets/css/font.css" rel="stylesheet" th:href="@{/resources/assets/css/font.css}">
    <link href="assets/css/font.css" rel="stylesheet" th:href="@{/js/jquery.magnify/jquery.magnify.min.css}">
    <script src="assets/js/jquery.min.js" th:src="@{/js/jquery/jquery.js}"></script>
<!--    <script src="assets/js/fontawesome.js" th:src="@{/resources/assets/js/fontawesome.js}"></script>-->

    <script th:inline="javascript">
        let contextRoot = /*[[@{/}]]*/ '';
        let facingMode = [[${session.facingMode}]];
        let accessToken = [[${agentVideoForm.callDetailsDto.accessToken}]];

        //facingMode="user";
        //alert(facingMode);
    </script>
    <style>
        .on-mi-alert-show-counter .toast {
            z-index: 99999;
            right: 0;
            top: 5px;
            position: absolute;
            left: 17px;
            display: block;
        }
        .blob {
            background: black;
            border-radius: 50%;
            box-shadow: 0 0 0 0 rgba(0, 0, 0, 1);
            margin: 0px;
            height: 20px;
            width: 20px;
            transform: scale(1);
            animation: pulse-black 2s infinite;
        }
        .blob.red {
            background: rgba(255, 82, 82, 1);
            box-shadow: 0 0 0 0 rgba(255, 82, 82, 1);
            animation: pulse-red 2s infinite;
        }

        @keyframes pulse-red {
            0% {
                transform: scale(0.95);
                box-shadow: 0 0 0 0 rgba(255, 82, 82, 0.7);
            }

            70% {
                transform: scale(1);
                box-shadow: 0 0 0 10px rgba(255, 82, 82, 0);
            }

            100% {
                transform: scale(0.95);
                box-shadow: 0 0 0 0 rgba(255, 82, 82, 0);
            }
        }
        .nav-scroller {
            position: relative;
            z-index: 2;

            /*overflow-y: hidden;*/
        }

        .nav-scroller .nav {
            display: -webkit-box;
            display: -ms-flexbox;
            display: flex;
            -ms-flex-wrap: nowrap;
            flex-wrap: nowrap;
            overflow-x: auto;
            white-space: nowrap;
            -webkit-overflow-scrolling: touch;
        }


    </style>

</head>
<body style="
<!--    overflow-y: hidden;-->
">
<form id="sessionEndForm"></form>
<!--On MI Site Page Main Wrapper  -->
<div id="wrapper">
    <!-- Content-->
    <div class="d-flex flex-column" id="content-wrapper">
        <!-- Header Top Details-->
        <div class="nav-scroller py-0 mb-2" style="position: fixed; width: 100%; z-index: 1000 !important;">
            <div class="shadow-sm">
                <nav class="navbar navbar-expand-md p-0" style="background: #ffffff;">
                    <div class="container-fluid" style="margin-top: 0 !important;">
                        <!-- Logo -->
                        <div class="navbar-brand p-0" style="padding-left: 8px !important;">
                            <img src="assets/img/hnblogo.png" style="width: 70px;" th:src="@{/resources/assets/img/hnblogo.png}" />
                        </div>

                        <!-- Mobile Toggle Button -->
                        <button class="btn btn-sm btn-outline-secondary d-md-none" type="button" data-bs-toggle="collapse" data-bs-target="#mobileNavDetails"
                                aria-controls="mobileNavDetails" aria-expanded="false" aria-label="Toggle details"
                        style="width: 34px; height: 34px; margin-left: 104px; position: absolute;">
                            <i class="fa-solid fa-bars"></i>
                        </button>

                        <!-- Navigation Items (Hidden on Mobile, Visible on Desktop) -->
                        <div class="d-none d-md-flex flex-row align-items-center" style="gap: 32px;">
                            <div class="nav-info-container">
                                <div class="nav-info-img">
                                    <i class="fa-solid fa-tag nav-icon" style="transform: rotate(90deg);"></i>
                                </div>
                                <div class="nav-info-child-container">
                                    <span class="info-label">Job No.</span>
                                    <span class="info-detail" th:text="${agentVideoForm.jobDetailDto.jobNumber}">1234567890</span>
                                </div>
                            </div>

                            <div class="nav-info-container">
                                <div class="nav-info-img">
                                    <i class="fa-solid fa-user nav-icon"></i>
                                </div>
                                <div class="nav-info-child-container">
                                    <span class="info-label">Customer Name</span>
                                    <span class="info-detail" th:text="${agentVideoForm.jobDetailDto.insuredName}">Chinthaka Perera</span>
                                </div>
                            </div>

                            <div class="nav-info-container">
                                <div class="nav-info-img">
                                    <i class="fa-solid fa-calendar-day nav-icon"></i>
                                </div>
                                <div class="nav-info-child-container">
                                    <span class="info-label">Reported Date</span>
                                    <span class="info-detail" th:text="${#temporals.format(agentVideoForm.callDetailsDto.tokenGenerateDateTime, 'MMMM dd, yyyy hh:mm a')}">
                                03-12-2020 | 04:04 PM
                            </span>
                                </div>
                            </div>
                        </div>

                        <!-- Right Side (Connection State & End Button) -->
                        <div class="d-flex flex-row align-items-center" style="gap: 16px;">
                            <div class="d-flex flex-column align-items-center" style="gap: 6px;">
                                <span class="badge text-bg-warning" id="connectionStateText" style="width: 104px; font-size: 11px !important;height: 20px; display: flex; justify-content: center; align-items: center;">
                                    Pending
                                </span>
                                <span class="badge text-bg-success" id="signalInfoBadge" style="width: 104px; font-size: 11px !important;height: 20px; display: flex; justify-content: center; align-items: center;">
                                    <span id="signalInfoText" style="font-size: 11px !important;">NA</span>
                                    <img src="assets/img/signal.svg" style="width: 12px; height: 12px; margin-left: 4px;" th:src="@{/resources/assets/img/signal.svg}" />
                                 </span>
                            </div>
                            <div style="padding-right: 12px;">
                                <button class="btn btn-light btn-sm" style="width: 20px; height: 20px; border-radius: 50%; display: flex;justify-content: center; align-items: center; padding: 0;" onclick="onSessionEnd()">
                                    <img class="mi-top-icn" src="assets/img/cancel.svg" style="width: 20px; height: 20px;" th:src="@{/resources/assets/img/cancel.svg}">
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Collapsible Mobile Details (Visible only when toggled) -->
                    <div class="collapse d-md-none" id="mobileNavDetails" style="border-top: 1px solid #f1f1f1; width: 100%;">
                        <div class="d-flex flex-column align-items-start p-2" style="gap: 12px;">
                            <div class="nav-info-container">
                                <div class="nav-info-img">
                                    <i class="fa-solid fa-tag nav-icon" style="transform: rotate(90deg);"></i>
                                </div>
                                <div class="nav-info-child-container">
                                    <span class="info-label">Job No.</span>
                                    <span class="info-detail" th:text="${agentVideoForm.jobDetailDto.jobNumber}">1234567890</span>
                                </div>
                            </div>

                            <div class="nav-info-container">
                                <div class="nav-info-img">
                                    <i class="fa-solid fa-user nav-icon"></i>
                                </div>
                                <div class="nav-info-child-container">
                                    <span class="info-label">Customer Name</span>
                                    <span class="info-detail" th:text="${agentVideoForm.jobDetailDto.insuredName}">Chinthaka Perera</span>
                                </div>
                            </div>

                            <div class="nav-info-container">
                                <div class="nav-info-img">
                                    <i class="fa-solid fa-calendar-day nav-icon"></i>
                                </div>
                                <div class="nav-info-child-container">
                                    <span class="info-label">Reported Date</span>
                                    <span class="info-detail" th:text="${#temporals.format(agentVideoForm.callDetailsDto.tokenGenerateDateTime, 'MMMM dd, yyyy hh:mm a')}">
                                03-12-2020 | 04:04 PM
                            </span>
                                </div>
                            </div>
                        </div>
                    </div>
                </nav>
            </div>
        </div>


        <!--        <div class="nav-scroller py-0 mb-2 ">-->
<!--            <div class="card shadow-sm">-->
<!--                <nav class="nav d-flex justify-content-between ">-->
<!--                    <p class="p-2 font-weight-500"><a href="#">-->
<!--                        <button class="btn btn-light btn-sm mx-2 mt-3" onclick="onSessionEnd()"-->
<!--                                style="border: 1px solid red" type="button">-->
<!--                            <img class="mi-top-icn" src="assets/img/cancel.svg"-->
<!--                                 th:src="@{/resources/assets/img/cancel.svg}">-->
<!--                        </button>-->
<!--                    </a></p>-->
<!--                    <p class="p-2 font-weight-500">-->
<!--                        <span class="badge badge-warning" id="connectionStateText">Pending</span>-->
<!--                        <br/>-->
<!--                        <span class="badge badge-danger float-lg-right" id="signalInfoBadge"><span id="signalInfoText">NA</span> &nbsp;<img-->
<!--                                src="assets/img/signal.svg" style="width: 10px;"-->
<!--                                th:src="@{/resources/assets/img/signal.svg}"/></span>-->
<!--                    </p>-->
<!--                    <p class="p-2  font-weight-500"><img src="assets/img/tag.svg"-->
<!--                                                         style="width: 16px;"-->
<!--                                                         th:src="@{/resources/assets/img/tag.svg}"/>&nbsp;Job-->
<!--                        No:-->
<!--                        </br><span class="on-dtl" th:text="${agentVideoForm.jobDetailDto.jobNumber}">1234567890</span>-->
<!--                    </p>-->
<!--                    <p class="p-2 font-weight-500" style="display: none;"><img src="assets/img/id-card.svg"-->
<!--                                                                               style="width: 16px;"-->
<!--                                                                               th:src="@{/resources/assets/img/id-card.svg}"/>&nbsp;Id.-->
<!--                        No:-->
<!--                        <br><span class="on-dtl" th:text="${agentVideoForm.jobDetailDto.idNo}">123456789V</span></p>-->
<!--                    <p class="p-2 font-weight-500"><img src="assets/img/user.svg"-->
<!--                                                        style="width: 16px;"-->
<!--                                                        th:src="@{/resources/assets/img/user.svg}"/>&nbsp;Customer-->
<!--                        Name:<br>-->
<!--                        <span class="on-dtl" th:text="${agentVideoForm.jobDetailDto.informerName}">&nbsp;Chinthaka Perera</span>-->
<!--                    </p>-->
<!--                    <p class="p-2 font-weight-500" style="display: none;"><img src="assets/img/old-typical-phone.svg"-->
<!--                                                                               style="width: 16px;"-->
<!--                                                                               th:src="@{/resources/assets/img/old-typical-phone.svg}"/>&nbsp;Mobile-->
<!--                        No:-->
<!--                        <br><span class="on-dtl"-->
<!--                                  th:text="${agentVideoForm.jobDetailDto.informerMobileNo}">************</span></p>-->
<!--                    <p class="p-2 font-weight-500"><img src="assets/img/calendar.svg"-->
<!--                                                        style="width: 16px;"-->
<!--                                                        th:src="@{/resources/assets/img/calendar.svg}"/>&nbsp;Reported-->
<!--                        Date: <br><span class="on-dtl"-->
<!--                                        th:text="${#temporals.format(agentVideoForm.callDetailsDto.tokenGenerateDateTime, 'MMMM dd, yyyy hh:mm a')}">03-12-2020|04:04pm</span>-->
<!--                    </p>-->
<!--                    <p class="p-2 font-weight-500" style="display: none;"><img src="assets/img/location.svg"-->
<!--                                                                               style="width: 16px;"-->
<!--                                                                               th:src="@{/resources/assets/img/location.svg}"/>&nbsp;Latitude:-->
<!--                        <br><span class="on-dtl" id="spanLatitude"></span></p>-->
<!--                    <p class="p-2 font-weight-500" style="display: none;"><img src="assets/img/location.svg"-->
<!--                                                                               style="width: 16px;"-->
<!--                                                                               th:src="@{/resources/assets/img/location.svg}"/>&nbsp;Longitude:<br><span-->
<!--                            class="on-dtl"-->
<!--                            id="spanLongitude"></span>-->
<!--                    </p>-->


<!--                </nav>-->
<!--            </div>-->
<!--        </div>-->


        <!-- End of Header Top Details-->
        <!-- Main Content -->
        <div class="on-site-main" id="content" style=" background: #fbfbfb; padding-top: 90px; height: 100vh;">
            <input
                    th:id="${_csrf.parameterName}"
                    th:name="${_csrf.parameterName}"
                    th:value="${_csrf.token}"
                    type="hidden"/>

            <input
                    id="localUser"
                    name="localUser"
                    th:value="${agentVideoForm.callDetailsDto.accessToken}"
                    type="hidden"/>
            <input
                    id="remoteUser"
                    name="remoteUser"
                    th:value="${agentVideoForm.callDetailsDto.getCreatedBy()+'-'+agentVideoForm.callDetailsDto.accessToken}"
                    type="hidden"/>
            <input
                    id="callId"
                    name="callId"
                    th:value="${agentVideoForm.callDetailsDto.callId}"
                    type="hidden"/>

            <input id="latitude" name="latitude" type="hidden" value=""/>
            <input id="longitude" name="longitude" type="hidden" value=""/>
            <div class="pl-5">
                <b></b><span id="geoLocation"></span>
            </div>
            <canvas id="canvas" style="display: none;width: 100%;height: 100%"></canvas>
            <!-- Begin Page Content -->
            <div class="container-fluid" style="
    height: calc(100vh - 90px);
">


                <!-- Page Heading -->

                <!-- Content Row -->

                <div class="row ">

                    <!-- Left Camera-->
                    <div class="col-12 col-lg-6 col-sm-12 offset-lg-3 offset-sm-0">
                        <div class="card  shadow-sm mb-3" style=" height: calc(100vh - 106px); background: #f9f9f9; border-radius: 8px !important;">
                            <div class="card-body " style="overflow-y: hidden; padding: 6px; overflow-x: hidden;">
                                <div class="row" style="height: 100%;">
                                    <!-- Direct Jobs -->
                                    <div class=" col-xl-12 col-lg-12 " style="height: 100%;">
                                        <!--  Counter  -->
                                        <div class="on-mi-alert-show-counter" id="recordingTimerContainer"
                                             style="display: none;">
                                            <div class="toast bg-dark show imageCaptureToast" style="display: block!important;" data-autohide="false">
                                                <div class="toast-body text-white">
                                                    Recording: <span id="hour">00</span> :
                                                    <span id="min">00</span> :
                                                    <span id="sec">00</span> :
                                                    <span id="milisec">00</span>
                                                    <span style="float: right;"><div class="blob red"></div></span>

                                                </div>
                                            </div>
                                        </div>
                                        <!--   Counter -->

                                        <!--  Image Capture  -->
                                        <div class="on-mi-alert-show-counter" id="imageCaptureTimerContainer"
                                             style="display: none;z-index: 9999">
                                            <div class="toast bg-dark show imageCaptureToast" style="display: block!important;" data-autohide="false">
                                                <div class="toast-body text-white">
                                                    Please wait... Image capture is processing...

                                                </div>
                                            </div>
                                        </div>
                                        <!--   Image Capture -->


                                        <div class="d-flex justify-content-center client-v-h-100" style="height: 100%;">
                                            <!--   Toast Alert -->
                                            <div class="on-mi-alert-show" id="toastContainer"></div>
                                            <!--   Toast Alert -->
                                            <div id="participants"></div>
                                        </div>
                                    </div>


                                </div>


                            </div>
                            <div class="card-footer-client-video">
                                <div class="d-flex flex-row justify-content-center">
                                    <div class="p-2" style="display: none">
                                        <button class="btn btn-light btn-circle on-speaker"
                                                data-bs-toggle="tooltip" data-bs-placement="top" id="btnSpeaker"
                                                data-bs-title="Speaker On/Off"><i class="fas fa-volume-up"></i>
                                        </button>
                                    </div>
                                    <div class="p-2">
                                        <button class="btn btn-primary btn-circle on-mute"
                                                data-bs-toggle="tooltip" data-bs-placement="top" id="btnOnMute"
                                                data-bs-title="Mute">
                                            <i class="fa-solid fa-microphone-lines"></i>
                                        </button>
                                    </div>

                                    <div class="p-2">
                                        <button class="btn btn-success btn-circle on-record"
                                                data-bs-toggle="tooltip" data-bs-placement="top" id="btnOnVideoStop"
                                                data-bs-title="Hide Video"
                                        >
                                            <i class="fa-solid fa-video"></i>
                                        </button>
                                    </div>
                                    <div class="p-2">

                                        <button class="btn btn-secondary btn-circle "
                                                data-bs-toggle="tooltip" data-bs-placement="top" id="btnSwitchCamera"
                                                data-bs-title="Switch Camera">
                                            <i class="fa-solid fa-camera-rotate"></i>
                                        </button>

                                    </div>
                                    <div class="p-2">
                                        <button class="btn btn-danger btn-circle on-hangup"
                                                data-bs-toggle="tooltip" data-bs-placement="top"
                                                data-bs-title="End Session"
                                                id="btnOnHangUp">
                                            <i class="fas fa-phone" style="transform: rotate(135deg); margin-top: 2px;"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>



                    <!-- Content Row -->

                </div>
                <!-- /.container -->

            </div>
            <!-- End of Main Content -->


            <!--end of Bottom Camera-->


            <!--Bottom Camera action-->
<!--            <div class="row " style="display: none;">-->
<!--                <div class=" col-xl-2 col-lg-2  ">-->
<!--                </div>-->
<!--                <div class=" col-xl-8 col-lg-8  ">-->
<!--                    <div class="row">-->
<!--                        <div class=" col-xl-12 col-lg-12 ">-->
<!--                            <div class="d-flex justify-content-center ">-->
<!--                                <div class="on-bot-crd">-->
<!--                                    <div class="on-sub-bot">-->
<!--                                        <div class="d-flex justify-content-center mt-0">-->
<!--                                            <div class="d-flex flex-row">-->
<!--                                                <div class="p-2" style="display: none">-->
<!--                                                    <button class="btn btn-light btn-circle btn-circle-lg m-1 on-speaker"-->
<!--                                                            data-placement="top"-->
<!--                                                            data-toggle="tooltip" id="btnSpeaker"-->
<!--                                                            title="Speaker On/Off"><i class="fas fa-volume-up"></i>-->
<!--                                                    </button>-->
<!--                                                </div>-->
<!--                                                <div class="p-2">-->
<!--                                                    <button class="btn btn-light btn-circle btn-circle-lg m-1 on-mute"-->
<!--                                                            data-placement="top"-->
<!--                                                            data-toggle="tooltip" id="btnOnMute"-->
<!--                                                            title="Mute / Un-Mute"><i class="fas fa-microphone-alt"></i>-->
<!--                                                    </button>-->
<!--                                                </div>-->
<!--                                                <div class="p-2">-->
<!--                                                    <button class="btn btn-danger btn-circle btn-circle-lg m-1 on-hangup"-->
<!--                                                            data-placement="top"-->
<!--                                                            data-toggle="tooltip" id="btnOnHangUp"-->
<!--                                                            title="End Session"><i class="fas fa-phone"></i>-->
<!--                                                    </button>-->
<!--                                                </div>-->
<!--                                                <div class="p-2">-->
<!--                                                    <button class="btn btn-light btn-circle btn-circle-lg m-1 on-record"-->
<!--                                                            data-placement="top"-->
<!--                                                            data-toggle="tooltip" id="btnOnVideoStop"-->
<!--                                                            title="Show/Hide Video"-->
<!--                                                    ><i class="fas fa-video"></i>-->
<!--                                                    </button>-->
<!--                                                </div>-->
<!--                                                <div class="p-2">-->

<!--                                                    <button class="btn btn-light btn-circle btn-circle-lg m-1 "-->
<!--                                                            data-placement="top"-->
<!--                                                            data-toggle="tooltip" id="btnSwitchCamera"-->
<!--                                                            title="Front Camera"><span><img-->
<!--                                                            src="assets/img/rotate-camera.svg"-->
<!--                                                            style="width: 50%"-->
<!--                                                            th:src="@{/resources/assets/img/rotate-camera.svg}"/>  </span>-->
<!--                                                    </button>-->

<!--                                                </div>-->
<!--                                            </div>-->
<!--                                        </div>-->
<!--                                    </div>-->
<!--                                </div>-->
<!--                            </div>-->
<!--                        </div>-->
<!--                    </div>-->
<!--                </div>-->
<!--                &lt;!&ndash; Content Row &ndash;&gt;-->
<!--            </div>-->
            <!--end of Bottom Camera action-->

        </div>
        <!-- End of Content  -->

    </div>
    <!-- On MI Site Page Main Wrapper -->

</div>


<!--Page loading-->
<div class="loader-wrapper">
    <div class="spinner-border text-primary" role="status">

    </div>
</div>
<!--End of Page loading-->

<!--Toast-->
<script>
    $(document).ready(function() {
        initTwilio();
        $(".toast").toast('show');
    });
</script>
<script>
    document.addEventListener("DOMContentLoaded", function () {
      const tooltipTriggerList = document.querySelectorAll('[data-bs-toggle="tooltip"]');
      const tooltipList = [...tooltipTriggerList].map(tooltipTriggerEl => new bootstrap.Tooltip(tooltipTriggerEl));
    });
</script>
<script>
    $(window).on("load", function () {
        $(".loader-wrapper").fadeOut("slow");
    });
</script>
<!--tooltip-->
<script>
    $(function () {
        $('[data-toggle="tooltip"]').tooltip()
    })
</script>

<!--Toast-->
<script th:inline="javascript">
    function onSessionEnd() {
        handleLeave();
        let url =/*[[@{/api/v1/client/join/session-end}]]*/ 'test';
        window.location.assign(url + '/' + accessToken);
    }
</script>


<!-- JavaScript-->
<script src="//sdk.twilio.com/js/video/releases/2.8.0/twilio-video.min.js"></script>

<!--<script src="node_modules/bootstrap/dist/js/bootstrap.bundle.js" th:src="@{/js/bootstrap/bootstrap.bundle.js}"></script>-->
<script th:src="@{/resources/js/.env.js}" type="text/javascript"></script>
<script th:src="@{/resources/js/web-rtc/recording-timer.js}" type="text/javascript"></script>
<script th:src="@{/resources/js/web-rtc/remote/signalingchannel.js}" type="text/javascript"></script>
<script th:src="@{/resources/js/web-rtc/recording-timer.js}" type="text/javascript"></script>
<script th:src="@{/resources/js/toast/toast.js}" type="text/javascript"></script>
<script th:src="@{/resources/js/web-rtc/common.js}" type="text/javascript"></script>
<script th:src="@{/resources/js/web-rtc/update-backend.js}" type="text/javascript"></script>
<script th:src="@{/resources/js/web-rtc/geolocation.js}" type="text/javascript"></script>
<script th:src="@{/resources/js/web-rtc/remote/twilio-user.js}" type="text/javascript"></script>
<!--<script th:src="@{/resources/js/web-rtc/remote/user.js}" type="text/javascript"></script>-->
<script th:src="@{/resources/js/web-rtc/remote/image-capture.js}" type="text/javascript"></script>

</body>
</html>


