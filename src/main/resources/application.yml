spring:
  application:
    name: onmisite-app
  security:
    oauth2:
      resource-server:
        jwt:
          issuer-uri: https://hnbuwauth.misynergy.com/realms/hnb-general
          jwk-set-uri: ${spring.security.oauth2.resourceserver.jwt.issuer-uri}/protocol/openid-connect/certs
      client:
        registration:
          keycloak:
            client-id: mcms-onmisite-webapp-dev
            client-secret: itVB7paazXIyltmvNPapRN6mBbLDgold
            scope: openid,profile,email,roles
            redirect-uri: https://localhost/misyn/login/oauth2/code/keycloak
        provider:
          keycloak:
            issuer-uri: https://hnbuwauth.misynergy.com/realms/hnb-general
            jwk-set-uri: https://hnbuwauth.misynergy.com/realms/hnb-general/protocol/openid-connect/certs
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    hikari:
      max-lifetime: 580000
    url: *****************************************************************************************************************************************************************************
    username: hnbuw
    password: misyn8b
  main:
    allow-bean-definition-overriding: true
  jpa:
    hibernate:
      ddl-auto: update
      naming:
        physical-strategy: org.hibernate.boot.model.naming.CamelCaseToUnderscoresNamingStrategy
    properties:
      hibernate:
        show_sql: false
        format_sql: false
        dialect:  org.hibernate.dialect.MariaDBDialect
        connection:
          characterEncoding: utf-8
          CharSet: utf-8
          useUnicode: true,
  thymeleaf:
    prefix: classpath:/templates/


logging:
  pattern:
    console: "%d{dd-MM-yyyy HH:mm:ss.SSS} %magenta([%thread]) %highlight(%-5level) %logger.%M - %cyan(%msg%n)"
    file: "%d %-5level [%thread] %logger : %msg%n"
  level:
    com.misyn.onmisite: info
    org.springframework.web: info
    org.hibernate: info
  file:
    name: /opt/hnb/logs/mcms-onmisite-app.log

server:
  tomcat:
    max-http-form-post-size: 100MB
    remoteip:
      remote-ip-header: x-forwarded-for
      protocol-header: X-Forwarded-Proto

  servlet:
    session:
      timeout: 30m
    context-path: /misyn
  error:
    whitelabel:
      enabled: false
  ssl:
    key-store: classpath:mkyong.p12
    key-store-password: 123456789
    key-store-type: PKCS12
  port: 443

application:
  twilio:
    sid: AC81e97a2f7e48bbe6f7996e82916c225b
    key: **********************************
    secret: Itw8Zmf3DZTS0E0iK4jUr6kvrID6bD0q
    call-back-url: https://3847-220-247-246-105.ngrok-free.app/misyn/api/v1/twilio/webhook
  redis:
    host: localhost
    port: 6379
  mcms:
    post-logout-redirect-uri: https://localhost/misyn
  image:
    claim-document-directory: claim-documents
    path: E:/HNB/upload
  client:
    video-join-url: https://localhost/misyn/
    call-token-expired-time: 600


oci:
**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************

  user: ocid1.user.oc1..aaaaaaaavijhicoyamc6thzwypvu6n7l4kbeoej7ilg55ekvcqbeszllb4bq
  tenancy: ocid1.tenancy.oc1..aaaaaaaapzg2gukkootkfyw3ex2sd7omkmnqzronufzvnoezsutobwwo2lpa
  fingerprint: 30:4f:4f:b6:4f:f3:51:f3:0d:47:8e:9b:bc:90:43:40
  region: ap-singapore-1
  bucket-name: bucket-20250227-1510
