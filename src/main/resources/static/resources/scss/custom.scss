.bg {
  /* The image used */
  background-image: url("../images/loginbacknew.png");

  /* Full height */
  height: 100%;

  /* Center and scale the image nicely */
  background-position: center;
  background-repeat: no-repeat;
  background-size: cover;
}

.contentdiv {
  border: 2px solid #ccc;
  border-radius: 15px;
  min-height: 300px;
  margin-top: 128px;
}

.bgfooter {
  color: #b3b1a8;
  margin-bottom: 64px;
}

.footercontainer {
  padding: 64px 16px 32px 16px
}

.beigeback {
  background-color: #F5F5DC
}

.usagetile canvas {
  margin-left: 20px
}

.br-15 {
  border-radius: 10px;
}

.bgdash {
  background-color: #FAF9F3;
}

.darkmodebg {
  background-color: #0c0c0c !important;
}

.topheading {
  color: #a6b5c3;
}

#customnavbar {
  box-shadow: 0 4px 12px 0 rgba(0, 0, 0, 0.05);
  z-index: 500;
  display: block;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  transition: top 0.3s;
}

.edlogo {
  width: 60px;
  height: 60px;
}

.navbar {
  padding: 0 1rem 0 0 !important;
  background-color: #ff9d23 !important;
}

.navbar a {
  width: 100px;
  font-size: 20px;
  line-height: 20px;
}

.navbar-nav > li > a {
  padding: 24px !important;
  text-decoration: none;
  min-height: 88px;
}

.navbar-dark .navbar-nav .nav-link:focus, .navbar-dark .navbar-nav .nav-link:hover {
  background-color: #e27e01;
}

.navbar-dark .navbar-nav .nav-link:focus, .navbar-dark .navbar-nav .nav-link:hover {
  color: #ffffff;
}

.hide {
  display: none;
}

.popupname {
  position: absolute;
  top: 76px;
  right: 50%;
  transform: translate(50%, -50%);
  color: #ffffff;
}

@font-face {
  font-family: "ITCBook";
  src: url("../fonts/ITC-Officina-Sans-Std-Book-Font.otf");
}

@font-face {
  font-family: "ONMISITEBody";
  src: url("../fonts/ONMISITE-Regular.woff2");
}

@font-face {
  font-family: "ONMISITECondensedMedium";
  src: url("../fonts/ONMISITE-CondensedMedium.woff2");
}

body {
  font-family: ONMISITECondensedMedium;
}

body P {
  font-family: ONMISITEBody;
}

.container-fluid, .container-lg, .container-md, .container-sm, .container-xl {
  padding-right: 0;
  padding-left: 0;
}

.margin-bottom-m {
  margin-bottom: 20px;
}

.inline-block {
  display: inline-block;
}

.w {
  color: #fff;
}

.redbackbtn {
  background-color: #ff9d23 !important;
  border: 1px solid #ff9d23 !important;
  border-radius: 15px !important;

  &:hover {
    border: 1px solid #ff9d23 !important;
    border-radius: 15px;
  }
}

.orangebackbtn {
  background-color: #ff9d23 !important;
  border: 1px solid #ff9d23 !important;
  border-radius: 15px !important;

  &:hover {
    border: 1px solid #ff9d23 !important;
    border-radius: 15px;
  }
}

.dashbtn {
  border: 1px solid #cccccc !important;
  color: #aaaaaa !important;
  min-width: 100% !important;
  border-radius: 15px !important;

  &:hover {
    border: 1px solid #ff9d23 !important;
    border-radius: 15px !important;
  }

  &:focus {
    border: 1px solid #cccccc !important;
    border-radius: 15px !important;
  }
}

.formbtn {
  border: 1px solid #ff9d23 !important;
  color: #ff9d23 !important;
  width: 150px !important;
  min-width: 150px !important;
  border-radius: 15px !important;

  &:hover {
    border: 1px solid #ff9d23 !important;
    border-radius: 15px !important;
  }

  &:focus {
    border: 1px solid #cccccc !important;
    border-radius: 15px !important;
  }

  &:disabled {
    background-color: transparent !important;
    cursor: not-allowed;
  }
}

.tbllblsuccess {
  padding: 5px;
  background-color: green;
  border-radius: 15px;
  color: #43ff6e;
  min-width: 100px;
}

.tbllbldanger {
  padding: 5px;
  background-color: #dc3545;
  border-radius: 15px;
  color: #f8f9fa;
  min-width: 100px;
}

.tblbtn {
  min-width: 70px;
}

.tbldangerbtn{
    color: #dc3545;
    background-color: transparent !important;
    border-color: #dc3545;
    border-radius: 15px !important;

  &:hover {
    color: #fff;
    background-color: #c82333 !important;
    border-color: #bd2130 !important;
    border-radius: 15px;
  }
}

.tblsuccessbtn{
  color: #218838;
  background-color: transparent !important;
  border-color: #1e7e34;
  border-radius: 15px !important;

  &:hover {
    color: #fff;
    background-color: #218838 !important;
    border-color: #1e7e34 !important;
    border-radius: 15px;
  }
}

.select2-container--default .select2-selection--single .select2-selection__placeholder {
  color: #6c757d;
}

.select2-container--default .select2-selection--single {
  border: 1px solid #ced4da;
}

.select2-container--default .select2-selection--single .select2-selection__arrow {
  height: 36px;
}

.select2-container--default .select2-selection--single .select2-selection__rendered {
  line-height: 36px;
}

.select2-container .select2-selection--single {
  min-height: 38px;
}

.toggle.ios, .toggle-on.ios, .toggle-off.ios {
  border-radius: 20px;
}

.toggle.ios .toggle-handle {
  border-radius: 20px;
}

.btn-default {
  color: #333;
  background-color: #fff;
  border-color: #ccc;
}

.datepickreadonly {
  background-color: #fff !important;
}

.graybackform .help-block {
  color: #ff9d23
}

.graybackform .has-error input {
  border-bottom: 1px solid #ff9d23;
}

.redbackform .help-block {
  color: #fff703
}

.redbackform .has-error input {
  border-bottom: 1px solid #fff703;
}

.redbacklabel {
  color: rgba(255, 255, 255, .5);
}

.redbackinput {
  background-color: #ff9d23 !important;
  color: #ffffff;
  border-radius: 0;
  border-bottom: 1px solid #ffffff;
  border-top: none;
  border-right: none;
  border-left: none;
  padding: 0;

  &:focus {
    color: #ffffff;
    background-color: #ff9d23;
    border-radius: 0;
    border-bottom: 1px solid #ffffff;
    border-top: none;
    border-right: none;
    border-left: none;
    outline: 0;
    box-shadow: none;
  }

  &::-webkit-input-placeholder { /* Edge */
    color: rgba(255, 255, 255, .5);;
  }

  &:-ms-input-placeholder { /* Internet Explorer 10-11 */
    color: rgba(255, 255, 255, .5);;
  }

  &::placeholder {
    color: rgba(255, 255, 255, .5);;
  }
}

.carousel .carousel-item {
  height: 565px;
}

.carousel-item img {
  position: absolute;
  top: 0;
  left: 0;
  min-height: 300px;
}


.btn-image {
  min-height: 96px;
  background-color: transparent;
  color: #ffffff;
  border: 2px solid #ffffff;
  min-width: 240px;
  padding-left: 20px;
  padding-right: 20px;
  width: 100%;
  font: normal 20px "ONMISITECondensedMedium", "ITCBook", sans-serif;
  line-height: 1;
  border-radius: 0;
  text-align: center;
  text-transform: uppercase;
  -webkit-font-smoothing: antialiased;
  outline: 0;

  &:hover {
    color: #ff9d23 !important;
    background-color: #ffffff !important;
    border: 2px solid #ffffff;
  }

  &:focus {
    color: #ff9d23 !important;
    background-color: #ffffff !important;
    border: 2px solid #ffffff;
    box-shadow: none !important;
  }

  &:active {
    color: #ffffff !important;
    background-color: #ff9d23 !important;
    border: 2px solid #ffffff;
  }
}

hr {
  border: 0;
  color: #dbdad4;
  background-color: #43423e;
  height: 1px;
  margin-top: 0;
  margin-bottom: 0;
}

.imgoverlay {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 1;
  display: block;
  width: 100%;
  height: 100%;
  background-color: #000;
  opacity: .4;
}

.factoid-container .factoid-row > div:not(.col-xs-12):nth-child(even) {
  padding-left: 6px
}

.factoid-container .factoid-row > div:not(.col-xs-12):nth-child(odd) {
  padding-right: 6px
}

.factoid-container .factoid-row .factoid-col {
  padding-left: 5px \9
;
  padding-right: 5px \9
}

.factoid-container .factoid-row .factoid-col .factoid-large .pull-content-right {
  width: 100% \9
;
  float: left \9
}

.factoid-container .factoid-row {
  padding-left: 11px;
  padding-right: 11px
}

.factoid-container .factoid-row .factoid-col {
  padding-left: 0;
  padding-right: 0;
  margin-bottom: 12px
}

.factoid-container .factoid-row .factoid-col:last-of-type {
  margin-bottom: 0
}

.factoid-container .factoid {
  width: 100%;
  margin: 0;
  padding: 0;
  padding-left: 20px;
  padding-right: 20px;
  padding-top: 30px;
  padding-bottom: 30px
}

.factoid-large .pull-content-left, .factoid-large .pull-content-right {
  width: auto;
  float: none
}

.factoid-container .factoid.padding-top-80 {
  padding-top: 30px
}

.factoid-container .factoid .content h3 {
  font-size: 24px;
  line-height: 22px
}


#login-dp {
  min-width: 250px;
  padding: 14px 14px 0;
  overflow: hidden;
  background-color: rgba(255, 255, 255, .8);
  left: -148px;
}

#login-dp .help-block {
  font-size: 12px
}

#login-dp .bottom {
  background-color: rgba(255, 255, 255, .8);
  border-top: 1px solid #ddd;
  clear: both;
  padding: 14px;
  width: 100%;
}

#login-dp .social-buttons {
  margin: 12px 0
}

#login-dp .social-buttons a {
  width: 49%;
}

#login-dp .form-group {
  margin-bottom: 10px;
}

.btn-fb {
  color: #fff;
  background-color: #3b5998;
}

.btn-fb:hover {
  color: #fff;
  background-color: #496ebc
}

.btn-tw {
  color: #fff;
  background-color: #55acee;
}

.btn-tw:hover {
  color: #fff;
  background-color: #59b5fa;
}

.logindropdown .small {
  font-size: 1rem !important;
}

.redbackselect .select2-container--default .select2-selection--single .select2-selection__placeholder {
  color: #e9889d;
}

.select2-container--default .select2-search--inline .select2-search__field::placeholder {
  color: #e9889d;
  opacity: 1; /* Firefox */
}

.redbackselect .select2-container--default .select2-selection--multiple .select2-selection__choice {
  background-color: transparent;
}

.redbackselect .select2-container--default .select2-selection--single {
  background-color: #ff9d23;
  border: 1px solid #fff;
  border-radius: 0;
  border-top: none;
  border-left: none;
  border-right: none;
}

.redbackselect .select2-container--default .select2-selection--multiple {
  background-color: #ff9d23;
  border: 1px solid #fff;
  border-radius: 0;
  border-top: none;
  border-left: none;
  border-right: none;
}

.redbackselect .select2-container--default .select2-selection--single .select2-selection__rendered {
  color: #fff;
  padding: 0;
}

.redbackselect .select2-container--default .select2-selection--multiple .select2-selection__rendered {
  color: #fff;
  padding: 0;
}

.menutile {
  min-height: 216px;
}


.dashboard-widget.widget-box {
  box-shadow: rgba(0, 0, 0, .2) 0 0 4px;
  border: none;
  cursor: pointer;
}

.widget-box {
  position: relative;
  border: 1px solid #CCC;
  display: block;
  overflow: hidden;
}

.widget-content {
  padding: 20px;
  overflow: hidden;
  min-height: 135px;
}

.dashboard-widget.widget-box .product-icon {
  position: absolute;
  left: 0;
  top: 0;
  height: 70px;
  width: 80px;
  color: #FFF;
  font-size: 26px;
  border-radius: 0 0 100px;
  text-align: center;
  -moz-transition: all .3s ease 0s;
  -o-transition: all .3s ease 0s;
  -webkit-transition: all .3s ease 0s;
  transition: all .3s ease 0s;
  overflow: hidden;
}

.dashboard-widget.widget-box .product-icon .product-hover {
  top: -50px;
  left: 8px;
  -moz-transition: top .5s ease 0s;
  -o-transition: top .5s ease 0s;
  -webkit-transition: top .5s ease 0s;
  transition: top .5s ease 0s;
}

.dashboard-widget.widget-box .product-icon i {
  position: relative;
}

.dashboard-widget.widget-box .product-icon .display-icon {
  -moz-transition: top .5s ease 0s;
  -o-transition: top .5s ease 0s;
  -webkit-transition: top .5s ease 0s;
  transition: top .5s ease 0s;
  top: 16px;
  left: -27px;
  opacity: .4;
}

.dashboard-widget.widget-box .product-icon i {
  position: relative;
}

.contain-inner.dashboard-v2 .dashboard-widget.widget-box .search_ad {
  color: #FFF;
  margin-right: 10px;
}

.dashboard-widget.widget-box span {
  color: #777;
}

.search_ad {
  background: #087380;
}

.search_ad {
  padding: 2px 5px;
  font-size: 12px;
  color: #FFF !important;
  margin-right: 5px;
  -webkit-border-radius: 5px;
  -moz-border-radius: 5px;
  -ms-border-radius: 5px;
  border-radius: 5px;
}

/* Absolute Center Spinner */
.loading {
  position: fixed;
  z-index: 999999;
  height: 2em;
  width: 2em;
  overflow: show;
  margin: auto;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
}

/* Transparent Overlay */
.loading:before {
  content: '';
  display: block;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.3);
}

/* :not(:required) hides these rules from IE9 and below */
.loading:not(:required) {
  /* hide "loading..." text */
  font: 0/0 a;
  color: transparent;
  text-shadow: none;
  background-color: transparent;
  border: 0;
}

.loading:not(:required):after {
  content: '';
  display: block;
  font-size: 10px;
  width: 1em;
  height: 1em;
  margin-top: -0.5em;
  -webkit-animation: spinner 1500ms infinite linear;
  -moz-animation: spinner 1500ms infinite linear;
  -ms-animation: spinner 1500ms infinite linear;
  -o-animation: spinner 1500ms infinite linear;
  animation: spinner 1500ms infinite linear;
  border-radius: 0.5em;
  -webkit-box-shadow: rgba(0, 0, 0, 0.75) 1.5em 0 0 0, rgba(0, 0, 0, 0.75) 1.1em 1.1em 0 0, rgba(0, 0, 0, 0.75) 0 1.5em 0 0, rgba(0, 0, 0, 0.75) -1.1em 1.1em 0 0, rgba(0, 0, 0, 0.5) -1.5em 0 0 0, rgba(0, 0, 0, 0.5) -1.1em -1.1em 0 0, rgba(0, 0, 0, 0.75) 0 -1.5em 0 0, rgba(0, 0, 0, 0.75) 1.1em -1.1em 0 0;
  box-shadow: rgba(0, 0, 0, 0.75) 1.5em 0 0 0, rgba(0, 0, 0, 0.75) 1.1em 1.1em 0 0, rgba(0, 0, 0, 0.75) 0 1.5em 0 0, rgba(0, 0, 0, 0.75) -1.1em 1.1em 0 0, rgba(0, 0, 0, 0.75) -1.5em 0 0 0, rgba(0, 0, 0, 0.75) -1.1em -1.1em 0 0, rgba(0, 0, 0, 0.75) 0 -1.5em 0 0, rgba(0, 0, 0, 0.75) 1.1em -1.1em 0 0;
}

/* Animation */

@-webkit-keyframes spinner {
  0% {
    -webkit-transform: rotate(0deg);
    -moz-transform: rotate(0deg);
    -ms-transform: rotate(0deg);
    -o-transform: rotate(0deg);
    transform: rotate(0deg);
  }
  100% {
    -webkit-transform: rotate(360deg);
    -moz-transform: rotate(360deg);
    -ms-transform: rotate(360deg);
    -o-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}

@-moz-keyframes spinner {
  0% {
    -webkit-transform: rotate(0deg);
    -moz-transform: rotate(0deg);
    -ms-transform: rotate(0deg);
    -o-transform: rotate(0deg);
    transform: rotate(0deg);
  }
  100% {
    -webkit-transform: rotate(360deg);
    -moz-transform: rotate(360deg);
    -ms-transform: rotate(360deg);
    -o-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}

@-o-keyframes spinner {
  0% {
    -webkit-transform: rotate(0deg);
    -moz-transform: rotate(0deg);
    -ms-transform: rotate(0deg);
    -o-transform: rotate(0deg);
    transform: rotate(0deg);
  }
  100% {
    -webkit-transform: rotate(360deg);
    -moz-transform: rotate(360deg);
    -ms-transform: rotate(360deg);
    -o-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}

@keyframes spinner {
  0% {
    -webkit-transform: rotate(0deg);
    -moz-transform: rotate(0deg);
    -ms-transform: rotate(0deg);
    -o-transform: rotate(0deg);
    transform: rotate(0deg);
  }
  100% {
    -webkit-transform: rotate(360deg);
    -moz-transform: rotate(360deg);
    -ms-transform: rotate(360deg);
    -o-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}

@media screen and (max-width: 768px) {

  .bg {
    /* The image used */
    background-image: url("../images/loginback2mobile.png");

    /* Full height */
    height: 100%;

    /* Center and scale the image nicely */
    background-position: center;
    background-repeat: no-repeat;
    background-size: cover;
  }

  .loginform {
    margin-top: 15px !important;
    margin-left: 15px;
    margin-right: 15px;
  }

  .navbar-toggler-icon {
    margin-top: -50px;
    margin-right: -15px;
  }
}


@media(max-width: 767px) {
  .col3-factoid .factoid-row > div:not(.col-xs-12):not(.col-xs-6):nth-child(1) {
    padding-left: 0;
    padding-right: 6px
  }
  .col3-factoid .factoid-row > div:not(.col-xs-12):not(.col-xs-6):nth-child(2) {
    padding-right: 6px;
    padding-left: 6px
  }
  .col3-factoid .factoid-row > div:not(.col-xs-12):not(.col-xs-6):nth-child(3) {
    padding-left: 6px;
    padding-right: 0
  }
  #login-dp {
    background-color: inherit;
    color: #fff;
  }
  #login-dp .bottom {
    background-color: inherit;
    border-top: 0 none;
  }
}

@media(min-width: 768px) and (max-width: 1199px) {
  .col3-factoid .factoid-row > div:not(.col-sm-12):not(.col-sm-6):nth-child(1) {
    padding-left: 0;
    padding-right: 6px
  }
  .col3-factoid .factoid-row > div:not(.col-sm-12):not(.col-sm-6):nth-child(2) {
    padding-right: 6px;
    padding-left: 6px
  }
  .col3-factoid .factoid-row > div:not(.col-sm-12):not(.col-sm-6):nth-child(3) {
    padding-left: 6px;
    padding-right: 0
  }
  .widget-content {
    min-height: 175px;
  }
}

@media(min-width: 768px) {
  .factoid-container .factoid {
    padding-bottom: 0
  }
  .factoid-container {
    padding-left: 48px;
    padding-right: 48px
  }
  .factoid-container .factoid-row {
    margin: 0;
    padding: 0;
    padding-left: 58px;
    padding-right: 58px
  }
  .factoid-container .factoid-row .factoid-col:nth-child(odd) {
    padding-right: 6px;
  }
  .factoid-container .factoid-row .factoid-col:nth-child(even) {
    padding-left: 6px
  }
  .factoid-container .factoid-row .factoid-col:last-of-type {
    margin-bottom: 12px
  }
  .factoid-container .factoid {
    height: 264px
  }
  .factoid-container .factoid .content h3 {
    font-size: 24px;
    line-height: 22px
  }
  .container, .card-container {
    width: 768px;
    padding-left: 40px;
    padding-right: 40px;
  }
}

@media(min-width: 1200px) {
  .col3-factoid .factoid-row > div:not(.col-md-12):not(.col-md-6):nth-child(1) {
    padding-left: 0;
    padding-right: 6px
  }
  .col3-factoid .factoid-row > div:not(.col-md-12):not(.col-md-6):nth-child(2) {
    padding-right: 6px;
    padding-left: 6px
  }
  .col3-factoid .factoid-row > div:not(.col-md-12):not(.col-md-6):nth-child(3) {
    padding-left: 6px;
    padding-right: 0
  }
  .factoid-container {
    padding-left: 48px;
    padding-right: 48px;
  }
  .factoid-container .factoid-row {
    margin: 0;
    padding: 0;
    padding-left: 58px;
    padding-right: 58px
  }
  .factoid-container .factoid-col {
    margin-bottom: 12px
  }
  .factoid-container .factoid {
    height: 320px;
    padding-left: 40px;
    padding-right: 40px;
    padding-top: 48px;
  }
  .factoid-container .factoid.padding-top-3xl {
    padding-top: 48px
  }
  .factoid-container .factoid.padding-top-80 {
    padding-top: 80px
  }
  .factoid-container .factoid .content h3 {
    font-size: 48px;
    line-height: 44px
  }
  .factoid-large .pull-content-left {
    width: 50%;
    float: left
  }
  .factoid-large .pull-content-right {
    width: 50%;
    float: right
  }
  .container, .card-container {
    width: 1200px;
    padding-left: 48px;
    padding-right: 48px;
  }
}
