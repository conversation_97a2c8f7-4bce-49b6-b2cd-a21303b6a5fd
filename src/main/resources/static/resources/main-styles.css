.nav-info-container {
    display: flex;
    flex-direction: row;
    align-items: center;

}

.nav-info-container .nav-info-img {
    width: 26px;
    height: 26px;
    border-radius: 50%;
    display: flex;
    align-content: center;
    justify-content: center;
    background: rgb(33 94 158 / 10%);
    align-items: center;
    margin-right: 8px;
    flex: 0 0 26px;

}

.nav-info-container .nav-info-img .nav-icon {
    width: 12px;
    height: 12px;
    color: #1d4ca0;
}

.nav-info-container .nav-info-child-container {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
}

.nav-info-container .nav-info-child-container .info-label {
    font-weight: 500;
    font-size: 11px;
    line-height: 14px;
    color: #898989;
}

.nav-info-container .nav-info-child-container .info-detail {
    font-weight: 600;
    font-size: 13px;
    line-height: 16px;
    color: #1d4ca0;
}

.admin-floating-action-buttons .btn {
    border-radius: 50%;
    height: 42px;
    width: 42px;
    margin-bottom: 16px;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-left: 8px;
    margin-right: 8px;
}

.remote-video-btns-col {
    display: flex;
    flex-direction: row;
}

.view-map-modal-container {
    position: fixed;
    bottom: 108px;
    right: 24px;
}

.admin-floating-action-buttons {
    position: fixed;
    bottom: 29px;
    left: 13px;
    width: calc(41.66667% - 26px);
    display: flex;
    justify-content: center;
    background: #fff;
    border-bottom-left-radius: 8px;
    border-bottom-right-radius: 8px;
    padding-bottom: 6px;
}

.remote-video-btns-col .fa-microphone-lines-slash {
    width: 20px !important;
    height: 20px !important;
}

.client-v-h-100 #participants > div {
    height: 100%;
    width: 100%;
    position: relative;
}

.client-v-h-100 #participants {
    height: 100%;
    width: 100%;
    position: relative;
}

.client-v-h-100 #participants > div > video {
    height: 100%;
    width: 100%;
    object-fit: cover;
    border-radius: 8px;
}

.card-footer-client-video {
    position: fixed;
    width: calc(50% - 38px);
    bottom: 23px;
    background: rgba(0, 0, 0, 0.55);
    left: calc(25% + 19px);
    padding: 4px;
    border-bottom-left-radius: 8px;
    border-bottom-right-radius: 8px;
}

@media (max-width: 991px) {
    .card-footer-client-video {
        position: fixed;
        width: calc(100% - 38px);
        bottom: 23px;
        background: rgba(0, 0, 0, 0.55);
        left: 19px;
        padding: 4px;
        border-bottom-left-radius: 8px;
        border-bottom-right-radius: 8px;
    }
}

.sub-card-header-uw {
    font-size: 13px !important;
    font-weight: 600;
    color: #595959;
}
.data-row-div{
    /*display: flex;*/
    /*flex-direction: row;*/
    /*flex-wrap: wrap;*/
    gap: 12px 0;
}
.data-row-div .data-container-div{
    /*flex: 0 0 calc(50% - 12px);*/
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.data-row-div .data-container-div .data-label-span{
    font-size: 14px;
    color: rgba(0, 0, 0, 0.55);
    line-height: 15px;
}
.data-row-div .data-container-div .data-details-span{
    font-weight: 500;
    font-size: 15px;
    color: #1d4ca0;
    line-height: 18px;
}
.data-row-div .inspection-data-form{
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    gap: 2px;
}

.data-row-div .inspection-data-form .form-check{
    flex: 0 0 calc(33% - 4px);
}
.data-row-div .inspection-data-form .form-check .form-check-label{
    font-size: 12px !important;
    font-weight: 500;
}
.tyre-condition-table th, .tyre-condition-table td{
    padding: 4px !important;
}