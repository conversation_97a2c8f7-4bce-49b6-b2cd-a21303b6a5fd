const TOAST_TYPE = {
    SUCCESS: 1,
    ERROR: 2,
    WARNING: 3,
}

function showToast(title, message, toastType) {
    let bgClass = "toast bg-success";
    if (TOAST_TYPE.ERROR == toastType) {
        bgClass = "toast bg-danger";
    } else if (TOAST_TYPE.WARNING == toastType) {
        bgClass = "toast bg-warning";
    }
    let contain = "<div class=\"" + bgClass + "\" data-autohide=\"false\">\n" +
        "<div class=\"toast-header\">\n" +
        " <i class=\"fas fa-exclamation-circle\"></i>\n" +
        " <strong class=\"mr-auto ml-2\">" + title + "</strong>\n" +
        " <button aria-label=\"Close\" class=\"ml-2 mb-1 close\"\n" +
        "  data-dismiss=\"toast\" type=\"button\">\n" +
        " <span aria-hidden=\"true\">&times;</span>\n" +
        "</button>\n" +
        "</div>\n" +
        "<div class=\"toast-body text-white\">\n" +
        " " + message + "\n" +
        "</div>\n" +
        "</div>";
    document.getElementById("toastContainer").innerHTML = contain;
    $(".toast").toast('show');
}
