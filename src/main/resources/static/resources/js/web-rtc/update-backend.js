'use strict';
const updateCallEndDateTime = () => {

    let csrfToken = document.getElementById('_csrf').value;
    let peerUserId = document.getElementById('remoteUser').value;
    let url = contextRoot + 'update/call-end-date-time';

    let data = {
        accessToken: peerUserId
    }
    let request = new Request(url, {
        method: 'POST',
        body: JSON.stringify(data),
        headers: new Headers({
            'Accept': 'application/json',
            'Content-Type': 'application/json; charset=UTF-8;x-www-form-urlencoded',
            'X-CSRF-TOKEN': csrfToken
        })
    });

    fetch(request)
        .then(function (res) {
            // Handle response you get from the server
            res.json().then(value => {
                console.log("Call End Date Update :", value, res.status);
            })

        });

}

const updateGeolocation = (latitude, longitude) => {
    let csrfToken = document.getElementById('_csrf').value;
    let localUser = document.getElementById('localUser').value;
    let url = contextRoot + 'api/v1/client/update/geolocation';
    let data = {
        accessToken: localUser,
        latitude: latitude,
        longitude: longitude
    }
    let request = new Request(url, {
        method: 'POST',
        body: JSON.stringify(data),
        headers: new Headers({
            'Accept': 'application/json',
            'Content-Type': 'application/json; charset=UTF-8;x-www-form-urlencoded',
            'X-CSRF-TOKEN': csrfToken
        })
    });

    fetch(request)
        .then(function (res) {
            // Handle response you get from the server
            res.json().then(value => {
                console.log("Geolocation Update :", value, res.status);
            })

        });

}

