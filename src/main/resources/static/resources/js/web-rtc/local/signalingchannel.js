'use strict';

let socket;


function connect() {
    return new Promise(function (resolve, reject) {
        let server = null;
        try {
            server = new WebSocket(WEB_SOCKET_URL);
        } catch (e) {
            alert("Connection error.Please reload your browser window");
            location.reload();
            return;
        }
        server.onopen = function () {
            resolve(server);
        };

        server.onmessage = function (event) {
            if (event.data instanceof ArrayBuffer) {
                console.log("Image Upload");
            } else {
                let data = JSON.parse(event.data);
                switch (data.type) {
                    case "imageCapture":
                        console.log("admin reload image ", data)
                        reloadImage(data)
                        break;
                    case "leave":
                        handleLeave();
                        break;
                    case "geolocation":
                        setRemoteGeolocationLabelDetails(data)
                        break;
                    default:
                }
            }
        };
        server.onerror = function (err) {
            reject(err);
        };

    });
}

const send = (message) => {
    console.log("send function ", message)
    try {
        if (socket.readyState == WebSocket.OPEN) {
            try {
                socket.send(JSON.stringify(message));
            } catch (e) {
                alert(e)
            }
        }
    } catch (e) {
        alert("Connection error.Please reload your browser window");
        location.reload();
    }
}


