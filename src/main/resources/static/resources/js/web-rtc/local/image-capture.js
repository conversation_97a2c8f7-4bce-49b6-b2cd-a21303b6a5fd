'use strict';
let imageIndex = 0;
const imageMap = new Map()

const takePicture = (remoteVideo, latitude, longitude) => {

    console.log("Image width", width, "Image Height", height);
    let context = canvas.getContext('2d');

    if (width && height) {
        canvas.width = width;
        canvas.height = height;
        context.drawImage(remoteVideo, 0, 0, width, height);
        let data = canvas.toDataURL('image/png');
        let index = imageIndex++;
        setLoadingFrame();

        uploadImage(data, index, latitude, longitude).then(function (res) {
            console.log("Upload Image :", res);
            res.json().then(value => {
                    console.log("Upload Image :", value, res.status);
                    if (res.status == 203) {
                        alert("Can not be uploaded.Your data package has been expired.");
                    } else if (res.status == 204) {
                        alert("Can not be uploaded.Your data usage limit has been exceeded.")
                    }
                    createImageTag(data, index);
                }
            )
        })

        /*fakeServiceMethod().then(value => {

        })*/
        // sendImageToServer(data, latitude, longitude);

    } else {
        clearPhoto();
    }
}


const createImageTag = (data, index) => {

    let img = document.createElement('img');
    img.width = imageWidth;
    img.height = imageHeight;
    img.src = data;

    addFrame(data, index);
    drawImage();
}

const drawImage = () => {
    let div = '';
    for (let [key, value] of imageMap) {
        div = div + value
    }
    document.getElementById("imageContainer").innerHTML = div;
    initMagnify();
}


const addFrame = (src, index) => {

    let value = "<div class='col-md-6 image-set'>" +
        "<div class='img-wrap'>" +
        "<span onclick='deleteImage(" + index + ")' class='close'><img src='" + contextRoot + "resources/assets/img/close.svg' style='width: 16px; text-align: center'/></span>" +
        "<a href='" + src + "' data-magnify='gallery' >" +
        "<div class='card'>" +
        "<div class='' style='padding: 0px;'>" +
        "<img src='" + src + "'  class='img-thumbnail'  />" +
        "</div>" +
        "</div>" +
        "</a>" +
        "</div>" +
        "</div>";
    imageMap.set(index, value);
    return value;
}

const setLoadingFrame = () => {
    let loadingImageTag = "<div class='col-md-6 image-set'>" +
        "<div class='img-wrap'>" +
        "<span class='close'></span>" +
        "<div class='card'>" +
        "<div class='card-body' >" +
        "<div class='spinner-border text-primary' role='status'>" +
        "<span class='sr-only'></span>" +
        "</div>" +
        "</div>" +
        "</div>" +
        "</div>" +
        "</div>";

    let div = '';
    for (let [key, value] of imageMap) {
        div = div + value
    }
    document.getElementById("imageContainer").innerHTML = div + loadingImageTag;
    initMagnify();
}

const deleteImage = (id) => {
    imageMap.delete(id);
    drawImage();
}

const initMagnify = () => {
    $('[data-magnify]').magnify({
        headToolbar: [
            'close'
        ],
        footToolbar: [
            'zoomIn',
            'zoomOut',
            'prev',
            'fullscreen',
            'next',
            'actualSize',
            'rotateRight'
        ],
        title: false
    });
}


const clearPhoto = () => {
    let context = canvas.getContext('2d');
    context.fillStyle = "#AAA";
    context.fillRect(0, 0, canvas.width, canvas.height);
}


const fakeServiceMethod = () => {
    return new Promise((resolve, reject) => {
        setTimeout(() => {
            resolve('OK');
        }, 5000)
    })
}

const uploadImage = (canvasData, index, latitude, longitude) => {
    let imageData = canvasData.replace(/^data:image\/(png|jpg);base64,/, "");
    let csrfToken = document.getElementById('_csrf').value;
    let peerUserId = document.getElementById('remoteUser').value;
    let url = contextRoot + 'api/v1/image/upload';
    let data = {
        accessToken: peerUserId,
        imageData: imageData,
        index: index,
        latitude: latitude,
        longitude: longitude
    }
    let request = new Request(url, {
        method: 'POST',
        body: JSON.stringify(data),
        headers: new Headers({
            'Accept': 'application/json',
            'Content-Type': 'application/json; charset=UTF-8;x-www-form-urlencoded',
            'X-CSRF-TOKEN': csrfToken
        })
    });

    return fetch(request);
}

const deleteBackendImage = (uploadImageDetailsRefNo, callIndex) => {
    // alert(uploadImageDetailsRefNo + "-" + callIndex);
    // confirm("Are you sure you want to delete this image?")
    // const message = `Are you sure you want to delete this image?`;
    // if (!confirm(message)) {
    //    return;
    // }

    let csrfToken = document.getElementById('_csrf').value;
    let peerUserId = document.getElementById('remoteUser').value;
    let url = contextRoot + 'api/v1/image/delete';
    let data = {
        accessToken: peerUserId,
        callIndex: callIndex,
        uploadImageDetailsRefNo: uploadImageDetailsRefNo
    }
    let request = new Request(url, {
        method: 'POST',
        body: JSON.stringify(data),
        headers: new Headers({
            'Accept': 'application/json',
            'Content-Type': 'application/json; charset=UTF-8;x-www-form-urlencoded',
            'X-CSRF-TOKEN': csrfToken
        })
    });

    fetch(request).then(res => {
        res.text().then(value => {
            document.getElementById("prevImageContainer" + callIndex).innerHTML = value;
        })
    })
}


const reloadImageViewer = (callId) => {

    console.log("admin reloadImageViewer function callId ", callId);

    let csrfToken = document.getElementById('_csrf').value;
    let peerUserId = document.getElementById('remoteUser').value;
    let url = contextRoot + 'api/v1/image/reload-image-viewer';
    let data = {
        accessToken: peerUserId,
        callId: callId
    }
    let request = new Request(url, {
        method: 'POST',
        body: JSON.stringify(data),
        headers: new Headers({
            'Accept': 'application/json',
            'Content-Type': 'application/json; charset=UTF-8;x-www-form-urlencoded',
            'X-CSRF-TOKEN': csrfToken
        })
    });

    fetch(request).then(res => {
        res.text().then(value => {
            //$("#imageLoader").fadeOut("slow");
            document.getElementById("imageContainer").innerHTML = value;
            btnTakePhoto.disabled = false;
        })
    })
}

const reloadPreviousImageViewer = (callId) => {

    console.log("admin reloadImageViewer function callId ", callId);

    let csrfToken = document.getElementById('_csrf').value;
    let peerUserId = document.getElementById('remoteUser').value;
    let url = contextRoot + 'api/v1/image/reload-previous-image-viewer';
    let data = {
        accessToken: peerUserId,
        callId: callId
    }
    let request = new Request(url, {
        method: 'POST',
        body: JSON.stringify(data),
        headers: new Headers({
            'Accept': 'application/json',
            'Content-Type': 'application/json; charset=UTF-8;x-www-form-urlencoded',
            'X-CSRF-TOKEN': csrfToken
        })
    });

    fetch(request).then(res => {
        res.text().then(value => {
            //$("#imageLoader").fadeOut("slow");
            document.getElementById("previousImageContainer").innerHTML = value;
            btnTakePhoto.disabled = false;
        })
    })
}

const reloadInspectionSummaryImages = (callId, mainDocumentTypeId, subDocumentTypeId) => {

    console.log("admin reloadInspectionSummaryImages function main and sub document ids ", mainDocumentTypeId, "  ", subDocumentTypeId);

    let csrfToken = document.getElementById('_csrf').value;
    let peerUserId = document.getElementById('remoteUser').value;
    let url = contextRoot + 'api/v1/image/reload-inspection-summary-image-list';
    let data = {
        accessToken: peerUserId,
        callId: callId,
        mainDocumentTypeId: mainDocumentTypeId,
        subDocumentTypeId: subDocumentTypeId
    }
    let request = new Request(url, {
        method: 'POST',
        body: JSON.stringify(data),
        headers: new Headers({
            'Accept': 'application/json',
            'Content-Type': 'application/json; charset=UTF-8;x-www-form-urlencoded',
            'X-CSRF-TOKEN': csrfToken
        })
    });

    fetch(request)
        .then(res => res.json())
        .then(data => {

            console.log("image data ", data)

            let container = document.getElementById(`inspectionImgRow_${mainDocumentTypeId}_${subDocumentTypeId}`);
            // Clear any existing content
            container.innerHTML = '';
            // Iterate over the remark details and create HTML elements
            data.forEach(function (uploadImage) {
                let imgDiv = document.createElement('div');
                imgDiv.classList.add('inspection-img-div');

                let imgWrapDiv = document.createElement('div');
                imgWrapDiv.classList.add('img-wrap');

                let closeButton = document.createElement('span');
                closeButton.classList.add('close');
                closeButton.innerHTML = '<img src="/misyn/resources/assets/img/close.svg" style="width: 16px; text-align: center" />';
                closeButton.addEventListener('click', () => {
                    imgDiv.remove();
                    deleteBackendImage(uploadImage.uploadImageDetailsRefNo, subDocumentTypeId);
                });

                let anchor = document.createElement('a');
                anchor.setAttribute('data-magnify', 'gallery');
                anchor.href = contextRoot + `api/v1/image/view-original-image/${uploadImage.uploadImageDetailsRefNo}`;
                anchor.setAttribute('data-ref-no', uploadImage.uploadImageDetailsRefNo);

                let cardDiv = document.createElement('div');
                cardDiv.classList.add('card');

                let cardBodyDiv = document.createElement('div');
                cardBodyDiv.classList.add('card-body');

                let imgElement = document.createElement('img');
                imgElement.classList.add('img-fluid');
                imgElement.src = contextRoot + `api/v1/image/view-thumb-image/${uploadImage.uploadImageDetailsRefNo}`;
                imgElement.alt = 'Responsive image';
                imgElement.setAttribute('data-ref-no', uploadImage.uploadImageDetailsRefNo);

                let editIconAnchor = document.createElement('a');
                editIconAnchor.style.color = 'white';

                let editIconSpan = document.createElement('span');
                editIconSpan.classList.add('edit-img-icon');
                editIconSpan.addEventListener('click', () => {
                    openDrawingModal(uploadImage.uploadImageDetailsRefNo);
                });

                let editIcon = document.createElement('i');
                editIcon.classList.add('fa', 'fa-pencil');
                editIcon.setAttribute('aria-hidden', 'true');

                editIconSpan.appendChild(editIcon);
                editIconAnchor.appendChild(editIconSpan);

                // Append the map icon if latitude and longitude are available
                let mapIconAnchor = document.createElement('a');
                // if (uploadImage.latitudeVal && uploadImage.longitudeVal) {
                    mapIconAnchor.href = '#';
                    mapIconAnchor.style.color = 'white';
                    mapIconAnchor.addEventListener('click', (e) => {
                        e.preventDefault();
                        showMapModal(uploadImage.latitudeVal, uploadImage.longitudeVal);
                    });

                    let mapIconSpan = document.createElement('span');
                    mapIconSpan.classList.add('bot-map');

                    let mapIconImg = document.createElement('img');
                    mapIconImg.src = `/misyn/resources/assets/img/${uploadImage.mapIcon}`;
                    mapIconImg.classList.add('map-icon');
                    mapIconImg.title = uploadImage.mapTitle;
                    mapIconImg.alt = 'bot_img';
                    mapIconImg.style.width = '28px';
                    mapIconImg.style.height = '28px';

                    mapIconSpan.appendChild(mapIconImg);
                    mapIconAnchor.appendChild(mapIconSpan);
                // }


                /*cardBodyDiv.appendChild(imgElement);
                cardDiv.appendChild(cardBodyDiv);
                anchor.appendChild(cardDiv);
                imgWrapDiv.appendChild(closeButton);
                imgWrapDiv.appendChild(anchor);
                imgDiv.appendChild(imgWrapDiv);
                container.appendChild(imgDiv);*/

                cardBodyDiv.appendChild(imgElement);
                cardDiv.appendChild(cardBodyDiv);
                anchor.appendChild(cardDiv);
                imgWrapDiv.appendChild(closeButton);
                imgWrapDiv.appendChild(anchor);
                imgWrapDiv.appendChild(editIconAnchor);
                imgWrapDiv.appendChild(mapIconAnchor);
                imgDiv.appendChild(imgWrapDiv);
                container.appendChild(imgDiv);
            });
            btnTakePhoto.disabled = false;
        })
        .catch(error => {
            console.error("Error fetching images:", error);
        });


    /* const imgRow = document.getElementById(`inspectionImgRow_${mainDocumentTypeId}_${subDocumentTypeId}`);
     const imgDiv = document.createElement('div');
     imgDiv.classList.add('inspection-img-div');
     const img = document.createElement('img');
     // img.src = e.target.result;
     img.alt = 'Uploaded Image';
     imgDiv.appendChild(img);
     imgRow.appendChild(imgDiv);*/

}

// Remove Images
document.addEventListener('DOMContentLoaded', () => {
    document.body.addEventListener('click', (event) => {
        if (event.target.closest('.close')) {
            const button = event.target.closest('.close');
            const refNo = button.getAttribute('data-ref-no');
            const subDocumentTypeId = button.getAttribute('data-sub-document-type-id');
            const imgDiv = button.closest('.inspection-img-div');

            if (refNo && subDocumentTypeId && imgDiv) {
                imgDiv.remove();
                // deleteBackendImage(refNo, subDocumentTypeId);
            }
        }
    });
});
