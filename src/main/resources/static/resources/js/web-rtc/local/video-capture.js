'use strict';
let videoIndex = 0;
const videoMap = new Map();
const videoUrlMap = new Map();

let isRecordingStart = false;

const sendVideoRecordingStatus = (recordStart) => {
    send({
        type: "recording",
        recordStart: recordStart
    });
}

function recordingVideo(e, latitude, longitude) {
    console.log("mediaRecorder", e);

    /* let options = {mimeType: 'video/webm;codecs=vp9,opus'};
     if (!MediaRecorder.isTypeSupported(options.mimeType)) {
         console.error(`${options.mimeType} is not supported`);
         options = {mimeType: 'video/webm;codecs=vp8,opus'};
         if (!MediaRecorder.isTypeSupported(options.mimeType)) {
             console.error(`${options.mimeType} is not supported`);
             options = {mimeType: 'video/webm'};
             if (!MediaRecorder.isTypeSupported(options.mimeType)) {
                 console.error(`${options.mimeType} is not supported`);
                 options = {mimeType: ''};
             }
         }
     }*/

    let options = {
        audioBitsPerSecond: 128000,
        videoBitsPerSecond: 128000,
        //mimeType : 'video/mp4'
    }
    let onRecord = document.getElementById('btnOnRecord');
    // let mediaRecorder = new MediaRecorder(e.streams[0], options);
    const timer = new VideoRecordTimer();
    let mediaRecorder = new MediaRecorder(e.streams[0]);
    //  let mediaRecorder = new MediaRecorder(window.stream, options);
    let chunks = [];
    onRecord.addEventListener('click', (ev) => {
        try {
            let $el = $('#btnOnRecord');
            $el.find('i').toggleClass('fa-play-circle fa-stop-circle');
            $el.toggleClass('btn-success btn-light ');
            $el.toggleClass('on-record');
            sendVideoRecordingStatus(!isRecordingStart);
            if (isRecordingStart == false) {
                mediaRecorder.start();
                isRecordingStart = true;
                onRecord.title = "Stop Recording";
                timer.startRecordingTimer();
            } else {
                mediaRecorder.stop();
                isRecordingStart = false;
                onRecord.title = "Start Recording";
                timer.stopRecordingTimer()
            }
        } catch (e) {
            let $el = $('#btnOnRecord');
            $el.find('i').toggleClass('fa-play-circle fa-stop-circle');
            $el.toggleClass('btn-success btn-light ');
            $el.toggleClass('on-record');
            console.log(e)
        }

    });

    mediaRecorder.ondataavailable = function (ev) {
        chunks.push(ev.data);
    }
    mediaRecorder.onstop = (ev) => {
        let blob = new Blob(chunks, {'type': 'video/mp4;'});
        chunks = [];
        console.log("Video Recording Blob :", blob);

        let reader = new FileReader();
        reader.readAsDataURL(blob);
        reader.onloadend = function () {
            let base64data = reader.result;
            setLoadingVideoFrame();
            let index = videoIndex++;
            uploadVideo(base64data, index, latitude, longitude).then(function (res) {
                res.json().then(value => {
                        if (res.status == 203) {
                            alert("Can not be uploaded.Your data package has been expired.");
                        } else if (res.status == 204) {
                            alert("Can not be uploaded.Your data usage limit has been exceeded.")
                        }
                        let videoURL = window.URL.createObjectURL(blob);
                        videoUrlMap.set(index, videoURL);
                        createVideoTag(index);
                    }
                )
            })
        }


    }
}


const createVideoTag = (index) => {
    addVideoFrame(index);
    drawVideo();
}

const drawVideo = () => {
    let div = '';
    for (let [key, value] of videoMap) {
        div = div + value
    }
    document.getElementById("videoContainer").innerHTML = div;

}


const addVideoFrame = (index) => {
    let value = "<div class='col-md-6 image-set'>" +
        "<div class='img-wrap'>" +
        "<span onclick='deleteVideo(" + index + ")' class='close'>" +
        "<img src='" + contextRoot + "resources/assets/img/close.svg' style='width: 16px; text-align: center'/>" +
        "</span>" +
        "<div class='card'>" +
        "<div class='card-body'>" +
        "<img src='" + contextRoot + "resources/assets/img/gallery.png'  class='img-thumbnail' data-target='#showVideoPlayerModal' onclick='showVideoModal(" + index + ")'  />" +
        "</div>" +
        "</div>" +

        "</div>" +
        "</div>";
    videoMap.set(index, value);
    return value;
}

const setLoadingVideoFrame = () => {
    let loadingVideoTag = "<div class='col-md-6 image-set'>" +
        "<div class='img-wrap'>" +
        "<span class='close'></span>" +
        "<div class='card'>" +
        "<div class='card-body' >" +
        "<div class='spinner-border text-primary' role='status'>" +
        "<span class='sr-only'></span>" +
        "</div>" +
        "</div>" +
        "</div>" +
        "</div>" +
        "</div>";

    let div = '';
    for (let [key, value] of videoMap) {
        div = div + value
    }
    document.getElementById("videoContainer").innerHTML = div + loadingVideoTag;

}

const deleteVideo = (id) => {
    videoMap.delete(id);
    drawVideo();
}


const clearVideo = () => {
}


const sendVideoToServer = () => {
    return new Promise((resolve, reject) => {
        setTimeout(() => {
            resolve('OK');
        }, 5000)
    })
}

const showVideoModal = (index) => {
    console.log("videoUrlMap", videoUrlMap, index)
    let playVideo = document.getElementById('playVideo');
    playVideo.src = videoUrlMap.get(index);
    $('#showVideoPlayerModal').modal('show');
}


const uploadVideo = (streamData, index, latitude, longitude) => {
    latitude = document.getElementById("latitude").value;
    longitude = document.getElementById("longitude").value;
    let csrfToken = document.getElementById('_csrf').value;
    let peerUserId = document.getElementById('remoteUser').value;
    let url = contextRoot + 'api/v1/video/upload';
    let data = {
        accessToken: peerUserId,
        videoData: streamData,
        index: index,
        latitude: latitude,
        longitude: longitude,
    }
    let request = new Request(url, {
        method: 'POST',
        body: JSON.stringify(data),
        headers: new Headers({
            'Accept': 'application/json',
            'Content-Type': 'application/json; charset=UTF-8;x-www-form-urlencoded',
            'X-CSRF-TOKEN': csrfToken
        })
    });
    return fetch(request);
}
