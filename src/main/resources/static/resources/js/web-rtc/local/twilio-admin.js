'use strict';

const textRemoteUser = document.getElementById('remoteUser');
const textLocalUser = document.getElementById('localUser');
const localVideo = document.getElementById('localVideo');
let remoteVideo;
const participants = document.getElementById('participants');
const remoteVideoLoader = document.getElementById('remoteVideoLoader');
const remoteVideoContainer = document.getElementById('remoteVideoContainer');
const remoteVideoButtonPanel = document.getElementById('remoteVideoButtonPanel');
const btnTakePhoto = document.getElementById('btnTakePhoto');
const onRecord = document.getElementById('btnOnRecord');
const latitude = document.getElementById("latitude");
const longitude = document.getElementById("longitude");
const btnOnMute = document.getElementById('btnOnMute');
const btnZoomIn = document.getElementById('btnZoomIn');
const btnZoomOut = document.getElementById('btnZoomOut');
const btnTorch = document.getElementById('btnTorch');
const btnSwitchCamera = document.getElementById('btnSwitchCamera');
const btnOnHangUp = document.getElementById('btnOnHangUp');
const canvas = document.getElementById('canvas');
const callId = document.getElementById('callId');

let localUser = textLocalUser.value;
let remoteUser = textRemoteUser.value;

let width = 320;
let height = 320;
let imageWidth = 60;
let imageHeight = 60;

btnOnHangUp.disabled = true;
btnOnMute.disabled = true;
btnTakePhoto.disabled = true;
onRecord.disabled = true;
setConnectionStatus('Error');

let tempLat = 0;
let tempLng = 0;

const setRemoteGeolocationLabelDetails = (data) => {

    $("#spanLatitude").text(data.latitude);
    $("#spanLongitude").text(data.longitude);
    latitude.value = data.latitude;
    longitude.value = data.longitude;
    btnTakePhoto.disabled = false;
    onRecord.disabled = false;
    if (tempLat !== latitude.value || tempLng !== longitude.value) {
        console.log("Changed GeolocationLabelDetails", data)
        setViewMapIframeSrc(data.latitude, data.longitude);
        tempLat = latitude.value;
        tempLng = longitude.value;
    }

}


connect().then(function (server) {

    if (server.readyState === WebSocket.OPEN) {
        socket = server;
        setConnectionStatus('online');
        initialLogin();
    }
}).catch(function (err) {
    setConnectionStatus('Error');
});

const initialLogin = () => {
    if (localUser.length > 0) {
        send({
            type: "login",
            name: localUser,
            connectedUser: remoteUser
        });
    }
}

const showHideRemoteVideo = (show) => {

    $("#remoteVideoLoader").removeClass("d-flex");
    $("#remoteVideoContainer").removeClass("d-flex");
    $("#remoteVideoButtonPanel").removeClass("d-flex");

   $("#remoteVideoLoader").addClass("d-none");
    $("#remoteVideoContainer").addClass("d-none");
    $("#remoteVideoButtonPanel").addClass("d-none");

    if (show) {
        $("#remoteVideoContainer").addClass("d-flex");
        $("#remoteVideoContainer").removeClass("d-none");
        $("#remoteVideoButtonPanel").addClass("d-flex");
        $("#remoteVideoButtonPanel").removeClass("d-none");
        $("#remoteVideoLoader").addClass("d-none");
    } else {
        $("#remoteVideoLoader").addClass("d-flex");
        $("#remoteVideoLoader").removeClass("d-none");
        $("#remoteVideoButtonPanel").addClass("d-none");
    }
}

function initTwilio() {

    const identity = textLocalUser.value;
    let csrfToken = document.getElementById('_csrf').value;
    let callId = document.getElementById('callId').value;
    showHideRemoteVideo(false);
    // Fetch the access token
    let url = contextRoot + 'api/v1/twilio/admin/token';
    fetch(url, {
        method: "POST",
        headers: {
            "Content-Type": "application/json",
            'X-CSRF-TOKEN': csrfToken
        },
        body: JSON.stringify({identity: identity, room: textRemoteUser.value,callId:callId}),
    })
        .then((res) => {
            return res.json()
        })
        .then(({token, room, identity}) => {

            console.log("ROOM token:" + token)
            console.log("ROOM room:" + room)
            console.log("ROOM identity:" + identity)
            //    usernameSpan.textContent = identity;
            //   roomSpan.textContent = room;
            //   chat.removeAttribute("hidden");

            startVideoChat(room, token);
        });
}

function startVideoChat(room, token) {
    setConnectionStatus("init");
    // Start video chat and listen to participant connected events
    Twilio.Video.connect(token, {
        room: room,
        audio: true,
        maxAudioBitrate: 16000,
        video: false,
    }).then((room) => {
        // Once we're connected to the room, add the local participant to the page
        // participantConnected(room.localParticipant);
        // Add any existing participants to the page.
        room.participants.forEach(participantConnected);
        // Listen for other participants to join and add them to the page when they
        // do.
        //

        room.on("participantConnected", participantConnected);
        // Listen for participants to leave the room and remove them from the page
        room.on("participantDisconnected", participantDisconnected);
        // Eject the participant from the room if they reload or leave the page
        window.addEventListener("beforeunload", tidyUp(room));
        window.addEventListener("pagehide", tidyUp(room));
        btnOnHangUp.addEventListener('click', tidyUp(room));
        btnOnMute.addEventListener('click', muteLocal(room));
    });
}

function participantConnected(participant) {

    console.log("participantConnected", participant.identity)
    // Create new <div> for participant and add it to the page
    const el = document.createElement("div");
    el.setAttribute("id", participant.identity);
    el.setAttribute("style", '\n' +
        '    height: 100%;\n' +
        '    text-align: center;\n');
    participants.appendChild(el);
    // Find all the participant's existing tracks and publish them to our page

    participant.tracks.forEach((trackPublication) => {
        trackPublished(trackPublication, participant);

    });
    // Listen for the participant publishing new tracks
    participant.on("trackPublished", trackPublished);


}


function trackPublished(trackPublication, participant) {

    // Get the participant's <div> we created earlier
    const el = document.getElementById(participant.identity);
    // Find out if the track has been subscribed to and add it to the page or
    // listen for the subscription, then add it to the page.

    // First create a function that adds the track to the page
    const trackSubscribed = (track) => {
        // track.attach() creates the media elements <video> and <audio> to
        // to display the track on the page.
        const videoTrack = track.attach();
        if (track.kind === 'video') {
            remoteVideo = videoTrack;
            videoTrack.setAttribute("id", "remoteVideo");
            videoTrack.setAttribute("height", "100%");
            videoTrack.setAttribute("width", "100%");
            remoteVideo = videoTrack;

        }
        el.appendChild(videoTrack);

    };
    // If the track is already subscribed, add it immediately to the page
    if (trackPublication.track) {
        trackSubscribed(trackPublication.track);
    }
    // Otherwise listen for the track to be subscribed to, then add it to the
    // page
    trackPublication.on("subscribed", trackSubscribed);
    setConnectionStatus("connected");
    showHideRemoteVideo(true);
    btnOnMute.disabled = false;
    btnTakePhoto.disabled = false;
    onRecord.disabled = false;
    btnOnHangUp.disabled = false;
    console.log("trackPublished", participant.identity)
}

function participantDisconnected(participant) {
    participant.removeAllListeners();
    const el = document.getElementById(participant.identity);
    el.remove();
    setConnectionStatus("disconnected");
    showHideRemoteVideo(false);
    btnOnHangUp.disabled = true;
    btnOnMute.disabled = true;
    btnTakePhoto.disabled = true;
    onRecord.disabled = true;
    console.log("participantDisconnected", participant.identity)
}

function trackUnpublished(trackPublication) {
    trackPublication.track.detach().forEach(function (mediaElement) {
        mediaElement.remove();
    });
}

function tidyUp(room) {
    return function (event) {
        if (event.persisted) {
            return;
        }
        if (room) {
            room.disconnect();
            showHideRemoteVideo(false);
            let $el = $('#btnOnHangUp');
            $el.find('i').toggleClass('fa-phone fa-phone-slash');
            $el.toggleClass('btn-light btn-danger');
            $el.toggleClass('on-hangup');
            btnOnHangUp.disabled = true;
            btnOnMute.disabled = true;
            btnTakePhoto.disabled = true;
            onRecord.disabled = true;
            room = null;
           // updateCallEndDateTime();
        }
    };
}

let isMuteLocal = false;

function muteLocal(room) {
    return function (event) {
        if (event.persisted) {
            return;
        }
        if (room) {
            console.log("room", room);
            let $el = $('#btnOnMute');
            $el.find('svg').toggleClass('fa-microphone-lines fa-microphone-lines-slash');
            $el.toggleClass('btn-secondary btn-danger');
            $el.toggleClass('on-mute');

            if (isMuteLocal == true) {
                room.localParticipant.audioTracks.forEach(publication => {
                    publication.track.enable();

                });
                isMuteLocal = false;
                 $el.attr('data-bs-title', 'Mute');
            } else {
                room.localParticipant.audioTracks.forEach(publication => {
                    publication.track.disable();
                });
                isMuteLocal = true;
                 $el.attr('data-bs-title', 'Unmute');
            }
            let tooltipInstance = bootstrap.Tooltip.getInstance($el[0]);
                        if (tooltipInstance) {
                            tooltipInstance.dispose();
                        }
                        new bootstrap.Tooltip($el[0]);

        }
    };

}

function onClickImageUpload(mainDocumentTypeId, subDocumentTypeId) {

    console.log("Click image upload function", mainDocumentTypeId,"     " ,subDocumentTypeId);

    btnTakePhoto.disabled = true;

    send({
        type: "imageCapture",
        captureStart: true,
        mainDocumentTypeId: mainDocumentTypeId,
        subDocumentTypeId: subDocumentTypeId
    });

    document.getElementById(`inspectionImgRow_${mainDocumentTypeId}_${subDocumentTypeId}`).innerHTML = "<div class=\"on-loader text-center\"\n" +
        "                                                                     style=\"padding: 30px;\"\n" +
        "                                                                     id=\"imageLoader\">\n" +
        "                                                                    <div class=\"spinner-border text-primary\"\n" +
        "                                                                         role=\"status\">\n" +
        "                                                                    </div>\n" +
        "                                                                    <div class=\"mt-3 text-primary\"><P>\n" +
        "                                                                        Please wait...!! Image is uploading.</P></div>\n" +
        "                                                                </div>";



}

btnTakePhoto.addEventListener('click', (ev) => {
    btnTakePhoto.disabled = true;
    send({
        type: "imageCapture",
        captureStart: true
    });



    document.getElementById("imageContainer").innerHTML = "<div class=\"on-loader text-center\"\n" +
        "                                                                     style=\"padding: 30px;\"\n" +
        "                                                                     id=\"imageLoader\">\n" +
        "                                                                    <div class=\"spinner-border text-primary\"\n" +
        "                                                                         role=\"status\">\n" +
        "                                                                    </div>\n" +
        "                                                                    <div class=\"mt-3 text-primary\"><P>\n" +
        "                                                                        Please wait...!! Image is uploading.</P></div>\n" +
        "                                                                </div>";
    // $("#imageLoader").fadeIn("slow");
});


btnZoomIn.addEventListener('click', (ev) => {
    send({
        type: "cameraZoomIn"
    });

});

btnZoomOut.addEventListener('click', (ev) => {
    send({
        type: "cameraZoomOut"
    });

});

let isTorchOn = true;
btnTorch.addEventListener('click', (ev) => {

    let $el = $('#btnTorch');

    $el.toggleClass('btn-outline-secondary btn-warning');

    $el.toggleClass('torch-on turned-on-touch');
    if (isTorchOn) {
        send({
            type: "torchOn"
        });
        isTorchOn = false;
        $el.attr('data-bs-title', 'Turn Off Flash');
    } else {
        send({
            type: "torchOff"
        });
        isTorchOn = true;
         $el.attr('data-bs-title', 'Turn On Flash');
    }
     let tooltipInstance = bootstrap.Tooltip.getInstance($el[0]);
                if (tooltipInstance) {
                    tooltipInstance.dispose();
                }
                new bootstrap.Tooltip($el[0]);

});

let isSwitchCamera = true;
btnSwitchCamera.addEventListener('click', (ev) => {
    let $el = $('#btnSwitchCamera');
    let $img = $('#cameraRotateImg');

    $el.toggleClass('btn btn-outline-info switched-camera');
    $el.toggleClass('btn btn-outline-info switch-camera');



    if (isSwitchCamera) {
        send({
            type: "switchCameraFront"
        });
        isSwitchCamera = false;
        $img.attr("src", '/misyn/resources/assets/img/rotate-white.svg');
    } else {
        send({
            type: "switchCameraRear"
        });
        isSwitchCamera = true;
        $img.attr("src", '/misyn/resources/assets/img/rotate.svg');
    }

});


let isRecording = false;
const timer = new VideoRecordTimer();

onRecord.addEventListener('click', (ev) => {

    try {
        let $el = $('#btnOnRecord');
        $el.find('svg').toggleClass('fa-video fa-circle-stop');
        $el.toggleClass('btn-success btn-danger');
        $el.toggleClass('on-record');
        if (isRecording == false) {
            recording(true);
            send({
                type: "recording",
                recordStart: true
            });
            isRecording = true;
            $el.attr('data-bs-title', 'Stop Recording');
            timer.startRecordingTimer();
        } else {
            recording(false);
            send({
                type: "recording",
                recordStart: false
            });
            isRecording = false;
            $el.attr('data-bs-title', 'Start Recording');
            timer.stopRecordingTimer();
        }
        let tooltipInstance = bootstrap.Tooltip.getInstance($el[0]);
                        if (tooltipInstance) {
                            tooltipInstance.dispose();
                        }
                        new bootstrap.Tooltip($el[0]);
    } catch (e) {
        let $el = $('#btnOnRecord');
//        $el.find('i').toggleClass('fa-video fa-circle-stop');
//        $el.toggleClass('btn-outline-secondary btn-danger');
        $el.toggleClass('on-record');
        console.log(e)
    }



});

function reloadImage(data) {
    reloadImageViewer(callId.value);
    if (null !== data?.mainDocumentTypeId && null !== data?.subDocumentTypeId) {
        reloadInspectionSummaryImages(callId.value, data.mainDocumentTypeId, data.subDocumentTypeId)
    }
}

function recording(isStart) {
    let url = contextRoot + 'api/v1/video/recording/start';
    if (isStart == false) {
        url = contextRoot + 'api/v1/video/recording/stop';
    }
    let csrfToken = document.getElementById('_csrf').value;

    let data = {
        accessToken: remoteUser,
        callId: callId.value
    }
    let request = new Request(url, {
        method: 'POST',
        body: JSON.stringify(data),
        headers: new Headers({
            'Accept': 'application/json',
            'Content-Type': 'application/json; charset=UTF-8;x-www-form-urlencoded',
            'X-CSRF-TOKEN': csrfToken
        })
    });

    fetch(request).then(res => {
        res.text().then(value => {
        })
    })
}


