'use strict';
let latitude = document.getElementById("latitude");
let longitude = document.getElementById("longitude");
let _sendGeolocationData = null;

function getGeolocation(sendGeolocationData) {
    _sendGeolocationData = sendGeolocationData;
    if (navigator.geolocation) {
        navigator.geolocation.watchPosition(showPosition);
    } else {
        console.log('Geolocation is not supported by this browser.');
    }
}

function showPosition(position) {
    console.log("getLocation", position);
    latitude.value = position.coords.latitude;
    longitude.value = position.coords.longitude;
    _sendGeolocationData(latitude.value, longitude.value);
    setGeolocationLabelDetails({latitude: position.coords.latitude, longitude: position.coords.longitude})
    updateGeolocation(position.coords.latitude, position.coords.longitude);
}


const setGeolocationLabelDetails = (data) => {
    $("#spanLatitude").text(data.latitude);
    $("#spanLongitude").text(data.longitude);
}
