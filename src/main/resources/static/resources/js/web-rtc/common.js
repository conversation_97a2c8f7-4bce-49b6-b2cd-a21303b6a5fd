'use strict';
let connection = navigator.connection || navigator.mozConnection || navigator.webkitConnection;
const signalConnectionInit = () => {
    try {
        connection.addEventListener('change', updateConnectionStatus);
        updateConnectionStatus();
    } catch (e) {
        console.log(e);
    }
}

const updateConnectionStatus = () => {
    let text;
    let className;
    $("#signalInfoBadge").removeClass("badge-success");
    $("#signalInfoBadge").removeClass("badge-primary");
    $("#signalInfoBadge").removeClass("badge-danger");
    $("#signalInfoBadge").removeClass("badge-warning");

    if (connection &&
        connection.effectiveType === '2g') {
        text = "2G";
        className = 'badge-warning';
        alert('2G is not supported. Please use a better internet service.');
    } else if (connection &&
        connection.effectiveType === '3g') {
        text = "3G";
        className = 'badge-primary';
    } else if (connection &&
        connection.effectiveType === '4g') {
        text = "4G";
        className = 'badge-success';
    } else {
        text = "N/A";
        className = 'badge-danger';
    }
    $("#signalInfoText").text(text);
    $("#signalInfoBadge").addClass(className);
}

signalConnectionInit();

const setLoaderText = (text) => {
    $("#remoteVideoLoaderText").text(text);
}

const setConnectionStatus = (status) => {
    $("#connectionStateText").removeClass("badge-success");
    $("#connectionStateText").removeClass("badge-primary");
    $("#connectionStateText").removeClass("badge-danger");
    $("#connectionStateText").removeClass("badge-warning");

    let text;
    let className;

    switch (status) {
        case "online":
            className = 'badge-success';
            text = "Online";
            break;
        case "new":
            className = 'badge-warning';
            text = "New";
            break;
        case "checking":
            className = 'badge-warning';
            text = "Peer Checking";
            break;
        case "connected":
        case "completed":
            className = 'badge-success';
            text = "Peer Connected";
            break;
        case "disconnected":
            className = 'badge-danger';
            text = "Peer Disconnected";
            break;
        case "close":
            className = 'badge-danger';
            text = "Call End";
            break;
        case "init":
            className = 'badge-warning';
            text = "Connecting";
            break;
        case "switch":
            className = 'badge-warning';
            text = "Switching camera";
            break;
        case "failed":
            className = 'badge-danger';
            text = "failed";
            break;
        case "closed":
            className = 'badge-danger';
            text = "Peer Closed";
            break;
        default:
            className = 'badge-danger';
            text = status;
    }
    $("#connectionStateText").addClass(className);
    $("#connectionStateText").text(text);
}

const setGeolocationDetail = (data) => {
    $("#spanLatitude").text(data.latitude);
    $("#spanLongitude").text(data.longitude);
}


















