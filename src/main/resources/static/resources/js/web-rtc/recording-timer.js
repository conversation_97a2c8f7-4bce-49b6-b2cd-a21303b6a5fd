'use strict';
function VideoRecordTimer() {
    const obj = {};
    let timerValue;
    let startStop = 0;
    let miliSec = 0;
    let sec = 0; /* holds incrementing value */
    let min = 0;
    let hour = 0;
    /* Contains and outputs returned value of  function checkTime */
    let miliSecOut = 0;
    let secOut = 0;
    let minOut = 0;
    let hourOut = 0;


    this.startRecordingTimer = function () {
        timerValue = setInterval(timer, 10);
        $("#recordingTimerContainer").css("display", "block");
        $(".imageCaptureToast").addClass("show");
       // $("#recordingTimerContainer").fadeIn("slow");

    } /* Start */
    this.stopRecordingTimer = function () {
        clearInterval(timerValue);
        resetRecordingTimer();
        $("#recordingTimerContainer").fadeOut("slow");
    } /* Stop */

    /* Output variable End */
    const timer = () => {
        /* Main Timer */
        miliSecOut = checkTime(miliSec);
        secOut = checkTime(sec);
        minOut = checkTime(min);
        hourOut = checkTime(hour);
        miliSec = ++miliSec;
        if (miliSec === 100) {
            miliSec = 0;
            sec = ++sec;
        }
        if (sec == 60) {
            min = ++min;
            sec = 0;
        }
        if (min == 60) {
            min = 0;
            hour = ++hour;
        }
        document.getElementById("milisec").innerHTML = miliSecOut;
        document.getElementById("sec").innerHTML = secOut;
        document.getElementById("min").innerHTML = minOut;
        document.getElementById("hour").innerHTML = hourOut;
    }
    /* Adds 0 when value is <10 */
    const checkTime = (i) => {
        if (i < 10) {
            i = "0" + i;
        }
        return i;
    }

    const resetRecordingTimer = () => {
        miliSec = 0;
        sec = 0;
        min = 0
        hour = 0;
        document.getElementById("milisec").innerHTML = miliSecOut;
        document.getElementById("sec").innerHTML = secOut;
        document.getElementById("min").innerHTML = minOut;
        document.getElementById("hour").innerHTML = hourOut;
    }
}


