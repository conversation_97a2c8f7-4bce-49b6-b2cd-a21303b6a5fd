'use strict';
let imageIndex = 0;
const imageMap = new Map()

const takePicture = (remoteVideo, latitude, longitude, mainDocumentTypeId, subDocumentTypeId) => {

    console.log("remote user take picture function main and sub id ", mainDocumentTypeId, "   ", subDocumentTypeId)

    console.log("Image width", width, "Image Height", height);
    let context = canvas.getContext('2d');


    if (width && height) {
        canvas.width = width;
        canvas.height = height;
        context.drawImage(remoteVideo, 0, 0, width, height);
        let data = canvas.toDataURL('image/png');
        let index = imageIndex++;

        uploadImage(data, index, latitude, longitude, mainDocumentTypeId, subDocumentTypeId).then(function (res) {
            console.log("Upload Image :", res);
            res.json().then(value => {
                    console.log("Upload Image :", value, res.status);
                    if (res.status == 203) {
                        alert("Can not be uploaded.Your data package has been expired.");
                    } else if (res.status == 204) {
                        alert("Can not be uploaded.Your data usage limit has been exceeded.")
                    }

                    send({
                        type: "imageCapture",
                        captureStart: false,
                        mainDocumentTypeId: mainDocumentTypeId,
                        subDocumentTypeId: subDocumentTypeId
                    });
                    clearPhoto();
                $("#imageCaptureTimerContainer").fadeOut("slow");
                    // createImageTag(data, index);
                }
            )
        })

    } else {
        clearPhoto();
    }
}


const clearPhoto = () => {
    let context = canvas.getContext('2d');
    context.fillStyle = "#AAA";
    context.fillRect(0, 0, canvas.width, canvas.height);
}


const uploadImage = (canvasData, index, latitude, longitude, mainDocumentTypeId, subDocumentTypeId) => {

    console.log("remote user uploadImage function main and sub id ", mainDocumentTypeId, "   ", subDocumentTypeId)

    let imageData = canvasData.replace(/^data:image\/(png|jpg);base64,/, "");
    let csrfToken = document.getElementById('_csrf').value;
    let peerUserId = document.getElementById('localUser').value;
    let url = contextRoot + 'api/v1/client/upload';
    let data = {
        accessToken: peerUserId,
        imageData: imageData,
        index: index,
        latitude: latitude,
        longitude: longitude,
        mainDocumentTypeId: mainDocumentTypeId,
        subDocumentTypeId: subDocumentTypeId,
    }
    let request = new Request(url, {
        method: 'POST',
        body: JSON.stringify(data),
        headers: new Headers({
            'Accept': 'application/json',
            'Content-Type': 'application/json; charset=UTF-8;x-www-form-urlencoded',
            'X-CSRF-TOKEN': csrfToken
        })
    });

    return fetch(request);
}

const deleteBackendImage = (uploadImageDetailsRefNo, callIndex) => {
    alert(uploadImageDetailsRefNo + "-" + callIndex);
    let csrfToken = document.getElementById('_csrf').value;
    let peerUserId = document.getElementById('remoteUser').value;
    let url = contextRoot + 'api/v1/image/delete';
    let data = {
        accessToken: peerUserId,
        callIndex: callIndex,
        uploadImageDetailsRefNo: uploadImageDetailsRefNo
    }
    let request = new Request(url, {
        method: 'POST',
        body: JSON.stringify(data),
        headers: new Headers({
            'Accept': 'application/json',
            'Content-Type': 'application/json; charset=UTF-8;x-www-form-urlencoded',
            'X-CSRF-TOKEN': csrfToken
        })
    });

    fetch(request).then(res => {
        res.text().then(value => {
            document.getElementById("prevImageContainer" + callIndex).innerHTML = value;
        })
    })
}







