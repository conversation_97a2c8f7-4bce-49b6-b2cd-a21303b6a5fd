'use strict';

const textRemoteUser = document.getElementById('remoteUser');
const textLocalUser = document.getElementById('localUser');
//const localVideo = document.getElementById('localVideo');
const remoteVideo = document.getElementById('remoteVideo');
const participants = document.getElementById('participants');
const btnSwitchCamera = document.getElementById('btnSwitchCamera');
const btnOnMute = document.getElementById('btnOnMute');
const btnOnVideoStop = document.getElementById('btnOnVideoStop');
const btnOnHangUp = document.getElementById('btnOnHangUp');
const latitudeTxt = document.getElementById("latitude");
const longitudeTxt = document.getElementById("longitude");
const canvas = document.getElementById('canvas');


let localUser = textLocalUser.value;
let remoteUser = textRemoteUser.value;

connect().then(function (server) {
    if (server.readyState == WebSocket.OPEN) {
        socket = server;
        setConnectionStatus('online');
        initialLogin();
    }
}).catch(function (err) {
    setConnectionStatus('Error');
});

const sendGeolocationData = (latitude, longitude) => {
    send({
        type: "geolocation",
        name: remoteUser,
        latitude: latitude,
        longitude: longitude
    });

    latitudeTxt.value = latitude;
    longitudeTxt.value = longitude;
}

const initialLogin = () => {
    if (localUser.length > 0) {
        setConnectionStatus("init");
        send({
            type: "login",
            name: localUser,
            connectedUser: remoteUser
        });
        getGeolocation(sendGeolocationData);
    }
}


function initTwilio() {
    const identity = textLocalUser.value;
    let csrfToken = document.getElementById('_csrf').value;
    let callId = document.getElementById('callId').value;
    let url = contextRoot + 'api/v1/twilio/client/token';
    // Fetch the access token
    fetch(url, {
        method: "POST",
        headers: {
            "Content-Type": "application/json",
            'X-CSRF-TOKEN': csrfToken
        },
        body: JSON.stringify({identity: identity, room: textLocalUser.value,callId:callId}),
    })
        .then((res) => {
            console.log("RES", res)
            return res.json()
        })
        .then(({token, room, identity}) => {
            console.log(token, room, identity)
            //    usernameSpan.textContent = identity;
            //   roomSpan.textContent = room;
            //   chat.removeAttribute("hidden");
            startVideoChat(room, token);
        });
}



function startVideoChat(room, token) {
    setConnectionStatus("init");
    // Start video chat and listen to participant connected events
    Twilio.Video.connect(token, {
        room: room,
        audio: true,
        maxAudioBitrate: 16000,
        video: {facingMode: 'environment', height: 480, frameRate: 24, width: 640},
        //video: {facingMode: 'environment', frameRate: 24,height: 720, width: 1280},
        /*preferredVideoCodecs: [
            { codec: 'VP8', simulcast: true }
        ]*/

    }).then((room) => {

        // Once we're connected to the room, add the local participant to the page
        participantConnected(room.localParticipant);
        // Add any existing participants to the page.
        room.participants.forEach(participantConnected);
        // Listen for other participants to join and add them to the page when they
        // do.
        room.on("participantConnected", participantConnected);
        // Listen for participants to leave the room and remove them from the page
        room.on("participantDisconnected", participantDisconnected);
        // Eject the participant from the room if they reload or leave the page
        window.addEventListener("beforeunload", tidyUp(room));
        window.addEventListener("pagehide", tidyUp(room));
        btnOnMute.addEventListener('click', muteLocal(room));
        btnOnVideoStop.addEventListener('click', localVideoStop(room));
        btnOnHangUp.addEventListener('click', handleLeave(room));
    });
}

let cameraTrack = null;

function participantConnected(participant) {


    // Create new <div> for participant and add it to the page
    const el = document.createElement("div");
    el.setAttribute("id", participant.identity);
    participants.appendChild(el);
    // Find all the participant's existing tracks and publish them to our page
    participant.tracks.forEach((trackPublication) => {
        trackPublished(trackPublication, participant);
    });
    // Listen for the participant publishing new tracks
    participant.on("trackPublished", trackPublished);
    console.log("participantConnected", participant.identity)
}

let localVideo = null;

function trackPublished(trackPublication, participant) {
    // Get the participant's <div> we created earlier
    const el = document.getElementById(participant.identity);
    // Find out if the track has been subscribed to and add it to the page or
    // listen for the subscription, then add it to the page.

    // First create a function that adds the track to the page
    const trackSubscribed = (track) => {
        // track.attach() creates the media elements <video> and <audio> to
        // to display the track on the page.
        const videoTrack = track.attach();
        if (track.kind === 'video') {

            cameraTrack = track;
            localVideo = videoTrack;
            videoTrack.setAttribute("id", "remoteVideo");
            //videoTrack.setAttribute("height", "100%");
            // videoTrack.setAttribute("width", "100%");
            localVideo = videoTrack;
            localVideo.play();
        }
        el.appendChild(track.attach());
    };
    // If the track is already subscribed, add it immediately to the page
    if (trackPublication.track) {
        trackSubscribed(trackPublication.track);
    }
    // Otherwise listen for the track to be subscribed to, then add it to the
    // page
    trackPublication.on("subscribed", trackSubscribed);
    console.log("trackPublished", participant.identity);
    setConnectionStatus("connected");
}

function participantDisconnected(participant) {
    participant.removeAllListeners();
    const el = document.getElementById(participant.identity);
    el.remove();
    setConnectionStatus("disconnected");

    console.log("participantDisconnected", participant.identity);

    // window.location.href = '/misyn/api/v1/client/join/session-end';
    //  window.location.reload();
    let form = document.getElementById('sessionEndForm');
    form.action = '/misyn/api/v1/client/join/session-end';
    form.submit();
}

function trackUnpublished(trackPublication) {

    trackPublication.track.detach().forEach(function (mediaElement) {
        mediaElement.remove();
    });
}

function tidyUp(room) {
    return function (event) {
        if (event.persisted) {
            return;
        }
        if (room) {
            room.disconnect();
            room = null;
        }
    };
}

let isEnvironment = true;
btnSwitchCamera.addEventListener('click', (ev) => {
    _switchCamera();
});

function _switchCamera() {
    if (isEnvironment) {
        cameraTrack.restart({facingMode: 'user'});
        isEnvironment = false;
        btnSwitchCamera.title = "Switch Rear Camera";

    } else {
        cameraTrack.restart({facingMode: 'environment'});
        isEnvironment = true;
        btnSwitchCamera.title = "Switch Front Camera";

    }
    const videoTrack = cameraTrack.attach();
    videoTrack.setAttribute("id", "remoteVideo");
    localVideo = videoTrack;
    localVideo.play();
}

function switchCamera(facingMode) {
    isEnvironment = facingMode === "front";
    _switchCamera();
}

let isMuteLocal = false;

function muteLocal(room) {
    return function (event) {

        if (event.persisted) {
            return;
        }
        if (room) {
            let $el = $('#btnOnMute');
            $el.find('svg').toggleClass('fa-microphone-lines fa-microphone-lines-slash');
            $el.toggleClass('btn-primary btn-danger');
            $el.toggleClass('on-mute');

            if (isMuteLocal == true) {
                room.localParticipant.audioTracks.forEach(publication => {
                    publication.track.enable();
                     $el.attr('data-bs-title', 'Mute');
                });
                isMuteLocal = false;
            } else {
                room.localParticipant.audioTracks.forEach(publication => {
                    publication.track.disable();
                    $el.attr('data-bs-title', 'Unmute');
                });
                isMuteLocal = true;
            }

            let tooltipInstance = bootstrap.Tooltip.getInstance($el[0]);
            if (tooltipInstance) {
                tooltipInstance.dispose();
            }
            new bootstrap.Tooltip($el[0]);
        }
    };

}

function torch(torchOn) {
    if (cameraTrack) {
        cameraTrack.mediaStreamTrack.applyConstraints({advanced: [{torch: torchOn}]})
    }
}

function zoom(zoomType) {
    const track = cameraTrack.mediaStreamTrack; // Assuming you have only one video track
    const capabilities = track.getCapabilities();
    const settings = track.getSettings();

// Check if zoom is supported
    if ('zoom' in capabilities) {
        // Increase zoom
        let newZoom;
        if (zoomType === 'zoom-in') {
            newZoom = Math.min(settings.zoom + 0.1, capabilities.zoom.max);
        } else {
            newZoom = Math.max(settings.zoom - 0.1, capabilities.zoom.min);
        }
        track.applyConstraints({advanced: [{zoom: newZoom}]})
            .then(() => {
                console.log('Zoom increased to', newZoom);
            })
            .catch((error) => {
                console.error('Failed to set zoom:', error);
            });
    }
}

let isStopLocalVideo = false;

function localVideoStop(room) {
    return function (event) {
        if (event.persisted) {
            return;
        }
        if (room) {
            let $el = $('#btnOnVideoStop');
            $el.find('svg').toggleClass('fa-video fa-video-slash');
            $el.toggleClass('btn-success btn-danger');
            $el.toggleClass('on-record');

            if (isStopLocalVideo == true) {
                room.localParticipant.videoTracks.forEach(publication => {
                    publication.track.enable();
                });
                isStopLocalVideo = false;
                $el.attr('data-bs-title', 'Hide Video');
            } else {
                room.localParticipant.videoTracks.forEach(publication => {
                    publication.track.disable();
                });
                isStopLocalVideo = true;
                $el.attr('data-bs-title', 'Show Video');
            }

            let tooltipInstance = bootstrap.Tooltip.getInstance($el[0]);
            if (tooltipInstance) {
                tooltipInstance.dispose();
            }
            new bootstrap.Tooltip($el[0]);

        }
    };

}

function handleLeave(room) {
    return function (event) {
        if (event.persisted) {
            return;
        }
        if (room) {
            let $el = $('#btnOnHangUp');
            $el.find('i').toggleClass('fa-phone fa-phone-slash');
            $el.toggleClass('btn-light btn-danger');
            $el.toggleClass('on-hangup');

            room.localParticipant.videoTracks.forEach(publication => {
                publication.track.stop();
                publication.unpublish();
            });

            room.localParticipant.audioTracks.forEach(publication => {
                publication.track.disable();
            });
            btnOnHangUp.disabled = true;
            btnOnMute.disabled = true;
            setConnectionStatus("close");
            tidyUp(room);

        }
    };

}

let width = 320;
let height = 320;
let imageWidth = 60;
let imageHeight = 60;


function takeImageCapture(data) {
    $("#imageCaptureTimerContainer").css("display", "block");
    $(".imageCaptureToast").addClass("show");
    height = localVideo.videoHeight;
    width = localVideo.videoWidth;
    imageHeight = localVideo.videoHeight / (localVideo.videoWidth / imageWidth);
    console.log("Take Image width", width, "Image Height", height);
    if (isNaN(imageHeight)) {
        imageHeight = imageWidth / (4 / 3);
    }
    canvas.setAttribute('width', width);
    canvas.setAttribute('height', height);

    console.log("called remote user take photo function ")


    takePicture(localVideo, latitudeTxt.value, longitudeTxt.value, data.mainDocumentTypeId, data.subDocumentTypeId);
}


const timer = new VideoRecordTimer();
const recodingHandler = (recordStart) => {
    if (recordStart) {
        timer.startRecordingTimer();
        btnSwitchCamera.disabled = true;
    } else {
        timer.stopRecordingTimer();
        btnSwitchCamera.disabled = false;
    }
}
