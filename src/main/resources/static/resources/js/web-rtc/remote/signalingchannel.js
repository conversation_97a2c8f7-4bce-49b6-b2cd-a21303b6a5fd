'use strict';

let socket;

function connect() {
    return new Promise(function (resolve, reject) {
        let server = null;
        try {
            server = new WebSocket(WEB_SOCKET_URL);
        } catch (e) {
            alert("Connection error.Please reload your browser window");
            location.reload();
            return;
        }
        server.onopen = function () {
            resolve(server);
        };
        server.onmessage = function (event) {
            if (event.data instanceof ArrayBuffer) {
                console.log("Image Upload");
            } else {
                let data = JSON.parse(event.data);
                switch (data.type) {
                    case "imageCapture":
                        console.log("remote user ", data)
                        takeImageCapture(data);
                        break;
                    case "leave":
                        handleLeave();
                        break;
                    case "recording":
                        recoding<PERSON>andler(data.recordStart);
                        break;
                    case "peer-disconnected":
                        peerDisconnected();
                        break;
                    case "torchOn":
                        torch(true);
                        break;
                    case "torchOff":
                        torch(false);
                        break;
                    case "switchCameraFront":
                        switchCamera('front');
                        break;
                    case "switchCameraRear":
                        switchCamera('rear');
                        break;
                    case "cameraZoomIn":
                        zoom('zoom-in');
                        break;
                    case "cameraZoomOut":
                        zoom('zoom-out');
                        break;
                    default:
                }
            }
        };
        server.onerror = function (err) {
            reject(err);
        };

    });
}

const send = (message) => {
    try {
        if (socket.readyState == WebSocket.OPEN) {
            try {
                socket.send(JSON.stringify(message));
            } catch (e) {
                alert(e)
            }
        }
    } catch (e) {
        alert("Connection error.Please reload your browser window");
        location.reload();
    }

}
