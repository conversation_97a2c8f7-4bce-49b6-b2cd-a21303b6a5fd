document.addEventListener("DOMContentLoaded", function() {
    let canvas = document.getElementById('drawingCanvas');
    let context = canvas.getContext('2d');
    let drawing = false;
    let startX, startY;
    let imgInstance = new Image();
    let lineWidth = 5; // Default line width
    // let strokeStyle = 'red'; // Default stroke style
    let strokeStyle = '#ff0000'; // Default stroke style red
    let lineCap = 'round'; // Default line cap
    let shape = 'freehand'; // Default shape
    let shapes = []; // Array to store all drawn shapes
    let freehandPath = []; // Array to store points of the current freehand path
    let undoStack = []; // Stack to store undone shapes
    let redoStack = []; // Stack to store readable shapes
    let drawingStartTime = ''; // Store the start time of the drawing


    // Create an off-screen canvas for real-time drawing
    let tempCanvas = document.createElement('canvas');
    let tempContext = tempCanvas.getContext('2d');

    function startDrawing(e) {
        drawing = true;
        startX = e.clientX - canvas.getBoundingClientRect().left;
        startY = e.clientY - canvas.getBoundingClientRect().top;

        let formattedDateTime = formatDate(new Date());
        console.log("today formatted date", formattedDateTime)

        // Capture the current time
        const now = new Date();
        const hours = now.getHours();
        const minutes = now.getMinutes();
        const ampm = hours >= 12 ? 'PM' : 'AM';
        const formattedTime = ((hours % 12) || 12) + ':' + (minutes < 10 ? '0' : '') + minutes + ' ' + ampm;
        // drawingStartTime = formattedTime;

        drawingStartTime = formattedDateTime;

        if (shape === 'freehand') {
            freehandPath.push({ x: startX, y: startY });
            context.beginPath();  // Start a new path
            context.moveTo(startX, startY);
        } else if (shape === 'comment') {
            // Show the comment input box at the click position
            const commentInput = document.getElementById('commentInput');
            commentInput.style.display = 'block';
            // commentInput.style.position = 'absolute';
            commentInput.style.left = startX + 'px';
            commentInput.style.top = startY + 'px';
            commentInput.focus();
        }
        console.log("start drawing ")
    }

    function endDrawing(e) {
        if (!drawing) return;
        drawing = false;

        if (shape === 'freehand') {
            context.closePath();  // Close the current path
            storeFreehandPath();
            // drawTimestampForFreehand();
            redrawShapes();
        } else if (shape !== 'comment') {
            storeShape(e);
        }

        context.beginPath();
        tempContext.clearRect(0, 0, tempCanvas.width, tempCanvas.height);

        redoStack = []; // Clear redo stack when new action is performed
        console.log("end drawing ")
    }

    function draw(e) {
        if (!drawing || shape !== 'freehand') return;

        context.lineWidth = lineWidth;
        context.lineCap = lineCap;
        context.strokeStyle = strokeStyle;

        let rect = canvas.getBoundingClientRect();
        let x = e.clientX - rect.left;
        let y = e.clientY - rect.top;

        context.lineTo(x, y);
        context.stroke();
        context.beginPath();  // Reset the path
        context.moveTo(x, y);

        freehandPath.push({ x, y });
    }

    function storeFreehandPath() {
        shapes.push({
            type: 'freehand',
            path: freehandPath.slice(),
            lineWidth,
            strokeStyle,
            lineCap,
            time: drawingStartTime // Store the time
        });
        freehandPath = []; // Clear current freehand path
    }

    function drawTimestampForFreehand() {
        const lastPoint = freehandPath[freehandPath.length - 1];
        if (lastPoint) {
            context.font = '10px Arial';
            context.fillStyle = 'white';
            context.fillText(drawingStartTime, lastPoint.x + 5, lastPoint.y + 20); // Adjust the position as needed
        }
    }

    function storeShape(e) {
        let rect = canvas.getBoundingClientRect();
        let x = e.clientX - rect.left;
        let y = e.clientY - rect.top;

        let shapeData = {
            type: shape,
            startX: startX,
            startY: startY,
            endX: x,
            endY: y,
            lineWidth: lineWidth,
            strokeStyle: strokeStyle,
            time: drawingStartTime // Store the time
        };

        shapes.push(shapeData);
        redrawShapes();
    }

    function redrawShapes() {
        context.clearRect(0, 0, canvas.width, canvas.height);
        context.drawImage(imgInstance, 0, 0);

        shapes.forEach(shapeData => {
            context.lineWidth = shapeData.lineWidth;
            context.strokeStyle = shapeData.strokeStyle;
            context.lineCap = shapeData.lineCap;

            switch (shapeData.type) {
                case 'line':
                    context.beginPath();
                    context.moveTo(shapeData.startX, shapeData.startY);
                    context.lineTo(shapeData.endX, shapeData.endY);
                    context.stroke();
                    context.closePath();  // Ensure the path is closed
                    break;
                case 'rectangle':
                    context.strokeRect(shapeData.startX, shapeData.startY, shapeData.endX - shapeData.startX, shapeData.endY - shapeData.startY);
                    break;
                case 'circle':
                    let radius = Math.sqrt(Math.pow(shapeData.endX - shapeData.startX, 2) + Math.pow(shapeData.endY - shapeData.startY, 2));
                    context.beginPath();
                    context.arc(shapeData.startX, shapeData.startY, radius, 0, 2 * Math.PI);
                    context.stroke();
                    context.closePath();
                    break;
                case 'freehand':
                    context.beginPath();
                    if (shapeData.path.length > 0) {
                        context.moveTo(shapeData.path[0].x, shapeData.path[0].y);
                        for (let i = 1; i < shapeData.path.length; i++) {
                            context.lineTo(shapeData.path[i].x, shapeData.path[i].y);
                        }
                        context.stroke();
                        context.closePath();

                        // Draw the time below the last point of the freehand path
                        if (shapeData.time) {
                            const lastPoint = shapeData.path[shapeData.path.length - 1];
                            context.font = '6px Arial';
                            context.fillStyle = shapeData.strokeStyle;
                            // context.fillStyle = 'white';
                            context.fillText(shapeData.time, lastPoint.x + 5, lastPoint.y + 10); // Adjust the position as needed
                        }
                    }
                    break;
                case 'comment':
                    context.font = '16px Arial';
                    context.fillStyle = shapeData.strokeStyle;
                    context.fillText(shapeData.text, shapeData.startX, shapeData.startY);
                    break;
                case 'highlight':
                    // context.fillStyle = 'rgba(255, 255, 0, 0.5)'; // Yellow color with 50% opacity
                    context.fillStyle = getRGBAColor(shapeData.strokeStyle, 0.4);
                    context.fillRect(shapeData.startX, shapeData.startY, shapeData.endX - shapeData.startX, shapeData.endY - shapeData.startY);
                    break;
                default:
                    break;
            }

            // Draw the time below the shape (for shapes other than freehand)
            if (shapeData.type !== 'freehand' && shapeData.time) {
                context.font = '6px Arial';
                context.fillStyle = shapeData.strokeStyle;
                // context.fillStyle = 'white';
                context.fillText(shapeData.time, shapeData.endX + 5, shapeData.endY + 10); // Adjust the position as needed
            }
           /* // Draw the time below the shape
            if (shapeData.time) {
                context.font = '10px Arial';
                // context.fillStyle = strokeStyle;
                context.fillStyle = 'white';
                context.fillText(shapeData.time, shapeData.endX + 5, shapeData.endY + 20); // Adjust the position as needed
            }*/
        });
    }

    function addComment() {
        const commentInput = document.getElementById('commentInput');
        const text = commentInput.value;
        if (text.trim() !== "") {
            shapes.push({
                type: 'comment',
                startX,
                startY,
                text,
                strokeStyle: strokeStyle,
                time: drawingStartTime // Store the time
            });
            redrawShapes();
            // commentInput.style.display = 'none';
            commentInput.value = "";
        }
        // commentInput.style.display = 'none';
        // commentInput.focus();
        commentInput.value = "";
        redoStack = []; // Clear redo stack when new action is performed
    }

    function drawShape(e) {
        let rect = canvas.getBoundingClientRect();
        let x = e.clientX - rect.left;
        let y = e.clientY - rect.top;

        context.lineWidth = lineWidth;
        context.strokeStyle = strokeStyle;

        switch (shape) {
            case 'line':
                context.beginPath();
                context.moveTo(startX, startY);
                context.lineTo(x, y);
                context.stroke();
                break;
            case 'rectangle':
                context.strokeRect(startX, startY, x - startX, y - startY);
                break;
            case 'circle':
                let radius = Math.sqrt(Math.pow(x - startX, 2) + Math.pow(y - startY, 2));
                context.beginPath();
                context.arc(startX, startY, radius, 0, 2 * Math.PI);
                context.stroke();
                break;
            default:
                break;
        }
    }

    function drawRealTimeShape(e) {
        if (!drawing || shape === 'freehand') return;

        tempContext.clearRect(0, 0, tempCanvas.width, tempCanvas.height);

        let rect = canvas.getBoundingClientRect();
        let x = e.clientX - rect.left;
        let y = e.clientY - rect.top;

        tempContext.lineWidth = lineWidth;
        tempContext.strokeStyle = strokeStyle;

        switch (shape) {
            case 'line':
                tempContext.beginPath();
                tempContext.moveTo(startX, startY);
                tempContext.lineTo(x, y);
                tempContext.stroke();
                tempContext.closePath();
                break;
            case 'rectangle':
                tempContext.strokeRect(startX, startY, x - startX, y - startY);
                break;
            case 'circle':
                let radius = Math.sqrt(Math.pow(x - startX, 2) + Math.pow(y - startY, 2));
                tempContext.beginPath();
                tempContext.arc(startX, startY, radius, 0, 2 * Math.PI);
                tempContext.stroke();
                tempContext.closePath();
                break;
            case 'highlight':
                // tempContext.fillStyle = 'rgba(255, 255, 0, 0.5)'; // Yellow color with 50% opacity
                tempContext.fillStyle = getRGBAColor(strokeStyle, 0.4); // Convert to RGBA with 50% opacity
                tempContext.fillRect(startX, startY, x - startX, y - startY);
                break;
            default:
                break;
        }

        context.clearRect(0, 0, canvas.width, canvas.height);
        context.drawImage(imgInstance, 0, 0);
        redrawShapes();
        context.drawImage(tempCanvas, 0, 0);
    }

    function getRGBAColor(hexColor, opacity) {
        let r = parseInt(hexColor.slice(1, 3), 16);
        let g = parseInt(hexColor.slice(3, 5), 16);
        let b = parseInt(hexColor.slice(5, 7), 16);
        return `rgba(${r}, ${g}, ${b}, ${opacity})`;
    }

    window.openDrawingModal = function(imageRefNo,documentTypeId) {
        console.log("image ref no ", imageRefNo)
        const selectElement = document.getElementById("documentType");
        selectElement.value = documentTypeId;

        let modal = document.getElementById("drawingModal");
        modal.style.display = "block";

        // let imgSrc = contextRoot +"api/v1/image/view-original-image/" + imageRefNo;
        let imgSrc = contextRoot +"api/v1/image/view-original-image/" + imageRefNo + "?" + new Date().getTime();
        imgInstance.src = imgSrc;

        /* imgInstance.onload = function() {
             canvas.width = imgInstance.width;
             canvas.height = imgInstance.height;
             context.drawImage(imgInstance, 0, 0);
         };*/

        imgInstance.onload = function() {
            canvas.width = imgInstance.width;
            canvas.height = imgInstance.height;
            tempCanvas.width = imgInstance.width;
            tempCanvas.height = imgInstance.height;
            context.drawImage(imgInstance, 0, 0);
        };

        // Set the imageRefNo as a data attribute on the upload button
        const uploadButton = document.getElementById('uploadButton');
        uploadButton.setAttribute('data-image-ref-no', imageRefNo);
    };

    window.closeDrawingModal = function() {
        let modal = document.getElementById("drawingModal");
        modal.style.display = "none";
        context.clearRect(0, 0, canvas.width, canvas.height);
        // context.drawImage(imgInstance, 0, 0, canvas.width, canvas.height);
        shapes = [];
        undoStack = [];
        redoStack = [];
    };

    canvas.addEventListener('mousedown', startDrawing);
    canvas.addEventListener('mouseup', endDrawing);
    // canvas.addEventListener('mousemove', draw);
    canvas.addEventListener('mousemove', function(e) {
        draw(e);
        drawRealTimeShape(e);
    });

    document.getElementById('commentInput').addEventListener('blur', addComment);

    document.getElementById('freehandButton').addEventListener('click', () => shape = 'freehand');
    document.getElementById('lineButton').addEventListener('click', () => shape = 'line');
    document.getElementById('rectangleButton').addEventListener('click', () => shape = 'rectangle');
    document.getElementById('circleButton').addEventListener('click', () => shape = 'circle');
    // document.getElementById('commentButton').addEventListener('click', () => shape = 'comment');
    document.getElementById('colorPicker').addEventListener('change', (event) => strokeStyle = event.target.value);
    // document.getElementById('lineWidthSelect').addEventListener('change', (event) => lineWidth = event.target.value);
    document.getElementById('highlightButton').addEventListener('click', () => shape = 'highlight');

    // Function to remove 'selected' class from all tool buttons
    function clearSelection() {
        const buttons = document.querySelectorAll('.tool-button');
        buttons.forEach(button => button.classList.remove('selected'));
    }

    // Function to handle the tool button click
    function handleToolButtonClick(event) {
        clearSelection();
        event.target.closest('.tool-button').classList.add('selected');
    }

    // Add event listeners to all tool buttons
    document.querySelectorAll('.tool-button').forEach(button => {
        button.addEventListener('click', handleToolButtonClick);
    });



    // Clear the canvas drawing without removing the background image
    const clearButton = document.getElementById('clearButton');
    clearButton.addEventListener('click', clearCanvas);

    function clearCanvas() {
        shapes = [];
        context.clearRect(0, 0, canvas.width, canvas.height); // Clear the entire canvas
        context.drawImage(imgInstance, 0, 0, canvas.width, canvas.height); // Redraw the background image
    }

    const undoButton = document.getElementById('undoButton');
    undoButton.addEventListener('click', undoLastAction);

    function undoLastAction() {
        if (shapes.length > 0) {
            let lastShape = shapes.pop();
            undoStack.push(lastShape); // Add the removed shape to the undo stack
            redrawShapes();
        }
    }

    const redoButton = document.getElementById('redoButton');
    redoButton.addEventListener('click', redoLastAction);

    function redoLastAction() {
        if (undoStack.length > 0) {
            let lastUndoneShape = undoStack.pop();
            shapes.push(lastUndoneShape); // Add the undone shape back to the shapes array
            redoStack.push(lastUndoneShape); // Add the undone shape to the redo stack
            redrawShapes();
        }
    }

    // Upload the canvas image to the server
    const uploadButton = document.getElementById('uploadButton');
    uploadButton.addEventListener('click', uploadImage);

    function uploadImage() {
        const uploadButton = document.getElementById('uploadButton');
        const documentTypeId = document.getElementById('documentType').value;
        const imageRefNo = uploadButton.getAttribute('data-image-ref-no');

        let imageData = canvas.toDataURL('image/png');
        imageData = imageData.replace(/^data:image\/(png|jpg);base64,/, "")

        let csrfToken = document.getElementById('_csrf').value;
        // let peerUserId = document.getElementById('localUser').value;
        let url = contextRoot + 'api/v1/image/upload-edit-image';
        let data = {
            // accessToken: peerUserId,
            imageData: imageData,
            uploadImageDetailsRefNo: imageRefNo,
            mainDocumentTypeId: documentTypeId
            // index: index,
            // latitude: latitude,
            // longitude: longitude,
            // mainDocumentTypeId: mainDocumentTypeId,
            // subDocumentTypeId: subDocumentTypeId,
        }
        let request = new Request(url, {
            method: 'POST',
            body: JSON.stringify(data),
            headers: new Headers({
                'Accept': 'application/json',
                'Content-Type': 'application/json; charset=UTF-8;x-www-form-urlencoded',
                'X-CSRF-TOKEN': csrfToken
            })
        });

        fetch(request)
            .then(response => response.text())
            .then(result => {
                console.log('Success:', result);
                showToast("Success", "Upload Successfully.", TOAST_TYPE.SUCCESS);

                let thumbImgElements = document.querySelectorAll('img[data-ref-no="' + imageRefNo + '"]');
                let originalImgElements = document.querySelectorAll('a[data-ref-no="' + imageRefNo + '"]');

                thumbImgElements.forEach(function(img) {
                    img.src = contextRoot +'api/v1/image/view-thumb-image/' + imageRefNo + '?' + new Date().getTime(); // Adding timestamp to avoid caching
                });

                originalImgElements.forEach(function(anchor) {
                    anchor.href = contextRoot +'api/v1/image/view-original-image/' + imageRefNo + '?' + new Date().getTime(); // Adding timestamp to avoid caching
                });

                // let modal = document.getElementById("drawingModal");
                // modal.style.display = "none";
                closeDrawingModal();
                reloadImageViewer(document.getElementById("callId").value);
                reloadPreviousImageViewer(document.getElementById("callId").value);

            })
            .catch(error => {
                console.error('Error:', error);
            });


    }


});

function formatDate(date) {
    const dateOptions = {
        year: 'numeric',
        month: 'short',
        day: '2-digit'
    };
    const timeOptions = {
        hour: '2-digit',
        minute: '2-digit',
        hour12: true
    };

    // Format date and time separately
    const formattedDate = new Intl.DateTimeFormat('en-US', dateOptions).format(date);
    const formattedTime = new Intl.DateTimeFormat('en-US', timeOptions).format(date);

    // Combine date and time with the desired separator
    return `${formattedDate} | ${formattedTime}`;
}
