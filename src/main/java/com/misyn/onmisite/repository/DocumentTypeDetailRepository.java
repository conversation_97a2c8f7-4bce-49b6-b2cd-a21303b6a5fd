package com.misyn.onmisite.repository;

import com.misyn.onmisite.entity.DocumentTypeDetail;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface DocumentTypeDetailRepository extends JpaRepository<DocumentTypeDetail, Integer> {

    @Query(value = "SELECT d FROM DocumentTypeDetail d ORDER BY d.documentTypeName ASC")
    List<DocumentTypeDetail> findAllDocumentTypes();
}
