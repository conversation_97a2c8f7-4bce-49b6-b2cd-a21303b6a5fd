package com.misyn.onmisite.repository;

import com.misyn.onmisite.entity.UploadVideoDetails;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

@Repository
public interface UploadVideoDetailsRepository extends JpaRepository<UploadVideoDetails, Long> {
    List<UploadVideoDetails> findAllByJobDetailJobIdOrderByUploadVideoDetailsRefNo(Long jobId);

    List<UploadVideoDetails> findAllByJobDetailJobIdAndCallDetailsCallIdOrderByUploadVideoDetailsRefNo(Long jobId, Long callId);

    int deleteByUploadVideoDetailsRefNo(Long uploadVideoDetailsRefNo);

    @Query(value = "SELECT IFNULL(SUM(file_size),0)  FROM upload_video_details  WHERE company_id=?1 AND created_by=?2 AND CAST(input_date_time AS DATE) BETWEEN ?3 AND ?4 ", nativeQuery = true)
    Long sumUserDataUsage(Long companyId, String userName, Date fromDate, Date toDate);

    @Query(value = "SELECT IFNULL(SUM(file_size),0)  FROM upload_video_details WHERE company_id=?1 AND CAST(input_date_time AS DATE) BETWEEN ?2 AND ?3 ", nativeQuery = true)
    Long sumCompanyDataUsage(Long companyId, Date fromDate, Date toDate);
}
