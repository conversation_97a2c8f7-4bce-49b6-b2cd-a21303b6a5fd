package com.misyn.onmisite.repository;

import com.misyn.onmisite.entity.ClaimAssignClaimHandler;
import com.misyn.onmisite.entity.ClaimTireConditionMe;
import com.misyn.onmisite.entity.ClaimTireConditionMeId;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ClaimHandlerDetailRepository extends JpaRepository<ClaimAssignClaimHandler, Integer> {

    ClaimAssignClaimHandler findByClaimNo(int claimNo);
}
