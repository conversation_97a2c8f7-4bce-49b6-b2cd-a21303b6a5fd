package com.misyn.onmisite.repository;

import com.misyn.onmisite.entity.JobDetail;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.List;
import java.util.Optional;

public interface JobDetailRepository extends JpaRepository<JobDetail, Long>, JobDetailRepositoryCustom {
    Optional<JobDetail> findByJobIdAndCompanyCompanyId(Long jobId, Long companyId);

    Optional<JobDetail> findByJobNumber(String jobNumber);

    List<JobDetail> findAllByCompanyCompanyId(Long companyId);


}
