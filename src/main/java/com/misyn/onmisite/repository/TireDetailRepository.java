package com.misyn.onmisite.repository;

import com.misyn.onmisite.entity.ClaimTireConditionMe;
import com.misyn.onmisite.entity.ClaimTireConditionMeId;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface TireDetailRepository extends JpaRepository<ClaimTireConditionMe, ClaimTireConditionMeId> {

    List<ClaimTireConditionMe> findByClaimNoAndRefNo(Integer claimNo, Integer refNo);

}
