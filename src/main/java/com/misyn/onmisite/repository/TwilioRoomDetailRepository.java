package com.misyn.onmisite.repository;

import com.misyn.onmisite.entity.TwilioRoomDetail;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface TwilioRoomDetailRepository extends JpaRepository<TwilioRoomDetail, Long> {

    Optional<TwilioRoomDetail> findByParticipantIdentity1(String participantIdentity1);

    Optional<TwilioRoomDetail> findByRoomSidAndStatusCallbackEvent(String roomSid, String statusCallbackEvent);

    Optional<TwilioRoomDetail> findByParticipantIdentity2(String participantIdentity2);

    Optional<TwilioRoomDetail> findByRoomName(String roomName);

    Optional<TwilioRoomDetail> findByCallId(Long callId);

    @Query(value = "SELECT * FROM twilio_room_detail WHERE is_composition=?1 AND record_composition_callback_event=?2", nativeQuery = true)
    List<TwilioRoomDetail> findAllByCompositionAndRecordCompositionCallbackEvent(boolean isComposition, String recordCompositionCallbackEvent);
}
