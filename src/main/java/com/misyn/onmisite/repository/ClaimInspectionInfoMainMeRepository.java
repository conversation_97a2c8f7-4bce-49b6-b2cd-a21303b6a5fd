package com.misyn.onmisite.repository;

import com.misyn.onmisite.entity.ClaimInspectionInfoMainMe;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface ClaimInspectionInfoMainMeRepository extends JpaRepository<ClaimInspectionInfoMainMe, Integer> {

    Optional<ClaimInspectionInfoMainMe> findByJobNo(String jobId);

}
