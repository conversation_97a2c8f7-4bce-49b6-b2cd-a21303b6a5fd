package com.misyn.onmisite.repository;

import com.misyn.onmisite.entity.UploadImageDetails;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

@Repository
public interface UploadImageDetailsRepository extends JpaRepository<UploadImageDetails, Long> {
    List<UploadImageDetails> findAllByJobDetailJobIdOrderByUploadImageDetailsRefNo(Long jobId);

    List<UploadImageDetails> findAllByCallDetailsCallIdOrderByUploadImageDetailsRefNo(Long callId);

    List<UploadImageDetails> findAllByJobDetailJobIdAndCallDetailsCallIdOrderByUploadImageDetailsRefNo(Long jobId, Long callId);

    int deleteByUploadImageDetailsRefNo(Long uploadImageDetailsRefNo);

    @Query(value = "SELECT IFNULL(SUM(file_size),0)  FROM upload_image_details WHERE company_id=?1 AND created_by=?2 AND CAST(input_date_time AS DATE) BETWEEN ?3 AND ?4 ", nativeQuery = true)
    Long sumUserDataUsage(Long companyId, String userName, Date fromDate, Date toDate);

    @Query(value = "SELECT IFNULL(SUM(file_size),0)  FROM upload_image_details WHERE company_id=?1 AND CAST(input_date_time AS DATE) BETWEEN ?2 AND ?3 ", nativeQuery = true)
    Long sumCompanyDataUsage(Long companyId, Date fromDate, Date toDate);

    @Modifying
    @Query(value = "update upload_image_details set latitude=?1,longitude=?2 where call_id = ?3", nativeQuery = true)
    int updateGeolocationDetail(String latitude, String longitude, Long call_id);
}
