package com.misyn.onmisite.repository;

import com.misyn.onmisite.entity.CallDetails;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

public interface CallDetailsRepository extends JpaRepository<CallDetails, Long> {

    @Query("SELECT c FROM CallDetails c JOIN FETCH c.jobDetail WHERE c.callId = :callId")
    Optional<CallDetails> findByIdWithJobDetail(@Param("callId") Long callId);

    Optional<CallDetails> findByRoomSid(String roomSid);

    Optional<CallDetails> findByRoomName(String roomName);

    @Modifying
    @Query("UPDATE CallDetails c SET c.roomName = :roomName WHERE c.callId = :callId")
    int updateRoomDetails(@Param("roomName") String roomName, @Param("callId") Long callId);

    @Modifying
    @Query("UPDATE CallDetails c SET c.callStartDateTime = :startDateTime WHERE c.accessToken = :accessToken")
    int updateCallStartDate(@Param("startDateTime") LocalDateTime startDateTime, @Param("accessToken") String accessToken);

    @Modifying
    @Query("UPDATE CallDetails c SET c.localParticipantSid = :localParticipantSid WHERE c.accessToken = :accessToken")
    int updateLocalParticipantSid(@Param("localParticipantSid") String localParticipantSid, @Param("accessToken") String accessToken);

    @Modifying
    @Query("UPDATE CallDetails c SET c.remoteParticipantSid = :remoteParticipantSid WHERE c.accessToken = :accessToken")
    int updateRemoteParticipantSid(@Param("remoteParticipantSid") String remoteParticipantSid, @Param("accessToken") String accessToken);

    @Modifying
    @Query("UPDATE CallDetails c SET c.longitude = :longitude, c.latitude = :latitude WHERE c.accessToken = :accessToken")
    int updateGeolocationDetail(@Param("longitude") Double longitude, @Param("latitude") Double latitude, @Param("accessToken") String accessToken);


    @Modifying
    @Query("UPDATE CallDetails c SET c.recordStatus = 'EXPIRED', c.callEndDateTime = :callEndDateTime WHERE c.accessToken = :accessToken AND c.createdBy = :createdBy")
    int updateCallEndDate(@Param("callEndDateTime") LocalDateTime callEndDateTime, @Param("accessToken") String accessToken, @Param("createdBy") String createdBy);

    @Modifying
    @Query("UPDATE CallDetails c SET c.latitude = :latitude, c.longitude = :longitude WHERE c.accessToken = :accessToken")
    int updateGeolocation(@Param("latitude") String latitude, @Param("longitude") String longitude, @Param("accessToken") String accessToken);


    @Query(value = "SELECT * FROM onmisite_call_details WHERE job_id=?1 AND call_start_date_time IS NOT NULL and latitude IS NOT NULL ORDER BY call_id LIMIT 1", nativeQuery = true)
    Optional<CallDetails> getFirstByJobId(Long jobId);

    Optional<CallDetails> findByAccessTokenAndCreatedBy(String accessToken, String createdBy);

    Optional<CallDetails> findByAccessToken(String accessToken);


    List<CallDetails> findAllByJobDetailJobIdOrderByCallId(Long jobId);

    Optional<CallDetails> findByJobDetailJobIdAndInspectionJobNumber(Long jobDetailJobId, String inspectionJobNumber);
}
