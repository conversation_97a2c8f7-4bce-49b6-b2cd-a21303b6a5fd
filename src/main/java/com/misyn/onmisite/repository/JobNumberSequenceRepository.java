package com.misyn.onmisite.repository;

import com.misyn.onmisite.entity.JobNumberSequence;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

@Repository
public interface JobNumberSequenceRepository extends JpaRepository<JobNumberSequence, Long> {

    JobNumberSequence findByCompanyCompanyId(Long companyId);

    @Modifying
    @Query(value = "update job_number_sequence set next_job_number=IFNULL(next_job_number,0)+1 where company_id = ?1", nativeQuery = true)
    int updateNextJobNumber(Long companyId);
}
