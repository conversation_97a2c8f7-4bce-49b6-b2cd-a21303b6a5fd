package com.misyn.onmisite.repository;


import com.misyn.onmisite.entity.TwilioVideoRecordingLog;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

public interface TwilioVideoRecordingLogRepository extends JpaRepository<TwilioVideoRecordingLog, Long> {

    Optional<TwilioVideoRecordingLog> findBySourceSid(String sourceSid);

    Optional<TwilioVideoRecordingLog> findByRoomName(String roomName);

    //@Query(value = "SELECT * FROM onmisite_twilio_video_recording_log WHERE remote_participant=?1 AND run_composition=0 AND status_callback_event='recording-completed' GROUP BY composition_sid HAVING count(composition_sid)>1", nativeQuery = true)
    @Query("SELECT t FROM TwilioVideoRecordingLog t " +
            "WHERE t.remoteParticipant = ?1 " +
            "AND t.runComposition = false " +
            "AND t.statusCallbackEvent = 'recording-completed' " +
            "GROUP BY t.participantSid " +
            "HAVING COUNT(t.participantSid) > 1")
    List<TwilioVideoRecordingLog> findAllByRemoteParticipant(Boolean isRemoteParticipant);

    /*@Query(value = "SELECT " +
            " *  " +
            "FROM " +
            " onmisite_twilio_video_recording_log  " +
            "WHERE " +
            " remote_participant = 1  " +
            " AND run_composition = 1 " +
            " AND file_downloaded=0  " +
            "GROUP BY " +
            " room_sid", nativeQuery = true)*/
    @Query("SELECT t FROM TwilioVideoRecordingLog t " +
            "WHERE t.remoteParticipant = true " +
            "AND t.runComposition = true " +
            "AND t.statusCallbackEvent = 'recording-completed' " +
            "AND t.fileDownloaded = false " +
            "GROUP BY t.participantSid " +
            "HAVING COUNT(t.participantSid) > 1")
    List<TwilioVideoRecordingLog> findAllByPendingDownload();

    @Transactional
    @Modifying
    @Query("UPDATE TwilioVideoRecordingLog t " +
            "SET t.runComposition = true, t.compositionSid = ?1 " +
            "WHERE t.participantSid = ?2 AND t.roomSid = ?3")
    void updateLocalParticipantSid(String compositionSid, String participantSid, String roomSid);

    @Transactional
    @Modifying
    @Query("UPDATE TwilioVideoRecordingLog t " +
            "SET t.fileDownloaded = true " +
            "WHERE t.compositionSid = ?1")
    void updateFileDownloadedStatus(String compositionSid);

   /* @Modifying
    @Query(value = "UPDATE onmisite_twilio_video_recording_log SET run_composition=1,composition_sid=?1 WHERE  participant_sid=?2 AND room_sid=?3", nativeQuery = true)
    int updateLocalParticipantSid(String compositionSid, String participantSid, String roomSid);

    @Modifying
    @Query(value = "UPDATE onmisite_twilio_video_recording_log SET file_downloaded=1 WHERE  composition_sid=?1", nativeQuery = true)
    int updateFileDownloadedStatus(String compositionSid);*/
}
