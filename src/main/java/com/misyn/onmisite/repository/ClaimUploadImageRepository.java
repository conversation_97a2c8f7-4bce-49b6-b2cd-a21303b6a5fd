package com.misyn.onmisite.repository;

import com.misyn.onmisite.dto.ClaimPreviousPhotoDto;
import com.misyn.onmisite.entity.ClaimUploadImage;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ClaimUploadImageRepository extends JpaRepository<ClaimUploadImage, Integer> {

    @Query(value = """
        SELECT  
            ci.N_CLIM_NO as claimNumber, 
            cim.n_inspection_id as inspectionId, 
            cit.inspection_type_desc as inspectionType, 
            ci.N_REF_NO as referenceNumber, 
            ci.V_DOC_NAME as documentName, 
            ci.V_DOC_PATH as documentPath, 
            cdt.N_DOC_TYPE_ID as documentTypeId, 
            cdt.V_DOC_TYPE_NAME as documentTypeName 
        FROM  
            claim_upload_images ci 
            INNER JOIN claim_inspection_info_main cim ON ci.N_CLIM_NO = cim.n_claim_no 
            INNER JOIN claim_inspection_type cit ON cim.n_inspection_type = cit.inspection_type_id 
            INNER JOIN claim_document_type cdt ON ci.N_DOC_TYPE_ID = cdt.N_DOC_TYPE_ID 
        WHERE  
            ci.N_CLIM_NO = :claimNumber 
        ORDER BY  
            ci.N_REF_NO
        """, nativeQuery = true)
    List<Object[]> findPreviousPhotosByClaimNumber(@Param("claimNumber") Integer claimNumber);
}
