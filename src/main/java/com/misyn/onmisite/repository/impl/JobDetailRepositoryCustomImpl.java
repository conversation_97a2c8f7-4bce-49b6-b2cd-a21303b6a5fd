package com.misyn.onmisite.repository.impl;

import com.misyn.onmisite.dto.JobCountDto;
import com.misyn.onmisite.repository.JobDetailRepositoryCustom;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.List;

@Slf4j
public class JobDetailRepositoryCustomImpl implements JobDetailRepositoryCustom {
    private static final String COUNT_SQL_BY_COMPANY = "SELECT\n" +
            "COUNT(j.created_by) as cnt,\n" +
            "j.created_by,\n" +
            "j.company_id,\n" +
            "c.company_name\n" +
            "FROM\n" +
            "job_detail AS j\n" +
            "INNER JOIN `user` AS u ON j.created_by = u.username\n" +
            "INNER JOIN company AS c ON u.company_id = c.company_id\n" +
            "WHERE\n" +
            "j.company_id = ?\n" +
            "GROUP BY\n" +
            "j.created_by";

    private static final String COUNT_SQL_BY_CREATED_BY = "SELECT\n" +
            "COUNT(j.created_by) as cnt,\n" +
            "j.created_by,\n" +
            "j.company_id,\n" +
            "c.company_name\n" +
            "FROM\n" +
            "job_detail AS j\n" +
            "INNER JOIN `user` AS u ON j.created_by = u.username\n" +
            "INNER JOIN company AS c ON u.company_id = c.company_id\n" +
            "WHERE\n" +
            "j.company_id = ? AND j.created_by=?\n" +
            "GROUP BY\n" +
            "j.created_by";


    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Override
    public List<JobCountDto> getJobCountDto(Long companyId) {
        return jdbcTemplate.query(
                COUNT_SQL_BY_COMPANY,
                new Object[]{companyId},
                (rs, rowNum) ->
                        this.getJobCountDto(rs)
        );
    }

    @Override
    public List<JobCountDto> getJobCountDto(Long companyId, String createdBy) {
        return jdbcTemplate.query(
                COUNT_SQL_BY_CREATED_BY,
                new Object[]{companyId, createdBy},
                (rs, rowNum) ->
                        this.getJobCountDto(rs)
        );
    }


    private JobCountDto getJobCountDto(ResultSet rs) {
        JobCountDto jobCountDto = new JobCountDto();
        try {
            jobCountDto.setJobCount(rs.getInt("cnt"));
            jobCountDto.setJobCreatedBy(rs.getString("j.created_by"));
            jobCountDto.setCompanyId(rs.getLong("j.company_id"));
            jobCountDto.setCompanyName(rs.getString("c.company_name"));
        } catch (SQLException e) {
            log.error(e.getMessage(), e);
        }
        return jobCountDto;
    }
}
