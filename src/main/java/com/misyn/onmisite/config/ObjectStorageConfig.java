package com.misyn.onmisite.config;

import com.oracle.bmc.Region;
import com.oracle.bmc.auth.SimpleAuthenticationDetailsProvider;
import com.oracle.bmc.auth.StringPrivateKeySupplier;
import com.oracle.bmc.objectstorage.ObjectStorage;
import com.oracle.bmc.objectstorage.ObjectStorageClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.io.InputStream;
import java.util.function.Supplier;

@Configuration
public class ObjectStorageConfig {
    @Value("${oci.private-key}")
    private String privateKey;
    @Value("${oci.user}")
    private String user;
    @Value("${oci.tenancy}")
    private String tenancy;
    @Value("${oci.fingerprint}")
    private String fingerprint;
    @Value("${oci.region}")
    private String region;

    @Bean
    public ObjectStorage objectStorage() {
        Supplier<InputStream> privateKeySupplier
                = new StringPrivateKeySupplier(privateKey);
        final SimpleAuthenticationDetailsProvider provider = SimpleAuthenticationDetailsProvider.builder()
                .tenantId(tenancy)
                .userId(user)
                .fingerprint(fingerprint)
                .region(Region.fromRegionCodeOrId(region))
                .privateKeySupplier(privateKeySupplier)
                .build();
        return ObjectStorageClient.builder().region(Region.AP_SINGAPORE_1).build(provider);

    }
}
