package com.misyn.onmisite.config;

import com.misyn.onmisite.util.Utility;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.domain.AuditorAware;
import org.springframework.data.jpa.repository.config.EnableJpaAuditing;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.oauth2.client.authentication.OAuth2AuthenticationToken;
import org.springframework.security.oauth2.core.user.OAuth2User;
import org.springframework.security.oauth2.server.resource.authentication.JwtAuthenticationToken;

import java.util.Optional;

@Configuration
@EnableJpaAuditing(auditorAwareRef = "auditorProvider")
public class JpaAuditingConfiguration {

    @Bean
    public AuditorAware<String> auditorProvider() {

        return () -> {
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            if (authentication == null) {
                return Optional.of("SystemUser");
            }
            if (authentication instanceof OAuth2User){
                OAuth2User user = (OAuth2User) authentication.getPrincipal();
                return Optional.of(Utility.getUsername((String) user.getAttribute("preferred_username")));
            }else  if (authentication instanceof OAuth2AuthenticationToken user){
                return Optional.of(Utility.getUsername(user.getPrincipal().getAttribute("preferred_username")));
            } else if (authentication instanceof JwtAuthenticationToken jwtAuthenticationToken) {
                return Optional.of(Utility.getUsername(jwtAuthenticationToken.getToken().getClaimAsString("preferred_username")));
            } else {
                return Optional.of("anonymousUser");
            }

        };

    }
}
