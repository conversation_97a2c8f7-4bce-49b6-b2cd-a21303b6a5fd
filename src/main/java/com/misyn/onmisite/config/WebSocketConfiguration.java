package com.misyn.onmisite.config;


import com.fasterxml.jackson.databind.ObjectMapper;
import com.misyn.onmisite.ws.AppWebSocketHandler;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.socket.config.annotation.EnableWebSocket;
import org.springframework.web.socket.config.annotation.WebSocketConfigurer;
import org.springframework.web.socket.config.annotation.WebSocketHandlerRegistry;
import org.springframework.web.socket.server.standard.ServletServerContainerFactoryBean;

@RequiredArgsConstructor
@Configuration
@EnableWebSocket
public class WebSocketConfiguration implements WebSocketConfigurer {

    private final ObjectMapper objectMapper;

    @Bean
    public ServletServerContainerFactoryBean createWebSocketContainer() {
        ServletServerContainerFactoryBean container = new ServletServerContainerFactoryBean();
        container.setMaxBinaryMessageBufferSize(1024000);
        container.setMaxSessionIdleTimeout(1000 * 60 * 60L);
        container.setAsyncSendTimeout(1000 * 60 * 60L);
        return container;
    }

    @Override
    public void registerWebSocketHandlers(WebSocketHandlerRegistry registry) {
        registry.addHandler(appWebSocketHandler(), "/socket")
                .setAllowedOrigins("*");
    }


    @Bean
    public org.springframework.web.socket.WebSocketHandler appWebSocketHandler() {
        return new AppWebSocketHandler(objectMapper);
    }


}
