package com.misyn.onmisite.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.config.annotation.method.configuration.EnableMethodSecurity;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configurers.HeadersConfigurer;
import org.springframework.security.oauth2.client.oidc.web.logout.OidcClientInitiatedLogoutSuccessHandler;
import org.springframework.security.oauth2.client.registration.ClientRegistrationRepository;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.security.web.authentication.logout.LogoutSuccessHandler;


@Configuration
@EnableWebSecurity
//@EnableMethodSecurity(jsr250Enabled = true)
public class SecurityConfig {
    private final ClientRegistrationRepository clientRegistrationRepository;
    private final JwtAuthConverter jwtAuthConverter;
    @Value("${application.mcms.post-logout-redirect-uri}")
    private String postLogoutRedirectUri;

    public SecurityConfig(ClientRegistrationRepository clientRegistrationRepository,JwtAuthConverter jwtAuthConverter) {
        this.clientRegistrationRepository = clientRegistrationRepository;
        this.jwtAuthConverter = jwtAuthConverter;
    }


    @Bean
    public SecurityFilterChain filterChain(HttpSecurity http) throws Exception {

        http.headers(headers -> headers
                        .frameOptions(HeadersConfigurer.FrameOptionsConfig::sameOrigin) // Allow iframes from the same origin
                )
                .authorizeHttpRequests(authorize -> authorize
                        .requestMatchers("/resources/**",
                                "/admin-view/**",
                                "/error/**",
                                "/api/logout",
                                "/api/v1/client/**",
                                "/js/**",
                                "/socket/**",
                                "/api/v1/client/update/geolocation",
                                "/api/v1/twilio/admin/token",
                                "/api/v1/twilio/client/token",
                                "/api/v1/twilio/webhook/**").permitAll()
                        .anyRequest().authenticated()
                )
                // .oauth2Login(withDefaults())
                .oauth2Login(oauth2 -> oauth2// Sets custom login page for OAuth2 with Keycloak
                        .defaultSuccessUrl("/redirect-after-login", true) // Redirects to "/menu" after successful login
                )
                .logout(logout -> logout
                        .logoutUrl("/logout")
                        .logoutSuccessHandler(oidcLogoutSuccessHandler())

                        .invalidateHttpSession(true)
                        .clearAuthentication(true)
                        .deleteCookies("JSESSIONID")
                ).oauth2ResourceServer(oauth -> oauth.jwt(jwtSpec -> jwtSpec.jwtAuthenticationConverter(jwtAuthConverter)));
        http.csrf(csrf -> csrf
                .ignoringRequestMatchers("/api/v1/twilio/webhook/**")
        );
        return http.build();
    }

    private LogoutSuccessHandler oidcLogoutSuccessHandler() {
        OidcClientInitiatedLogoutSuccessHandler oidcLogoutSuccessHandler =
                new OidcClientInitiatedLogoutSuccessHandler(this.clientRegistrationRepository);

        oidcLogoutSuccessHandler.setPostLogoutRedirectUri(postLogoutRedirectUri);


        return oidcLogoutSuccessHandler;
    }
}
