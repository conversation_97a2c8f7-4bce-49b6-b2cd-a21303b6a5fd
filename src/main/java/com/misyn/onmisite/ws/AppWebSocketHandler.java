package com.misyn.onmisite.ws;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.misyn.onmisite.ws.dto.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Component;
import org.springframework.web.socket.BinaryMessage;
import org.springframework.web.socket.CloseStatus;
import org.springframework.web.socket.TextMessage;
import org.springframework.web.socket.WebSocketSession;
import org.springframework.web.socket.handler.AbstractWebSocketHandler;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.IOException;
import java.nio.ByteBuffer;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@RequiredArgsConstructor
@Slf4j
@Component
public class AppWebSocketHandler extends AbstractWebSocketHandler {
    private static final String USER_NAME_KEY = "name";
    private static final String CONNECTED_USER_NAME_KEY = "otherName";
    private final Map<String, WebSocketSession> SESSION_MAP = new ConcurrentHashMap<>();
    private final ObjectMapper objectMapper;
    @Value("${application.image.path}")
    private String imagePath;

    @Override
    protected void handleTextMessage(@NonNull WebSocketSession session, TextMessage message) throws IOException {
        String stringFieldJsonMessage;
        Data data = objectMapper.readValue(message.getPayload(), Data.class);
        WebSocketSession webSocketSession;
        WebSocketSession removeWebSocketSession;
        ErrorData errorData;
        String connectedUsername;
        try {
            switch (data.getType()) {
                case "login":
                    log.info("User logged :{}", data.getName());
                    LoginData loginData = new LoginData();
                    loginData.setType(data.getType());
                    if (SESSION_MAP.containsKey(data.getName())) {
                        loginData.setSuccess(false);
                    } else {
                        session.getAttributes().put("name", data.getName());
                        SESSION_MAP.put(data.getName(), session);
                        session.getAttributes().put(CONNECTED_USER_NAME_KEY, data.getConnectedUser());
                        loginData.setSuccess(true);
                    }

                    if (SESSION_MAP.containsKey(data.getConnectedUser())) {
                        loginData.setConnectedUser(data.getConnectedUser());
                        loginData.setPeerConnected(true);
                    }
                    stringFieldJsonMessage = objectMapper.writeValueAsString(loginData);
                    session.sendMessage(new TextMessage(stringFieldJsonMessage));
                    break;
                case "logout":
                    removeWebSocketSession = SESSION_MAP.remove(data.getName());
                    if (removeWebSocketSession != null && removeWebSocketSession.isOpen()) {
                        removeWebSocketSession.close();
                    }
                    break;
                case "offer":
                    log.info("Sending offer to:  :{}", data.getName());
                    webSocketSession = SESSION_MAP.get(data.getName());
                    if (webSocketSession != null) {
                        session.getAttributes().put(CONNECTED_USER_NAME_KEY, data.getName());
                        OfferData offerData = new OfferData();
                        offerData.setType("offer");
                        offerData.setOffer(data.getOffer());
                        offerData.setName((String) session.getAttributes().get("name"));
                        stringFieldJsonMessage = objectMapper.writeValueAsString(offerData);
                        webSocketSession.sendMessage(new TextMessage(stringFieldJsonMessage));
                    }
                    break;
                case "answer":
                    log.info("Sending answer to:{}", data.getName());
                    webSocketSession = SESSION_MAP.get(data.getName());
                    if (webSocketSession != null) {
                        session.getAttributes().put(CONNECTED_USER_NAME_KEY, data.getName());
                        AnswerData answerData = new AnswerData();
                        answerData.setType("answer");
                        answerData.setAnswer(data.getAnswer());
                        stringFieldJsonMessage = objectMapper.writeValueAsString(answerData);
                        webSocketSession.sendMessage(new TextMessage(stringFieldJsonMessage));
                    }
                    break;
                case "candidate":
                    log.info("Sending candidate to:{}", data.getName());
                    webSocketSession = SESSION_MAP.get(data.getName());
                    if (webSocketSession != null) {
                        CandidateData candidateData = new CandidateData();
                        candidateData.setType("candidate");
                        candidateData.setCandidate(data.getCandidate());
                        stringFieldJsonMessage = objectMapper.writeValueAsString(candidateData);
                        webSocketSession.sendMessage(new TextMessage(stringFieldJsonMessage));
                    }
                    break;
                case "leave":
                    log.info("Leave from : {}", data.getName());
                    removeWebSocketSession = SESSION_MAP.remove(data.getName());
                    if (removeWebSocketSession != null && removeWebSocketSession.isOpen()) {
                        removeWebSocketSession.close();
                    }
                    webSocketSession = SESSION_MAP.remove(data.getConnectedUser());
                    if (webSocketSession != null) {
                        LeaveData leaveData = new LeaveData();
                        leaveData.setType("leave");
                        stringFieldJsonMessage = objectMapper.writeValueAsString(leaveData);
                        webSocketSession.sendMessage(new TextMessage(stringFieldJsonMessage));
                    }
                    break;
                case "leave-client":
                    log.info("Disconnecting from:{}", data.getName());
                    webSocketSession = SESSION_MAP.get(data.getName());
                    if (webSocketSession != null) {
                        LeaveData leaveData = new LeaveData();
                        leaveData.setType("leave");
                        stringFieldJsonMessage = objectMapper.writeValueAsString(leaveData);
                        webSocketSession.sendMessage(new TextMessage(stringFieldJsonMessage));
                    }
                    break;
                case "ping":
                    log.info("PING");
                    errorData = new ErrorData();
                    errorData.setType("ping");
                    errorData.setMessage("PING: " + data.getName());
                    stringFieldJsonMessage = objectMapper.writeValueAsString(errorData);
                    session.sendMessage(new TextMessage(stringFieldJsonMessage));
                    break;
                case "geolocation":
                    errorData = new ErrorData();
                    errorData.setType("geolocation");
                    errorData.setMessage(data.getName());
                    errorData.setLatitude(data.getLatitude());
                    errorData.setLongitude(data.getLongitude());
                    stringFieldJsonMessage = objectMapper.writeValueAsString(errorData);
                    session.sendMessage(new TextMessage(stringFieldJsonMessage));

                    connectedUsername = (String) session.getAttributes().get(CONNECTED_USER_NAME_KEY);
                    if (null != connectedUsername) {
                        webSocketSession = SESSION_MAP.get(data.getName());
                        if (null != webSocketSession) {
                            webSocketSession.sendMessage(new TextMessage(stringFieldJsonMessage));
                        }
                    }
                    break;
                case "recording":
                    RecordingData recordingData = new RecordingData();
                    recordingData.setType(data.getType());
                    recordingData.setMessage(data.getType());
                    recordingData.setRecordStart(data.isRecordStart());
                    stringFieldJsonMessage = objectMapper.writeValueAsString(recordingData);
                    connectedUsername = (String) session.getAttributes().get(CONNECTED_USER_NAME_KEY);
                    if (null != connectedUsername) {
                        webSocketSession = SESSION_MAP.get(connectedUsername);
                        if (null != webSocketSession) {
                            webSocketSession.sendMessage(new TextMessage(stringFieldJsonMessage));
                        }
                    }
                    break;
                case "imageCapture":
                    ImageCaptureData imageCaptureData = new ImageCaptureData();
                    imageCaptureData.setMessage(data.getType());
                    imageCaptureData.setType(data.getType());
                    imageCaptureData.setMainDocumentTypeId(data.getMainDocumentTypeId());
                    imageCaptureData.setSubDocumentTypeId(data.getSubDocumentTypeId());
                    stringFieldJsonMessage = objectMapper.writeValueAsString(imageCaptureData);
                    connectedUsername = (String) session.getAttributes().get(CONNECTED_USER_NAME_KEY);
                    if (null != connectedUsername) {
                        webSocketSession = SESSION_MAP.get(connectedUsername);
                        if (null != webSocketSession) {
                            webSocketSession.sendMessage(new TextMessage(stringFieldJsonMessage));
                        }
                    }
                    break;
                case "torchOn":
                case "torchOff":
                    TorchData torchData = new TorchData();
                    torchData.setOn(data.getType().equals("torchOn"));
                    torchData.setType(data.getType());
                    stringFieldJsonMessage = objectMapper.writeValueAsString(torchData);
                    connectedUsername = (String) session.getAttributes().get(CONNECTED_USER_NAME_KEY);
                    if (null != connectedUsername) {
                        webSocketSession = SESSION_MAP.get(connectedUsername);
                        if (null != webSocketSession) {
                            webSocketSession.sendMessage(new TextMessage(stringFieldJsonMessage));
                        }
                    }
                    break;
                case "switchCameraFront":
                case "switchCameraRear":
                    SwitchCameraData switchCameraData = new SwitchCameraData();
                    switchCameraData.setFacingMode(data.getType().equals("front") ? "front" : "rear");
                    switchCameraData.setType(data.getType());
                    stringFieldJsonMessage = objectMapper.writeValueAsString(switchCameraData);
                    connectedUsername = (String) session.getAttributes().get(CONNECTED_USER_NAME_KEY);
                    if (null != connectedUsername) {
                        webSocketSession = SESSION_MAP.get(connectedUsername);
                        if (null != webSocketSession) {
                            webSocketSession.sendMessage(new TextMessage(stringFieldJsonMessage));
                        }
                    }
                    break;
                case "cameraZoomIn":
                case "cameraZoomOut":
                    CameraZoomData cameraZoomData = new CameraZoomData();
                    cameraZoomData.setZoomType(data.getType().equals("zoom-in") ? "zoom-in" : "zoom-out");
                    cameraZoomData.setType(data.getType());
                    stringFieldJsonMessage = objectMapper.writeValueAsString(cameraZoomData);
                    connectedUsername = (String) session.getAttributes().get(CONNECTED_USER_NAME_KEY);
                    if (null != connectedUsername) {
                        webSocketSession = SESSION_MAP.get(connectedUsername);
                        if (null != webSocketSession) {
                            webSocketSession.sendMessage(new TextMessage(stringFieldJsonMessage));
                        }
                    }
                    break;

                case "peer-disconnected":
                    errorData = new ErrorData();
                    errorData.setType("peer-disconnected");
                    errorData.setMessage("peer-disconnected");
                    stringFieldJsonMessage = objectMapper.writeValueAsString(errorData);
                    connectedUsername = (String) session.getAttributes().get(CONNECTED_USER_NAME_KEY);
                    if (null != connectedUsername) {
                        webSocketSession = SESSION_MAP.get(connectedUsername);
                        if (null != webSocketSession) {
                            webSocketSession.sendMessage(new TextMessage(stringFieldJsonMessage));
                        }
                    }
                    break;
                default:
                    errorData = new ErrorData();
                    errorData.setType("error");
                    errorData.setMessage("Command not found: " + data.getType());
                    stringFieldJsonMessage = objectMapper.writeValueAsString(errorData);
                    session.sendMessage(new TextMessage(stringFieldJsonMessage));
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }

    @Override
    protected void handleBinaryMessage(WebSocketSession session, BinaryMessage message) throws IOException {

        ByteBuffer payload = message.getPayload();
        String connectedUserName = (String) session.getAttributes().get(CONNECTED_USER_NAME_KEY);
        if (connectedUserName != null) {
            convertAndSaveImage(payload, connectedUserName);
        }
    }

    private void convertAndSaveImage(ByteBuffer payload, String connectedUserName) {
        log.info(imagePath);
        try {
            String imageFileName = imagePath.concat(connectedUserName)
                    .concat("_").concat(String.valueOf(System.nanoTime())).concat(".png");

            byte[] data = payload.array();
            ByteArrayInputStream bis = new ByteArrayInputStream(data);
            BufferedImage bImage2 = ImageIO.read(bis);
            ImageIO.write(bImage2, "png", new File(imageFileName));
            bImage2.flush();
            bis.close();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }

    @Override
    public void afterConnectionEstablished(@NonNull WebSocketSession session) throws Exception {
        log.info("User connected :{}", session.getAttributes().get(USER_NAME_KEY));
    }

    @Override
    public void afterConnectionClosed(@NonNull WebSocketSession session, @NonNull CloseStatus status) throws Exception {
        try {
            WebSocketSession removeWebSocketSession = SESSION_MAP.remove(String.valueOf(session.getAttributes().get(USER_NAME_KEY)));
            if (removeWebSocketSession != null && removeWebSocketSession.isOpen()) {
                removeWebSocketSession.close();
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }


}
