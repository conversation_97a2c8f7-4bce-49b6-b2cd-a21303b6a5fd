package com.misyn.onmisite.entity;

import java.io.Serializable;
import java.util.Objects;

public class ClaimTireConditionMeId implements Serializable {

    private Integer refNo;
    private Integer claimNo;
    private Integer position;

    // Required: Default constructor
    public ClaimTireConditionMeId() {}

    public ClaimTireConditionMeId(Integer refNo, Integer claimNo, Integer position) {
        this.refNo = refNo;
        this.claimNo = claimNo;
        this.position = position;
    }

    // Required: equals() and hashCode()
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof ClaimTireConditionMeId)) return false;
        ClaimTireConditionMeId that = (ClaimTireConditionMeId) o;
        return Objects.equals(refNo, that.refNo) &&
                Objects.equals(claimNo, that.claimNo) &&
                Objects.equals(position, that.position);
    }

    @Override
    public int hashCode() {
        return Objects.hash(refNo, claimNo, position);
    }
}
