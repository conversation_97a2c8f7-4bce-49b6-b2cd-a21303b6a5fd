package com.misyn.onmisite.entity;

import jakarta.persistence.*;
import lombok.Data;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@Entity
@Data
@Table(name = "claim_inspection_info_main")
public class ClaimInspectionDetail {

    @Id
    @Column(name = "n_inspection_id")
    private Integer inspectionId;

    @Column(name = "n_ref_no", nullable = false)
    private Integer refNo;

    @Column(name = "v_job_no", length = 15, nullable = false)
    private String jobNo;

    @Column(name = "n_claim_no", nullable = false)
    private Integer claimNo;

    @Column(name = "v_make_confirm", nullable = false)
    private String makeConfirm;

    @Column(name = "v_model_confirm", nullable = false)
    private String modelConfirm;

    @Column(name = "v_eng_no_confirm", nullable = false)
    private String engNoConfirm;

    @Column(name = "v_chassis_no_confirm", nullable = false)
    private String chassisNoConfirm;

    @Column(name = "v_chassis_no", length = 45, nullable = false)
    private String chassisNo;

    @Column(name = "n_not_checked_reason", nullable = false)
    private Integer notCheckedReason;

    @Column(name = "v_year_make_confirm", nullable = false)
    private String yearMakeConfirm;

    @Column(name = "d_inspect_datetime", nullable = false)
    private LocalDateTime inspectDatetime;

    @Column(name = "n_pav", precision = 20, scale = 2, nullable = false)
    private BigDecimal pav;

    @Column(name = "v_damage_details", length = 255, nullable = false)
    private String damageDetails;

    @Column(name = "v_pad", length = 500, nullable = false)
    private String pad;

    @Column(name = "v_genun_of_accid", nullable = false)
    private String genunOfAccid;

    @Column(name = "v_first_statement_rqed", nullable = false)
    private String firstStatementRqed;

    @Column(name = "v_first_statement_req_reason", length = 5, nullable = false)
    private String firstStatementReqReason;

    @Column(name = "v_invest_rqed", nullable = false)
    private String investRqed;

    @Column(name = "v_assessor_remark", length = 1000, nullable = false)
    private String assessorRemark;

    @Column(name = "n_inspection_type", nullable = false)
    private Integer inspectionType;

    @Column(name = "n_job_type", nullable = false)
    private Integer jobType;

    @Column(name = "v_assigned_location", length = 255, nullable = false)
    private String assignedLocation;

    @Column(name = "v_current_location", length = 255, nullable = false)
    private String currentLocation;

    @Column(name = "v_place_of_inspection", length = 255, nullable = false)
    private String placeOfInspection;

    @Column(name = "n_mileage", nullable = false)
    private Integer mileage;

    @Column(name = "n_cost_of_call", precision = 20, scale = 2, nullable = false)
    private BigDecimal costOfCall;

    @Column(name = "n_other_fee", precision = 20, scale = 2, nullable = false)
    private BigDecimal otherFee;

    @Column(name = "n_total_assessor_fee", precision = 20, scale = 2, nullable = false)
    private BigDecimal totalAssessorFee;

    @Column(name = "v_fee_desc", length = 255, nullable = false)
    private String feeDesc;

    @Column(name = "n_record_status", nullable = false)
    private Integer recordStatus;

    @Column(name = "v_inpuser", length = 45, nullable = false)
    private String inpUser;

    @Column(name = "d_inpdatetime", nullable = false)
    private LocalDateTime inpDatetime;

    @Column(name = "v_ass_fee_apr_status", length = 5, nullable = false)
    private String assFeeAprStatus;

    @Column(name = "v_ass_fee_apr_user", length = 45, nullable = false)
    private String assFeeAprUser;

    @Column(name = "v_ass_fee_apr_datetime", nullable = false)
    private LocalDateTime assFeeAprDatetime;

    @Column(name = "v_ass_esti_apr_status", length = 5, nullable = false)
    private String assEstiAprStatus;

    @Column(name = "v_ass_esti_apr_user", length = 45, nullable = false)
    private String assEstiAprUser;

    @Column(name = "v_ass_esti_apr_datetime", nullable = false)
    private LocalDateTime assEstiAprDatetime;

    @Column(name = "v_assign_assessor_user", length = 45, nullable = false)
    private String assignAssessorUser;

    @Column(name = "d_assign_assessor_datetime", nullable = false)
    private LocalDateTime assignAssessorDatetime;

    @Column(name = "v_assign_rte_user", length = 45, nullable = false)
    private String assignRteUser;

    @Column(name = "d_assign_rte_datetime", nullable = false)
    private LocalDateTime assignRteDatetime;

    @Column(name = "v_approve_assign_rte_user", length = 45, nullable = false)
    private String approveAssignRteUser;

    @Column(name = "d_approve_assign_datetime", nullable = false)
    private LocalDateTime approveAssignDatetime;

    @Column(name = "v_fwd_rte_desktop_user", length = 45, nullable = false)
    private String fwdRteDesktopUser;

    @Column(name = "d_fwd_rte_desktop_datetime", nullable = false)
    private LocalDateTime fwdRteDesktopDatetime;

    @Column(name = "v_assign_tc_user", length = 45, nullable = false)
    private String assignTcUser;

    @Column(name = "d_assign_tc_datetime", nullable = false)
    private LocalDateTime assignTcDatetime;

    @Column(name = "v_fwd_tc_user", length = 45, nullable = false)
    private String fwdTcUser;

    @Column(name = "d_fwd_tc_datetime", nullable = false)
    private LocalDateTime fwdTcDatetime;

    @Column(name = "v_fwd_tc_desktop_user", length = 45, nullable = false)
    private String fwdTcDesktopUser;

    @Column(name = "d_fwd_tc_desktop_datetime", nullable = false)
    private LocalDateTime fwdTcDesktopDatetime;

    @Column(name = "is_vehicle_available", length = 2, nullable = false)
    private String vehicleAvailable;
}
