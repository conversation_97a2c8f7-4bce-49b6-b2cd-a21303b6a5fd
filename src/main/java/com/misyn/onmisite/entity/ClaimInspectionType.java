package com.misyn.onmisite.entity;

import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

@Entity
@Table(name = "claim_inspection_type")
@Getter
@Setter
public class ClaimInspectionType implements Serializable {

    @Id
    @Column(name = "inspection_type_id")
    private Integer inspectionTypeId;

    @Column(name = "inspection_type_desc")
    private String inspectionTypeDesc;
}
