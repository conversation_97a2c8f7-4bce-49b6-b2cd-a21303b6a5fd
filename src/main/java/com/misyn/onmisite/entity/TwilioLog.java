package com.misyn.onmisite.entity;

import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.envers.Audited;

import java.time.LocalDateTime;
import java.util.Date;

@Table(name = "onmisite_twilio_log")
@Entity
@Getter
@Setter
//@Audited
public class TwilioLog {


    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    private Long logId;
    @Column
    private String roomSid;
    @Column
    private String roomName;
    @Column
    private String participantSid;
    @Column
    private String sequenceNumber;
    @Column
    private String statusCallbackEvent;
    @Column
    private LocalDateTime inpDateTime;
}
