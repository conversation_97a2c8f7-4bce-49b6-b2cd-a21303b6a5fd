package com.misyn.onmisite.entity;


import com.misyn.onmisite.enums.RecordStatus;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@Table(name = "onmisite_upload_image_details")
@Entity
@Getter
@Setter
//@Audited
@EntityListeners(AuditingEntityListener.class)
public class UploadImageDetails implements Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long uploadImageDetailsRefNo;
    @Column
    private Integer imageIndex;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = EntityFieldName.JOB_ID)
    private JobDetail jobDetail;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = EntityFieldName.CALL_ID)
    private CallDetails callDetails;

    @Column
    private String imageName;
    @Column
    private String thumbImageName;
    @Column
    private String thumbImagePath;
    @Column
    private String imagePath;
    @Column
    private String editImageName;
    @Column
    private String editImagePath;
    @Column
    private String thumbEditImageName;
    @Column
    private String thumbEditImagePath;
    @Column
    @Temporal(TemporalType.TIMESTAMP)
    private LocalDateTime inputDateTime;

    @Column
    private String latitude;
    @Column
    private String longitude;
    @Column
    private BigDecimal fileSize;
    @Column
    private BigDecimal thumbFileSize;
    @Column
    private BigDecimal distance;
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = EntityFieldName.COMPANY_ID)
    private Company company;
    @Column
    private Integer documentTypeId;
    @Column(nullable = false, updatable = false)
    @CreatedBy
    private String createdBy = "";
    @LastModifiedBy
    private String modifiedBy;
    @Column(nullable = false)
    @Enumerated(EnumType.STRING)
    private RecordStatus recordStatus = RecordStatus.ACTIVE;
    @Column(nullable = false, updatable = false)
    @CreatedDate
    private LocalDateTime createdDate;
    @LastModifiedDate
    private LocalDateTime modifiedDate;

}
