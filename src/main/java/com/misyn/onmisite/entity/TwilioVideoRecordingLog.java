package com.misyn.onmisite.entity;

import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.envers.Audited;

import java.time.LocalDateTime;


@Entity
@Getter
@Setter
@Table(name = "onmisite_twilio_video_recording_log",indexes = {
        @Index(name = "IND9cf3cdc86c90e607f89d39ca8c1878dc" ,columnList = "sourceSid",unique = true),
        @Index(name = "IND8cf3cdc86c90e607f89d39ca8c1878dd" ,columnList = "compositionSid"),
        @Index(name = "IND94f3cdc86c90e607f89d39ca8c1878d1" ,columnList = "remoteParticipant,runComposition,statusCallbackEvent")
})
//@Audited
public class TwilioVideoRecordingLog {
    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    private Long logId;
    @Column
    private Long callId;
    @Column
    private String sourceSid;
    @Column
    private String participantSid;
    @Column
    private String roomSid;
    @Column
    private String roomName;
    @Column
    private String roomType;
    @Column
    private Integer sequenceNumber;
    @Column
    private String statusCallbackEvent;
    @Column
    private LocalDateTime createdDate;
    @Column
    private LocalDateTime modifiedDate;
    @Column
    private Boolean remoteParticipant;
    @Column
    private Boolean runComposition;
    @Column
    private String  compositionSid;
    @Column
    private Boolean fileDownloaded;
}
