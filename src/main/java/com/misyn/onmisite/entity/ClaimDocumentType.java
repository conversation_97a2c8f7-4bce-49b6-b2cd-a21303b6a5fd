package com.misyn.onmisite.entity;

import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

@Entity
@Table(name = "claim_document_type")
@Getter
@Setter
public class ClaimDocumentType implements Serializable {

    @Id
    @Column(name = "N_DOC_TYPE_ID")
    private Integer documentTypeId;

    @Column(name = "V_DOC_TYPE_NAME")
    private String documentTypeName;
}
