package com.misyn.onmisite.entity;

import jakarta.persistence.*;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Entity
@Table(name = "claim_inspection_info_main_me")
@Data
public class ClaimInspectionInfoMainMe {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "n_inspection_id")
    private Integer inspectionId;

    @Column(name = "n_ref_no")
    private Integer refNo;

    @Column(name = "v_job_no", length = 15)
    private String jobNo;

    @Column(name = "n_claim_no")
    private Integer claimNo;

    @Column(name = "v_make_confirm", length = 2)
    private String makeConfirm;

    @Column(name = "v_model_confirm", length = 2)
    private String modelConfirm;

    @Column(name = "v_eng_no_confirm", length = 2)
    private String engNoConfirm;

    @Column(name = "v_chassis_no_confirm", length = 2)
    private String chassisNoConfirm;

    @Column(name = "v_chassis_no", length = 45)
    private String chassisNo;

    @Column(name = "n_not_checked_reason")
    private Integer notCheckedReason;

    @Column(name = "v_year_make_confirm", length = 2)
    private String yearMakeConfirm;

    @Column(name = "d_inspect_datetime")
    private LocalDateTime inspectDatetime;

    @Column(name = "n_pav", precision = 20, scale = 2)
    private BigDecimal pav;

    @Column(name = "v_damage_details", length = 255)
    private String damageDetails;

    @Column(name = "v_pad", length = 500)
    private String pad;

    @Column(name = "v_genun_of_accid", length = 1)
    private String genunOfAccid;

    @Column(name = "v_first_statement_rqed", length = 1)
    private String firstStatementRqed;

    @Column(name = "v_first_statement_req_reason", length = 5)
    private String firstStatementReqReason;

    @Column(name = "v_invest_rqed", length = 1)
    private String investRqed;

    @Column(name = "v_assessor_remark", length = 1000)
    private String assessorRemark;

    @Column(name = "n_inspection_type")
    private Integer inspectionType;

    @Column(name = "n_job_type")
    private Integer jobType;

    @Column(name = "v_assigned_location", length = 255)
    private String assignedLocation;

    @Column(name = "v_current_location", length = 255)
    private String currentLocation;

    @Column(name = "v_place_of_inspection", length = 255)
    private String placeOfInspection;

    @Column(name = "n_mileage")
    private Integer mileage;

    @Column(name = "n_cost_of_call", precision = 20, scale = 2)
    private BigDecimal costOfCall;

    @Column(name = "n_other_fee", precision = 20, scale = 2)
    private BigDecimal otherFee;

    @Column(name = "n_deductions", precision = 20, scale = 2)
    private BigDecimal deductions;

    @Column(name = "v_reason_of_deduction", length = 255)
    private String reasonOfDeduction;

    @Column(name = "n_total_assessor_fee", precision = 20, scale = 2)
    private BigDecimal totalAssessorFee;

    @Column(name = "v_fee_desc", length = 255)
    private String feeDesc;

    @Column(name = "n_record_status")
    private Integer recordStatus;

    @Column(name = "v_inpuser", length = 45)
    private String inpUser;

    @Column(name = "d_inpdatetime")
    private LocalDateTime inpDatetime;

    @Column(name = "v_ass_fee_apr_status", length = 5)
    private String assFeeAprStatus;

    @Column(name = "v_ass_fee_apr_user", length = 45)
    private String assFeeAprUser;

    @Column(name = "v_ass_fee_apr_datetime")
    private LocalDateTime assFeeAprDatetime;

    @Column(name = "v_ass_esti_apr_status", length = 5)
    private String assEstiAprStatus;

    @Column(name = "v_ass_esti_apr_user", length = 45)
    private String assEstiAprUser;

    @Column(name = "v_ass_esti_apr_datetime")
    private LocalDateTime assEstiAprDatetime;

    // Getters and Setters omitted for brevity
}
