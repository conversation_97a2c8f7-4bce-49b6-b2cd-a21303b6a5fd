package com.misyn.onmisite.entity;

import jakarta.persistence.*;
import lombok.Data;

@Entity
@Data
@IdClass(ClaimTireConditionMeId.class)
@Table(name = "claim_tire_condition_me")
public class ClaimTireConditionMe {

    @Id
    @Column(name = "N_REF_NO")
    private Integer refNo;

    @Id
    @Column(name = "N_CLIM_NO", nullable = false)
    private Integer claimNo;

    @Id
    @Column(name = "N_POSITION", nullable = false)
    private Integer position;

    @Column(name = "V_RF")
    private String rightFront;

    @Column(name = "V_LF")
    private String leftFront;

    @Column(name = "V_RR")
    private String rightRear;

    @Column(name = "V_RL")
    private String leftRear;

    @Column(name = "V_RRI")
    private String rightRearInner;

    @Column(name = "V_LRI")
    private String leftRearInner;

    @Column(name = "V_OTHER")
    private String other;

}
