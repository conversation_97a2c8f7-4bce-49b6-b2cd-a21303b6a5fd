package com.misyn.onmisite.entity;

import jakarta.persistence.*;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

@Entity
@Data
@Table(name = "claim_assign_claim_handler")
public class ClaimAssignClaimHandler {

        @Id
        @Column(name = "N_TXN_NO", length = 15)
        private Integer txnNo;

        @Column(name = "N_CLAIM_NO", length = 20)
        private Integer claimNo;

        @Column(name = "N_TEAM_ID", length = 9)
        private Integer teamId;

        @Column(name = "V_ASSIGN_USER_ID", length = 45)
        private String assignUserId;

        @Column(name = "V_ASSIGN_STATUS" , insertable = false, updatable = false)
        private String assignStatus;

        @Column(name = "D_ASSIGN_DATE_TIME")
        private LocalDateTime assignDateTime;

        @Column(name = "V_INIT_LIABILITY_ASSIGN_USER_ID", length = 45)
        private String initLiabilityAssignUserId;

        @Column(name = "V_INIT_LIABILITY_ASSIGN_DATE_TIME")
        private LocalDateTime initLiabilityAssignDateTime;

        @Column(name = "V_INIT_LIABILITY_APRV_USER_ID", length = 45)
        private String initLiabilityAprvUserId;

        @Column(name = "V_INIT_LIABILITY_APRV_STATUS" , insertable = false, updatable = false)
        private String initLiabilityAprvStatus;

        @Column(name = "D_INIT_LIABILITY_APRV_DATE_TIME")
        private LocalDateTime initLiabilityAprvDateTime;

        @Column(name = "V_LIABILITY_APRV_ASSIGN_USER", length = 45)
        private String liabilityAprvAssignUser;

        @Column(name = "D_LIABILITY_APRV_ASSIGN_DATE_TIME")
        private LocalDate liabilityAprvAssignDateTime;

        @Column(name = "V_LIABILITY_APRV_USER", length = 45)
        private String liabilityAprvUser;

        @Column(name = "V_LIABILITY_APRV_STATUS" , insertable = false, updatable = false)
        private String liabilityAprvStatus;

        @Column(name = "D_LIABILITY_APRV_DATE_TIME")
        private LocalDateTime liabilityAprvDateTime;

        @Column(name = "V_MANAGER_USER_ID", length = 45)
        private String managerUserId;

        @Column(name = "V_CHANNEL_CODE", length = 20)
        private String channelCode;

        @Column(name = "N_ACCESS_USER_TYPE", length = 2)
        private Integer accessUserType;

        @Column(name = "N_CLAIM_STATUS", length = 11)
        private Integer claimStatus;

        @Column(name = "N_RESERVE_AMOUNT", precision = 20)
        private BigDecimal reserveAmount;

        @Column(name = "N_RESERVE_AMOUNT_AFTER_APRV", precision = 20)
        private BigDecimal reserveAmountAfterAprv;

        @Column(name = "n_aprv_tot_acr_amount", precision = 38)
        private BigDecimal aprvTotAcrAmount;

        @Column(name = "V_ACR_APRV_USER", length = 50)
        private String acrAprvUser;

        @Column(name = "N_ENGINEER_APRV_AMOUNT", precision = 20)
        private BigDecimal engineerAprvAmount;

        @Column(name = "N_LABOUR_COST", precision = 20)
        private BigDecimal labourCost;

        @Column(name = "N_PART_COST", precision = 20)
        private BigDecimal partCost;

        @Column(name = "N_LOSS_TYPE", length = 11)
        private Integer lossType;

        @Column(name = "V_IS_ALL_DOC_UPLOAD" , insertable = false, updatable = false)
        private String isAllDocUpload;

        @Column(name = "V_IS_CHECK_ALL_MND_DOCS" , insertable = false, updatable = false)
        private String isCheckAllMndDocs;

        @Column(name = "N_PENALTY_BALD_TYRE", precision = 20)
        private BigDecimal penaltyBaldTyre;

        @Column(name = "N_PENALTY_BALD_TYRE_RATE", precision = 20)
        private BigDecimal penaltyBaldTyreRate;

        @Column(name = "V_PENALTY_BALD_TYRE_USER", length = 45)
        private String penaltyBaldTyreUser;

        @Column(name = "D_PENALTY_BALD_TYRE_DATE_TIME")
        private LocalDateTime penaltyBaldTyreDateTime;

        @Column(name = "N_PENALTY_UNDER_INSURCE", precision = 20)
        private BigDecimal penaltyUnderInsurance;

        @Column(name = "N_PENALTY_UNDER_INSURCE_RATE", precision = 20)
        private BigDecimal penaltyUnderInsuranceRate;

        @Column(name = "V_PENALTY_UNDER_INSURCE_USER", length = 45)
        private String penaltyUnderInsuranceUser;

        @Column(name = "D_PENALTY_UNDER_INSURCE_DATE_TIME")
        private LocalDateTime penaltyUnderInsuranceDateTime;

        @Column(name = "v_intimation_chk", length = 255)
        private String intimationChk;

        @Column(name = "v_intimation_chk_user", length = 255)
        private String intimationChkUser;

        @Column(name = "D_INTIMATION_CHK_DATE_TIME")
        private LocalDateTime intimationChkDateTime;

        @Column(name = "v_is_lc_chk1", length = 255)
        private String isLcChk1;

        @Column(name = "V_IS_LC_CHK2", length = 1)
        private String isLcChk2;

        @Column(name = "V_IS_LC_CHK3", length = 1)
        private String isLcChk3;

        @Column(name = "V_IS_LC_CHK4", length = 1)
        private String isLcChk4;

        @Column(name = "V_IS_LC_CHK5", length = 1)
        private String isLcChk5;

        @Column(name = "V_IS_LC_CHK6", length = 1)
        private String isLcChk6;

        @Column(name = "V_IS_LC_CHK7", length = 1)
        private String isLcChk7;

        @Column(name = "v_is_lc_chk8", columnDefinition = "TEXT")
        private String isLcChk8;

        @Column(name = "V_LC_CHK_USER", length = 20)
        private String lcChkUser;

        @Column(name = "D_LC_CHK_DATE_TIME")
        private LocalDateTime lcChkDateTime;

    @Column(name = "v_claim_panel_assign_users", length = 255)
    private String claimPanelAssignUsers;

    @Column(name = "D_CLAIM_PANEL_ASSIGN_USER_DATE_TIME")
    private LocalDateTime claimPanelAssignUserDateTime;

    @Column(name = "N_DECISION_APPRV_CLAIM_PANEL", length = 10)
    private Integer decisionApprvClaimPanel;

    @Column(name = "v_claim_panel_decision", length = 255)
    private String claimPanelDecision;

    @Column(name = "D_CLAIM_PANEL_DECISION_DATE_TIME")
    private LocalDateTime claimPanelDecisionDateTime;

    @Column(name = "N_REPUDIATED_TYPE", length = 10)
    private Integer repudiatedType;

    @Column(name = "V_IS_PRINT_REPUDIATED_LETTER" , insertable = false, updatable = false)
    private String isPrintRepudiatedLetter;

    @Column(name = "v_repudiated_letter_print_user_id", columnDefinition = "TEXT")
    private String repudiatedLetterPrintUserId;

    @Column(name = "V_REPUDIATED_LETTER_PRINT_DATE_TIME")
    private LocalDate repudiatedLetterPrintDateTime;

    @Column(name = "v_financial_interest", length = 255)
    private String financialInterest;

    @Column(name = "N_LEASING_REF_NO", length = 11)
    private Integer leasingRefNo;

    @Column(name = "v_finalize_user_id", length = 255)
    private String finalizeUserId;

    @Column(name = "D_FINALIZE_DATE_TIME")
    private LocalDateTime finalizeDateTime;

    @Column(name = "v_is_genarate_supply_order", length = 255)
    private String isGenerateSupplyOrder;

    @Column(name = "V_SUPPLY_ORDER_ASSIGN_STATUS" , insertable = false, updatable = false)
    private String supplyOrderAssignStatus;

    @Column(name = "v_supply_order_assign_user", columnDefinition = "TEXT")
    private String supplyOrderAssignUser;

    @Column(name = "D_SUPPLY_ORDER_ASSIGN_DATE_TIME")
    private LocalDateTime supplyOrderAssignDateTime;

    @Column(name = "v_supply_order_create_user", columnDefinition = "TEXT")
    private String supplyOrderCreateUser;

    @Column(name = "D_SUPPLY_ORDER_CREATE_DATE_TIME")
    private LocalDateTime supplyOrderCreateDateTime;

    @Column(name = "V_SUPPLY_ORDER_CREATE_CLOSE", length = 1)
    private String supplyOrderCreateClose;

    @Column(name = "v_is_file_store", length = 255)
    private String isFileStore;

    @Column(name = "v_file_user_store_user_id", length = 255)
    private String fileUserStoreUserId;

    @Column(name = "D_FILE_STORE_DATE_TIME")
    private LocalDateTime fileStoreDateTime;

    @Column(name = "v_reopen_assign_user_id", columnDefinition = "TEXT")
    private String reopenAssignUserId;

    @Column(name = "D_REOPEN_ASSIGN_USER_DATE_TIME")
    private LocalDateTime reopenAssignUserDateTime;

    @Column(name = "v_reopen_user_id", columnDefinition = "TEXT")
    private String reopenUserId;

    @Column(name = "D_REOPEN_DATE_TIME")
    private LocalDateTime reopenDateTime;

    @Column(name = "N_REOPEN_NO_OF_TIME", length = 3)
    private Integer reopenNoOfTime;

    @Column(name = "v_is_gen_final_remind_letter", length = 255)
    private String isGenFinalRemindLetter;

    @Column(name = "D_GEN_FINAL_REMIND_LETTER_DATE_TIME")
    private LocalDateTime genFinalRemindLetterDateTime;

    @Column(name = "v_decision_making_assign_user_id", length = 255)
    private String decisionMakingAssignUserId;

    @Column(name = "D_DECISION_MAKING_ASSIGN_DATE_TIME")
    private LocalDateTime decisionMakingAssignDateTime;

    @Column(name = "v_investigation_status", length = 255)
    private String investigationStatus;

    @Column(name = "v_investigation_assign_user_id", length = 255)
    private String investigationAssignUserId;

    @Column(name = "D_INVESTIGATION_ASSIGN_DATE_TIME")
    private LocalDateTime investigationAssignDateTime;

    @Column(name = "v_investigation_arrange_user_id", length = 255)
    private String investigationArrangeUserId;

    @Column(name = "D_INVESTIGATION_ARRANGE_DATE_TIME")
    private LocalDateTime investigationArrangeDateTime;

    @Column(name = "v_investigation_completed_user_id", length = 255)
    private String investigationCompletedUserId;

    @Column(name = "D_INVESTIGATION_COMPLETED_DATE_TIME")
    private LocalDateTime investigationCompletedDateTime;

    @Column(name = "v_special_approval_input_user_id", columnDefinition = "TEXT")
    private String specialApprovalInputUserId;

    @Column(name = "V_SPECIAL_APPROVAL_INPUT_DATE_TIME")
    private LocalDateTime specialApprovalInputDateTime;

    @Column(name = "v_special_approval_user_id", columnDefinition = "TEXT")
    private String specialApprovalUserId;

    @Column(name = "V_SPECIAL_APPROVAL_DATE_TIME")
    private LocalDateTime specialApprovalDateTime;

    @Column(name = "N_OLD_CLAIM_STATUS", length = 11)
    private Integer oldClaimStatus;

    @Column(name = "N_OLD_ENGINEER_CLAIM_STATUS", columnDefinition = "TEXT")
    private String oldEngineerClaimStatus;

    @Column(name = "v_is_doubt", length = 255)
    private String isDoubt;

    @Column(name = "V_IS_ON_SITE_OFFER" , insertable = false, updatable = false)
    private String isOnSiteOffer;

    @Column(name = "n_aprv_advance_amount", precision = 38)
    private BigDecimal aprvAdvanceAmount;

    @Column(name = "V_REOPEN_TYPE" , insertable = false, updatable = false)
    private String reopenType;

    @Column(name = "v_close_status", length = 255)
    private String closeStatus;

    @Column(name = "v_close_user", length = 255)
    private String closeUser;

    @Column(name = "D_CLOSE_DATE_TIME")
    private LocalDateTime closeDateTime;

    @Column(name = "N_REPUDIATED_LETTER_TYPE", length = 3)
    private Integer repudiatedLetterType;

    @Column(name = "v_inp_status", length = 255)
    private String inpStatus;

    @Column(name = "v_inp_user_id", length = 255)
    private String inpUserId;

    @Column(name = "D_INP_DATE_TIME")
    private LocalDateTime inpDateTime;

    @Column(name = "N_VERSION_NO", length = 10)
    private Integer versionNo;

    @Column(name = "v_is_excess_include", length = 255)
    private String isExcessInclude;

    @Column(name = "V_IS_PROVIDE_OFFER" , insertable = false, updatable = false)
    private String isProvideOffer;

    @Column(name = "v_letter_panel_user_id", columnDefinition = "TEXT")
    private String letterPanelUserId;

    @Column(name = "D_LETTER_PANEL_DATETIME")
    private LocalDateTime letterPanelDatetime;

    @Column(name = "V_IS_REJECTION_ATTACHED" , insertable = false, updatable = false)
    private String isRejectionAttached;

    @Column(name = "D_REJECTION_ATTACHED_DATETIME")
    private LocalDateTime rejectionAttachedDatetime;

    @Column(name = "v_advance_approval_assign_user", length = 255)
    private String advanceApprovalAssignUser;

    @Column(name = "D_ADVANCE_APPROVAL_ASSIGN_DATE_TIME")
    private LocalDateTime advanceApprovalAssignDatetime;

    @Column(name = "v_advance_status", length = 255)
    private String advanceStatus;

    @Column(name = "v_advance_forwarded_user", length = 255)
    private String advanceForwardedUser;

    @Column(name = "n_approval_pending_advance_amount", precision = 38)
    private BigDecimal approvalPendingAdvanceAmount;

    @Column(name = "v_advance_approved_user", length = 255)
    private String advanceApprovedUser;

    @Column(name = "D_ADVANCE_APPROVED_DATE_TIME")
    private LocalDateTime advanceApprovedDateTime;

    @Column(name = "v_forwarded_engineer", length = 255)
    private String forwardedEngineer;

}    
