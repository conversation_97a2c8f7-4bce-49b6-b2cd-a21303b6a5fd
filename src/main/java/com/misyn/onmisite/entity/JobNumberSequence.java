package com.misyn.onmisite.entity;

import jakarta.persistence.*;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.envers.Audited;

import java.io.Serializable;

@Table(name = "onmisite_job_number_sequence")
@Entity
@Getter
@Setter
//@Audited
public class JobNumberSequence implements Serializable {
    @EqualsAndHashCode.Include
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = EntityFieldName.COMPANY_ID)
    private Company company;
    @Column
    private Long nextJobNumber;
}
