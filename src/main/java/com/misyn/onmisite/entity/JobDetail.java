package com.misyn.onmisite.entity;

import com.misyn.onmisite.enums.JobStatus;
import com.misyn.onmisite.enums.RecordStatus;
import jakarta.persistence.*;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.envers.Audited;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import java.io.Serializable;
import java.time.LocalDateTime;

@Table(name = "onmisite_job_detail")
@Entity
@Getter
@Setter
//@Audited
@EntityListeners(AuditingEntityListener.class)
public class JobDetail implements Serializable {
    @EqualsAndHashCode.Include
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long jobId;
    @Column(unique = true)
    private String jobNumber;

    @Column
    private String policyNo;
    @Column
    private String vehicleNo;
    @Column
    private String vehicleMake;
    @Column
    private String vehicleModel;

    @Column
    private String insuredName;
    @Column
    private String insuredMobileNo;
    @Column
    private String insuredEmail;
    @Column
    private String idNo;
    @Column
    private String insuredAddress1;
    @Column
    private String insuredAddress2;
    @Column
    private String insuredAddress3;
    @Column
    @Enumerated(EnumType.STRING)
    private JobStatus jobStatus;

    @Column
    private String jobDescription;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = EntityFieldName.COMPANY_ID)
    private Company company;

    @Column(nullable = false, updatable = false)
    @CreatedBy
    private String createdBy = "";
    @LastModifiedBy
    private String modifiedBy;
    @Column(nullable = false)
    @Enumerated(EnumType.STRING)
    private RecordStatus recordStatus = RecordStatus.ACTIVE;
    @Column(nullable = false, updatable = false)
    @CreatedDate
    private LocalDateTime createdDate;
    @LastModifiedDate
    private LocalDateTime modifiedDate;
}
