package com.misyn.onmisite.entity;

import jakarta.persistence.*;
import lombok.Data;
import java.time.LocalDateTime;

@Data
@Entity
@Table(name = "claim_special_remark")
public class ClaimSpecialRemark {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "txn_id")
    private Integer txnId;

    @Column(name = "claim_no")
    private Integer claimNo;

    @Column(name = "department_id")
    private Integer departmentId;

    @Column(name = "section_name", length = 300)
    private String sectionName;

    @Column(name = "remark", length = 3000)
    private String remark;

    @Column(name = "input_user_id", length = 45)
    private String inputUserId;

    @Column(name = "input_date_time")
    private LocalDateTime inputDateTime;

}
