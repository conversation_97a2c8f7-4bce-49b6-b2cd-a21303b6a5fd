package com.misyn.onmisite.entity;

import com.misyn.onmisite.enums.RecordStatus;
import jakarta.persistence.*;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.envers.Audited;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import java.io.Serializable;
import java.time.LocalDateTime;

@Table(name = "onmisite_call_details")
@Entity
@Getter
@Setter
//@Audited
@EntityListeners(AuditingEntityListener.class)
public class CallDetails implements Serializable {
    @EqualsAndHashCode.Include
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long callId;
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = EntityFieldName.JOB_ID)
    private JobDetail jobDetail;
    @Column
    private LocalDateTime callStartDateTime;
    @Column
    private LocalDateTime callEndDateTime;

    @Column
    private String latitude;
    @Column
    private String longitude;
    @Column
    private String accessToken;
    @Column
    private LocalDateTime tokenGenerateDateTime;
    @Column
    private String callRemark;
    @Column
    private String link;
    @Column
    private String assignee;
    @Column
    private String callingMobileNo;
    @Column
    private Integer inspectionTypeId;
    @Column
    private String inspectionJobNumber;

    @Column
    private String roomName;

    @Column
    private String roomSid;

    @Column
    private String localParticipantSid;

    @Column
    private String remoteParticipantSid;

    @Column(nullable = false, updatable = false)
    @CreatedBy
    private String createdBy = "";
    @LastModifiedBy
    private String modifiedBy;
    @Column(nullable = false)
    @Enumerated(EnumType.STRING)
    private RecordStatus recordStatus = RecordStatus.ACTIVE;
    @Column(nullable = false, updatable = false)
    @CreatedDate
    private LocalDateTime createdDate;
    @LastModifiedDate
    private LocalDateTime modifiedDate;
}
