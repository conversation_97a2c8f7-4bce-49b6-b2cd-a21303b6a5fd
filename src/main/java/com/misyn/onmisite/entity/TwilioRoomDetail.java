package com.misyn.onmisite.entity;

import com.misyn.onmisite.enums.RecordStatus;
import jakarta.persistence.*;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.envers.Audited;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;

@Table(name = "onmisite_twilio_room_detail")
@Entity
@Getter
@Setter
//@Audited
@EntityListeners(AuditingEntityListener.class)
public class TwilioRoomDetail implements Serializable {
    @EqualsAndHashCode.Include
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long twilioRoomId;
    @Column
    private Long callId;
    @Column
    private String roomType;
    @Column
    private String roomSid;
    @Column
    private String roomName;
    @Column
    private String roomStatus;
    @Column
    private String participantIdentity1;
    @Column
    private String participantIdentity2;
    @Column
    private String participantSid1;
    @Column
    private String participantSid2;
    @Column
    private String participantIdentityStatus1;
    @Column
    private String participantIdentityStatus2;
    @Column
    private String statusCallbackEvent;
    @Column
    @Temporal(TemporalType.TIMESTAMP)
    private Date updatedDateTime;
    @Column
    private boolean isComposition;
    @Column
    private String recordCompositionCallbackEvent;
    @Column
    private String compositionSid;

    @Column(nullable = false, updatable = false)
    @CreatedBy
    private String createdBy = "";
    @LastModifiedBy
    private String modifiedBy;
    @Column(nullable = false)
    @Enumerated(EnumType.STRING)
    private RecordStatus recordStatus = RecordStatus.ACTIVE;
    @Column(nullable = false, updatable = false)
    @CreatedDate
    private LocalDateTime createdDate;
    @LastModifiedDate
    private LocalDateTime modifiedDate;
}
