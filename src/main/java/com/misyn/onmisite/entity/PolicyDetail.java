package com.misyn.onmisite.entity;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

@Entity
@Table(name = "claim_vehicle_info_main")
@Data
public class PolicyDetail {

    @Id
    @Column(name = "N_POL_REF_NO")
    private Integer policyRefNo;

    @Column(name = "V_POL_BRANCH")
    private String policyBranch;

    @Column(name = "V_POL_TYPE")
    private String policyType;

    @Column(name = "V_POL_NUMBER")
    private String policyNumber;

    @Column(name = "N_REN_COUNT")
    private Integer renewalCount;

    @Column(name = "N_END_COUNT")
    private Integer endorsementCount;

    @Column(name = "V_VEHICLE_NUMBER")
    private String vehicleNumber;

    @Column(name = "D_EXPIRE_DATE")
    private LocalDate expireDate;

    @Column(name = "D_INSPEC_DATE")
    private LocalDate inspectionDate;

    @Column(name = "D_ORG_INSPEC_DATE")
    private LocalDate originalInspectionDate;

    @Column(name = "V_POL_STATUS")
    private String policyStatus;

    @Column(name = "V_CUST_NAME")
    private String customerName;

    @Column(name = "V_CUST_NIC")
    private String customerNic;

    @Column(name = "V_CUST_ADDRESS_LINE1")
    private String customerAddressLine1;

    @Column(name = "V_CUST_ADDRESS_LINE2")
    private String customerAddressLine2;

    @Column(name = "V_CUST_ADDRESS_LINE3")
    private String customerAddressLine3;

    @Column(name = "V_CUST_MOBILE_NO")
    private String customerMobileNo;

    @Column(name = "V_CUST_LAND_NO")
    private String customerLandNo;

    @Column(name = "N_ANNUAL_PREMIUM")
    private BigDecimal annualPremium;

    @Column(name = "N_SUM_INSURED")
    private BigDecimal sumInsured;

    @Column(name = "D_LATEST_CLM_INTIM_DATE")
    private LocalDate latestClaimIntimationDate;

    @Column(name = "D_LATEST_CLM_LOSS_DATE")
    private LocalDate latestClaimLossDate;

    @Column(name = "V_AGENT_BROKER")
    private String agentBroker;

    @Column(name = "N_TOT_PREM_OUTSTAND")
    private BigDecimal totalPremiumOutstanding;

    @Column(name = "N_NO_DAY_PREM_OUTSTAND")
    private Integer noOfDaysPremiumOutstanding;

    @Column(name = "V_ENGINE_NO")
    private String engineNo;

    @Column(name = "V_CHASSIS_NO")
    private String chassisNo;

    @Column(name = "V_VEHICLE_MAKE")
    private String vehicleMake;

    @Column(name = "V_VEHICLE_MODEL")
    private String vehicleModel;

    @Column(name = "N_MANUFACT_YEAR")
    private Integer manufactureYear;

    @Column(name = "N_EXCESS")
    private BigDecimal excess;

    @Column(name = "N_NCB_RATE")
    private Integer ncbRate;

    @Column(name = "N_NCB_AMOUNT")
    private BigDecimal ncbAmount;

    @Column(name = "V_COVER_NOTE_NO")
    private String coverNoteNo;

    @Column(name = "V_COVER_TYPE")
    private String coverType;

    @Column(name = "V_CHANNEL")
    private String channel;

    @Column(name = "V_UPDATE_FLAG")
    private String updateFlag;

    @Column(name = "V_INSERT_FLAG")
    private String insertFlag;

    @Column(name = "V_CREATE_USER")
    private String createUser;

    @Column(name = "D_CREATE_DATE")
    private LocalDate createDate;

    @Column(name = "T_CREATE_TIME")
    private String createTime;

    @Column(name = "V_CANCEL_REASON")
    private String cancelReason;

    @Column(name = "D_LAPSED_DATE")
    private String lapsedDate;

    @Column(name = "D_REGIST_DATE")
    private LocalDate registerDate;

    @Column(name = "D_POL_CANCEL_DATE")
    private String policyCancelDate;

    @Column(name = "V_LOCATION")
    private String location;

    @Column(name = "V_RISK")
    private String risk;

    @Column(name = "V_BODY_TYPE")
    private String bodyType;

    @Column(name = "V_CLIENT_ID")
    private String clientId;

    @Column(name = "V_IS_THIRD_PARTY")
    private String isThirdParty;

    @Column(name = "V_FINANCE_COMPANY")
    private String financeCompany;

    @Column(name = "V_VEHICLE_USAGE")
    private String vehicleUsage;

    @Column(name = "V_AGENT_CODE")
    private String agentCode;

    @Column(name = "V_FUEL_TYPE")
    private String fuelType;

    @Column(name = "N_NO_OF_SEAT")
    private Integer noOfSeat;

    @Column(name = "N_VEHICLE_AGE")
    private Integer vehicleAge;

    @Column(name = "V_POL_SUSPEND")
    private String policySuspend;

    @Column(name = "V_ENG_CAPACITY")
    private String engineCapacity;

    @Column(name = "V_BRANCH_CODE")
    private String branchCode;

    @Column(name = "V_PRODUCT")
    private String product;

    @Column(name = "V_LAST_MODIFY_USER")
    private String lastModifyUser;

    @Column(name = "D_LAST_MODIFY_DATE_TIME")
    private String lastModifyDateTime;

    @Column(name = "V_CURRENT_POL_STATUS")
    private String currentPolicyStatus;

    @Column(name = "V_VEHICLE_COLOR")
    private String vehicleColor;

    @Column(name = "V_TRADE_PLATE_NO")
    private String tradePlateNo;

    @Column(name = "D_LAST_MODIFY_DATE")
    private LocalDate lastModifyDate;

    @Column(name = "V_IND_COM_FLAG")
    private String indComFlag;

    @Column(name = "V_COMPANY_BRANCH")
    private String companyBranch;

    @Column(name = "V_COMPANY_CODE")
    private String companyCode;

    @Column(name = "D_CMS_UPDATE_DATE_TIME")
    private LocalDateTime cmsUpdateDateTime;

    @Column(name = "V_FIN_COMPANY_CODE")
    private String finCompanyCode;

    @Column(name = "V_FIN_COMPANY_BRANCH")
    private String finCompanyBranch;

    @Column(name = "V_LOAN_ACC_NO")
    private String loanAccountNo;

    @Column(name = "V_IDEN_CODE")
    private String idenCode;

    @Column(name = "V_VEHICLE_NO_LAST_DIGIT")
    private String vehicleNoLastDigit;

    @Column(name = "V_POL_NUMBER_LAST_DIGIT")
    private String policyNumberLastDigit;

    @Column(name = "V_POLICY_CHANNEL_TYPE")
    private String policyChannelType;

    @Column(name = "V_BIZ_TYPE")
    private String businessType;

    @Column(name = "V_INTRODUCER")
    private String introducer;

    @Column(name = "V_BANK_REF_NO")
    private String bankRefNo;

    @Column(name = "V_WORKFLOW")
    private String workflow;

    @Column(name = "V_CATEGORY_DESC")
    private String categoryDesc;

}
