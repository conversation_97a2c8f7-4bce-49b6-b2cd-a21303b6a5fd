package com.misyn.onmisite.entity;

import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

@Table(name = "onmisite_document_type_detail")
@Entity
@Getter
@Setter
@EntityListeners(AuditingEntityListener.class)
public class DocumentTypeDetail {
    @Id
    private Integer documentTypeId;
    @Column
    private String documentTypeName;
    private Integer orderId;
}
