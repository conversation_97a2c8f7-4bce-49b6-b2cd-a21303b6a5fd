package com.misyn.onmisite.entity;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Data;

@Entity
@Table(name = "claim_claim_info_main")
@Data
public class ClaimDetail {

    @Id
    @Column(name = "N_REF_NO")
    private Integer refNo;

    @Column(name = "N_CLIM_NO")
    private Integer claimNo;

    @Column(name = "V_INTIMATION_NO")
    private String intimationNo;

    @Column(name = "V_ISF_CLAIM_NO")
    private String isfClaimNo;

    @Column(name = "V_PREFERRED_LANGUAGE")
    private String preferredLanguage;

    @Column(name = "V_POL_BRANCH")
    private String policyBranch;

    @Column(name = "V_POL_TYPE")
    private String policyType;

    @Column(name = "V_POL_NUMBER")
    private String policyNumber;

    @Column(name = "V_VEHICLE_NO")
    private String vehicleNo;

    @Column(name = "N_RELESHIP_INSURD")
    private Integer releshipInsurd;

    @Column(name = "N_REPORTER_TITLE")
    private Integer reporterTitle;

    @Column(name = "V_REPORTER_NAME")
    private String reporterName;

    @Column(name = "V_REPORTER_ID")
    private String reporterId;

    @Column(name = "V_CLI_NO")
    private String cliNo;

    @Column(name = "V_OTHER_CONT_NO")
    private String otherContNo;

    @Column(name = "V_INSURD_MOB_NO")
    private String insurdMobNo;

    @Column(name = "V_IS_SAME_REPORT_DRIVER")
    private String isSameReportDriver;

    @Column(name = "N_DRIVER_STATUS")
    private Integer driverStatus;

    @Column(name = "N_DRIVER_TITLE")
    private Integer driverTitle;

    @Column(name = "V_DRIVER_NAME")
    private String driverName;

    @Column(name = "N_DRIVER_RELESHIP_INSURD")
    private Integer driverReleshipInsurd;

    @Column(name = "V_DRIVER_NIC")
    private String driverNic;

    @Column(name = "V_DL_NO")
    private String dlNo;

    @Column(name = "V_DL_TYPE")
    private String driverLicenceType;

    @Column(name = "V_REPORTER_REMARK")
    private String reporterRemark;

    @Column(name = "N_INTIMATION_TYPE")
    private Integer intimationType;

    @Column(name = "N_LATE_INTIMATE_REASON")
    private Integer lateIntimateReason;

    @Column(name = "D_ACCID_DATE")
    private String accidDate;

    @Column(name = "T_ACCID_TIME")
    private String accidTime;

    @Column(name = "D_DATE_OF_REPORT")
    private String dateOfReport;

    @Column(name = "T_TIME_OF_REPORT")
    private String timeOfReport;

    @Column(name = "V_IS_CAT_EVENT")
    private String isCatEvent;

    @Column(name = "N_CAT_EVENT_CODE")
    private Integer catEventCode;

    @Column(name = "V_CAT_EVENT")
    private String catEvent;

    @Column(name = "N_CAUSE_OF_LOSS")
    private Integer causeOfLoss;

    @Column(name = "V_ACCID_DESC")
    private String accidDesc;

    @Column(name = "V_PLACE_OF_ACCID")
    private String placeOfAccid;

    @Column(name = "V_DISTRICT_CODE", insertable = false, updatable = false)
    private String districtCode;

    @Column(name = "N_NEAREST_CITY")
    private Integer nearestCity;

    @Column(name = "V_CURRENT_LOCATION")
    private String currentLocation;

    @Column(name = "N_NEAR_POLICE_STATION")
    private Integer nearPoliceStation;

    @Column(name = "V_IS_FIRST_STATEMENT_REQ")
    private String isFirstStatementReq;

    @Column(name = "N_FIRST_STATEMENT_REQ_REASON")
    private Integer firstStatementReqReason;

    @Column(name = "V_FIRST_STATEMENT_REMARK")
    private String firstStatementRemark;

    @Column(name = "V_INSPECTION_TYPE")
    private String inspectionType;

    @Column(name = "N_INSPECTION_TYPE_REASON")
    private Integer inspectionTypeReason;

    @Column(name = "N_DRAFT_REASON")
    private Integer draftReason;

    @Column(name = "V_FOLLOW_CALL_USER_ID")
    private String followCallUserId;

    @Column(name = "D_FOLLOW_CALL_DONE_DATE_TIME")
    private String followCallDoneDateTime;

    @Column(name = "N_FOLLOW_CALL_CONTACT_PERSON_TITLE")
    private Integer followCallContactPersonTitle;

    @Column(name = "V_FOLLOW_CALL_CONTACT_PERSON_NAME")
    private String followCallContactPersonName;

    @Column(name = "V_FOLLOW_CALL_CONTACT_NUMBER")
    private String followCallContactNumber;

    @Column(name = "N_FOLLOW_CALL_AGNET_SERVICE_RATE")
    private Integer followCallAgentServiceRate;

    @Column(name = "V_FOLLOW_CALL_ASSESSOR_SERVICE_RATE", insertable = false, updatable = false)
    private String followCallAssessorServiceRate;

    @Column(name = "V_NCB_PROM")
    private String ncbProm;

    @Column(name = "N_NCB_REASON")
    private Integer ncbReason;

    @Column(name = "V_NCB_REMARK")
    private String ncbRemark;

    @Column(name = "V_LOMO_PROM")
    private String lomoProm;

    @Column(name = "N_LOMO_REASON")
    private Integer lomoReason;

    @Column(name = "V_CALL_USER")
    private String callUser;

    @Column(name = "N_CLAIM_STATUS")
    private Integer claimStatus;

    @Column(name = "V_VEHICLE_COLOR")
    private String vehicleColor;

    @Column(name = "V_COVER_NOTE_NO")
    private String coverNoteNo;

    @Column(name = "N_POL_REF_NO")
    private Integer polRefNo;

    @Column(name = "V_ISFS_UPDATE_STATUS")
    private String isfsUpdateStatus;

    @Column(name = "V_POL_CHK_STATUS")
    private String polChkStatus;

    @Column(name = "V_IS_NO_DAMAGE")
    private String isNoDamage;

    @Column(name = "V_DAMAGE_REMARK")
    private String damageRemark;

    @Column(name = "V_IS_HUGE_DAMAGE")
    private String isHugeDamage;

    @Column(name = "V_HUGE_REMARK")
    private String hugeRemark;

    @Column(name = "V_DAMAGE_NOT_GIVEN")
    private String damageNotGiven;

    @Column(name = "V_PRINT_LETTER")
    private String printLetter;

    @Column(name = "V_NEG_PREM_OUT")
    private String negPremOut;

    @Column(name = "V_LAND_MARK")
    private String landMark;

    @Column(name = "N_VEH_CLS_ID")
    private Integer vehClsId;

    @Column(name = "V_IS_DOUBT")
    private String isDoubt;

    @Column(name = "V_IS_DOUBT_REMARK")
    private String isDoubtRemark;

    @Column(name = "V_IS_FUTHER_DAMAGE")
    private String isFutherDamage;

    @Column(name = "V_DRAFT_REMARK")
    private String draftRemark;

    @Column(name = "N_ACCESSUSR_TYPE")
    private Integer accessusrType;

    @Column(name = "V_REC_STATUS")
    private String recStatus;

    @Column(name = "V_INPUSER")
    private String inpUser;

    @Column(name = "D_INPTIME")
    private String inpTime;

    @Column(name = "V_IS_FOLLOWUP_CALL_DONE")
    private String isFollowupCallDone;

    @Column(name = "V_DRIVER_DETAIL_NOT_RELEVANT")
    private String driverDetailNotRelevant;

    @Column(name = "V_DRIVER_DETAIL_SUBMIT")
    private String driverDetailSubmit;

    @Column(name = "V_POLICY_CHANNEL_TYPE")
    private String policyChannelType;

    @Column(name = "V_IS_THEFT_AND_FOUND")
    private String isTheftAndFound;
}
