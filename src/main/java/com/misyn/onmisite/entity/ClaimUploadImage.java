package com.misyn.onmisite.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

@Entity
@Table(name = "claim_upload_images")
@Getter
@Setter
public class ClaimUploadImage implements Serializable {

    @Id
    @Column(name = "N_REF_NO")
    private Integer refNo;

    @Column(name = "N_CLIM_NO")
    private Integer claimNo;

    @Column(name = "V_DOC_NAME")
    private String documentName;

    @Column(name = "V_DOC_PATH")
    private String documentPath;

    @Column(name = "N_DOC_TYPE_ID")
    private Integer documentTypeId;
}
