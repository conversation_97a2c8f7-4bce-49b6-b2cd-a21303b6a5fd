package com.misyn.onmisite.entity;


import com.misyn.onmisite.enums.RecordStatus;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;

import java.io.Serializable;
import java.time.LocalDateTime;

@Table(name = "onmisite_company")
@Entity
@Getter
@Setter
//@Audited
//@EntityListeners(AuditingEntityListener.class)
public class Company implements Serializable {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long companyId;
    @Column
    private String companyName;
    @Column
    private String address1;
    @Column
    private String address2;
    @Column
    private String address3;
    @Column
    private String email;
    @Column
    private String phoneNo;
    @Column
    private String mobileNo;
    @Column
    private String faxNo;

    @Column
    private boolean activated;


    @Column
    private boolean isAllowedAdminAccess;

    @Column(nullable = false, updatable = false)
    @CreatedBy
    private String createdBy = "";
    @LastModifiedBy
    private String modifiedBy;
    @Column(nullable = false)
    @Enumerated(EnumType.STRING)
    private RecordStatus recordStatus = RecordStatus.ACTIVE;
    @Column(nullable = false, updatable = false)
    @CreatedDate
    private LocalDateTime createdDate;
    @LastModifiedDate
    private LocalDateTime modifiedDate;

}
