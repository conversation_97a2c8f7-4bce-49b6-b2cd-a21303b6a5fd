package com.misyn.onmisite.dto;

import lombok.Data;
import java.math.BigDecimal;

@Data
public class FeeCalculationRequestDto {
    
    private String calType;

    // ACR calculation
    private String costPart;
    private String costLabour;

    // Bald Tyre Penalty
    private String acr;
    private String boldPercent;

    // Under Penalty
    private String underPenaltyPercent;

    // Payable Amount
    private String excess;
    private String underPenaltyAmount;
    private String boldTirePenaltyAmount;
}
