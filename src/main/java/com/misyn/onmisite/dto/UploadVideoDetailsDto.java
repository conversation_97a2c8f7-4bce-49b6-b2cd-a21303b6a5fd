package com.misyn.onmisite.dto;


import com.misyn.onmisite.enums.RecordStatus;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;

@Data
public class UploadVideoDetailsDto implements Serializable {
    private Long uploadVideoDetailsRefNo;
    private Long jobId;
    private Long callId;
    private String videoName;
    private String videoPath;
    private String inputDateTime;
    private String latitude;
    private String longitude;
    private BigDecimal distance;
    private BigDecimal fileSize;
    private Integer videoIndex;
    private Long companyId;
    private String mapIcon;
    private String mapTitle;
    private String mapUrl;
    private double latitudeVal = 0;
    private double longitudeVal = 0;

    private String createdBy = "";
    private String modifiedBy;
    private RecordStatus recordStatus = RecordStatus.ACTIVE;
    private LocalDateTime createdDate;
    private LocalDateTime modifiedDate;

}
