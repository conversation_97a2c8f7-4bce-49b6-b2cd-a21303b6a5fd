package com.misyn.onmisite.dto;

import com.misyn.onmisite.enums.RecordStatus;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;

@Data
public class CallDetailsDto implements Serializable {

    private Long callId;
    private Long jobId;
    private LocalDateTime callStartDateTime;
    private LocalDateTime callEndDateTime;
    private String latitude;
    private String longitude;
    private String accessToken;
    private LocalDateTime tokenGenerateDateTime;
    private String callRemark;
    private String link;
    private String title;
    private String assignee;
    private String callingMobileNo;
    private Integer inspectionTypeId;
    private String inspectionJobNumber;

    private String roomName;
    private String roomSid;
    private String localParticipantSid;
    private String remoteParticipantSid;

    private String createdBy = "";
    private String modifiedBy;
    private RecordStatus recordStatus = RecordStatus.ACTIVE;
    private LocalDateTime createdDate;
    private LocalDateTime modifiedDate;
}
