package com.misyn.onmisite.dto;

import com.misyn.onmisite.entity.ClaimTireConditionMe;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class ClaimsDto implements Serializable {


    private Integer refNo;
    private Integer claimNo;
    private String intimationNo;
    private String isfClaimNo;
    private String preferredLanguage;
    private String policyBranch;
    private String policyType;
    private String policyNumber;
    private String vehicleNo;
    private Integer releshipInsurd;
    private Integer reporterTitle;
    private String reporterName;
    private String reporterId;
    private String cliNo;
    private String otherContNo;
    private String insurdMobNo;
    private String isSameReportDriver;
    private Integer driverStatus;
    private Integer driverTitle;
    private String driverName;
    private Integer driverReleshipInsurd;
    private String driverNic;
    private String dlNo;
    private String driverLicenceType;
    private String reporterRemark;
    private Integer intimationType;
    private Integer lateIntimateReason;
    private String accidDate;
    private String accidTime;
    private String dateOfReport;
    private String timeOfReport;
    private String isCatEvent;
    private Integer catEventCode;
    private String catEvent;
    private Integer causeOfLoss;
    private String accidDesc;
    private String placeOfAccid;
    private Integer districtCode;
    private Integer nearestCity;
    private String currentLocation;
    private Integer nearPoliceStation;
    private String isFirstStatementReq;
    private Integer firstStatementReqReason;
    private String firstStatementRemark;
    private String inspectionType;
    private Integer inspectionTypeReason;
    private Integer draftReason;
    private String followCallUserId;
    private String followCallDoneDateTime;
    private Integer followCallContactPersonTitle;
    private String followCallContactPersonName;
    private String followCallContactNumber;
    private Integer followCallAgnetServiceRate;
    private Integer followCallAssessorServiceRate;
    private String ncbProm;
    private Integer ncbReason;
    private String ncbRemark;
    private String lomoProm;
    private Integer lomoReason;
    private String callUser;
    private Integer claimStatus;
    private String vehicleColor;
    private String coverNoteNo;
    private Integer polRefNo;
    private String isfsUpdateStatus;
    private String polChkStatus;
    private String isNoDamage;
    private String damageRemark;
    private String isHugeDamage;
    private String hugeRemark;
    private String damageNotGiven;
    private String printLetter;
    private String negPremOut;
    private String landMark;
    private Integer vehClsId;
    private String isDoubt;
    private String isDoubtRemark;
    private String isFutherDamage;
    private String draftRemark;
    private Integer accessusrType;
    private String recStatus;
    private String inpUser;
    private String inpTime;
    private String isFollowupCallDone;
    private String driverDetailNotRelevant;
    private String driverDetailSubmit;
    private String policyChannelType;
    private String isTheftAndFound;

    PolicyDto policyDto;
    InspectionDto inspectionDto;
    List<ClaimTireConditionMe> tireConditionMeList;
    ClaimAssignClaimHandlerDto claimAssignClaimHandlerDto;


}
