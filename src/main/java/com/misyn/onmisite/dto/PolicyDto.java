package com.misyn.onmisite.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
@Data
public class PolicyDto {
    private Integer policyRefNo;
    private String policyBranch;
    private String policyType;
    private String policyNumber;
    private Integer renewalCount;
    private Integer endorsementCount;
    private String vehicleNumber;
    private LocalDate expireDate;
    private LocalDate inspectionDate;
    private LocalDate originalInspectionDate;
    private String policyStatus;
    private String customerName;
    private String customerNic;
    private String customerAddressLine1;
    private String customerAddressLine2;
    private String customerAddressLine3;
    private String customerMobileNo;
    private String customerLandNo;
    private BigDecimal annualPremium;
    private BigDecimal sumInsured;
    private LocalDate latestClaimIntimationDate;
    private LocalDate latestClaimLossDate;
    private String agentBroker;
    private BigDecimal totalPremiumOutstanding;
    private Integer noOfDaysPremiumOutstanding;
    private String engineNo;
    private String chassisNo;
    private String vehicleMake;
    private String vehicleModel;
    private Integer manufactureYear;
    private BigDecimal excess;
    private Integer ncbRate;
    private BigDecimal ncbAmount;
    private String coverNoteNo;
    private String coverType;
    private String channel;
    private String updateFlag;
    private String insertFlag;
    private String createUser;
    private LocalDate createDate;
    private String createTime;
    private String cancelReason;
    private String lapsedDate;
    private LocalDate registerDate;
    private String policyCancelDate;
    private String location;
    private String risk;
    private String bodyType;
    private String clientId;
    private String isThirdParty;
    private String financeCompany;
    private String vehicleUsage;
    private String agentCode;
    private String fuelType;
    private Integer noOfSeat;
    private Integer vehicleAge;
    private String policySuspend;
    private String engineCapacity;
    private String branchCode;
    private String product;
    private String lastModifyUser;
    private String lastModifyDateTime;
    private String currentPolicyStatus;
    private String vehicleColor;
    private String tradePlateNo;
    private LocalDate lastModifyDate;
    private String indComFlag;
    private String companyBranch;
    private String companyCode;
    private LocalDateTime cmsUpdateDateTime;
    private String finCompanyCode;
    private String finCompanyBranch;
    private String loanAccountNo;
    private String idenCode;
    private String vehicleNoLastDigit;
    private String policyNumberLastDigit;
    private String policyChannelType;
    private String businessType;
    private String introducer;
    private String bankRefNo;
    private String workflow;
    private String categoryDesc;
}
