package com.misyn.onmisite.dto.input;

public record InputJobDetailDto(String claimNumber,
                                String inspectionJobNumber,
                                String assignee,
                                String callingMobileNo,
                                Integer inspectionTypeId,
                                String policyNo,
                                String vehicleNo,
                                String vehicleMake,
                                String vehicleModel,
                                String insuredName,
                                String insuredAddress1,
                                String insuredAddress2,
                                String insuredAddress3,
                                String insuredMobileNo,
                                String insuredEmail) {


    public InputJobDetailDto {
        if (claimNumber == null || claimNumber.isBlank()) {
            throw new IllegalArgumentException("Claim number cannot be null or empty");
        }if (inspectionJobNumber == null || inspectionJobNumber.isBlank()) {
            throw new IllegalArgumentException("Inspection Job number cannot be null or empty");
        }
        if (assignee == null || assignee.isBlank()) {
            throw new IllegalArgumentException("Assignee cannot be null or empty");
        }
        if (callingMobileNo == null || callingMobileNo.isBlank()) {
            throw new IllegalArgumentException("Calling Mobile Number cannot be null or empty");
        }
        if (inspectionTypeId == null ) {
            throw new IllegalArgumentException("Inspection Type Id cannot be null or empty");
        }
        if (policyNo == null || policyNo.isBlank()) {
            throw new IllegalArgumentException("Policy Number cannot be null or empty");
        }
        if (vehicleNo == null || vehicleNo.isBlank()) {
            throw new IllegalArgumentException("Vehicle number cannot be null or empty");
        }if (insuredName == null || insuredName.isBlank()) {
            throw new IllegalArgumentException("Insured Name cannot be null or empty");
        }

    }
}
