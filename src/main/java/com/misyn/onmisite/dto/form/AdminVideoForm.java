package com.misyn.onmisite.dto.form;

import com.misyn.onmisite.dto.CallDetailsDto;
import com.misyn.onmisite.dto.CallUploadImageVideoDetailDto;
import com.misyn.onmisite.dto.JobDetailDto;
import com.misyn.onmisite.dto.ListItem;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

@Data
public class AdminVideoForm implements Serializable {
    private JobDetailDto jobDetailDto;
    private CallDetailsDto callDetailsDto;
    private List<CallUploadImageVideoDetailDto> prevCallImageVideoList = new ArrayList<>();
    private List<ListItem> documnetTypeList = new ArrayList<>();
}
