package com.misyn.onmisite.dto;

import com.misyn.onmisite.enums.RecordStatus;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;


@Builder
@NoArgsConstructor
@AllArgsConstructor
@Data
public class RemarkDetailDto {
    private Long remarkDetailId;
    private Long jobId;
    private String remark;


    private String createdBy = "";
    private String modifiedBy;
    private RecordStatus recordStatus = RecordStatus.ACTIVE;
    private LocalDateTime createdDate;
    private LocalDateTime modifiedDate;
}
