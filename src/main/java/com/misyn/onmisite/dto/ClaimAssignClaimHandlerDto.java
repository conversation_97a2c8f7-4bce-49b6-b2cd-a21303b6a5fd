package com.misyn.onmisite.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
public class ClaimAssignClaimHandlerDto {

    private Integer txnNo;
    private LocalDateTime penaltyUnderInsuranceDateTime;

    private String intimationCheck;
    private String intimationCheckUser;
    private LocalDateTime intimationCheckDateTime;
    private String lcCheck1;
    private String lcCheck2;
    private String lcCheck3;
    private String lcCheck4;
    private String lcCheck5;
    private String lcCheck6;
    private String lcCheck7;
    private String lcCheck8;
    private String lcCheckUser;
    private LocalDateTime lcCheckDateTime;
    private String claimPanelAssignUsers;
    private LocalDateTime claimPanelAssignUserDateTime;
    private Integer decisionApprovalClaimPanel;
    private String claimPanelDecision;
    private LocalDateTime claimPanelDecisionDateTime;
    private Integer repudiatedType;
    private String isPrintRepudiatedLetter;
    private String repudiatedLetterPrintUserId;
    private LocalDate repudiatedLetterPrintDateTime;
    private String financialInterest;
    private Integer leasingRefNo;
    private String finalizeUserId;
    private LocalDateTime finalizeDateTime;
    private String isGenerateSupplyOrder;
    private String supplyOrderAssignStatus;
    private String supplyOrderAssignUser;
    private LocalDateTime supplyOrderAssignDateTime;
    private String supplyOrderCreateUser;
    private LocalDateTime supplyOrderCreateDateTime;
    private String supplyOrderCreateClose;
    private String isFileStore;
    private String fileUserStoreUserId;
    private LocalDateTime fileStoreDateTime;
    private String reopenAssignUserId;
    private LocalDateTime reopenAssignUserDateTime;
    private String reopenUserId;
    private LocalDateTime reopenDateTime;
    private Integer reopenNoOfTime;
    private String isGenFinalRemindLetter;
    private LocalDateTime genFinalRemindLetterDateTime;
    private String decisionMakingAssignUserId;
    private LocalDateTime decisionMakingAssignDateTime;
    private String investigationStatus;
    private String investigationAssignUserId;
    private LocalDateTime investigationAssignDateTime;
    private String investigationArrangeUserId;
    private LocalDateTime investigationArrangeDateTime;
    private String investigationCompletedUserId;
    private LocalDateTime investigationCompletedDateTime;
    private String specialApprovalInputUserId;
    private LocalDateTime specialApprovalInputDateTime;
    private String specialApprovalUserId;
    private LocalDateTime specialApprovalDateTime;
    private Integer oldClaimStatus;
    private String oldEngineerClaimStatus;
    private String isDoubt;
    private String isOnSiteOffer;
    private BigDecimal aprvAdvanceAmount;
    private String reopenType;
    private String closeStatus;
    private String closeUser;
    private LocalDateTime closeDateTime;
    private Integer repudiatedLetterType;
    private String inpStatus;
    private String inpUserId;
    private LocalDateTime inpDateTime;
    private Integer versionNo;
    private String isExcessInclude;
    private String isProvideOffer;
    private String letterPanelUserId;
    private LocalDateTime letterPanelDateTime;
    private String isRejectionAttached;
    private LocalDateTime rejectionAttachedDateTime;
    private String advanceApprovalAssignUser;
    private LocalDateTime advanceApprovalAssignDateTime;
    private String advanceStatus;
    private String advanceForwardedUser;
    private BigDecimal approvalPendingAdvanceAmount;
    private BigDecimal totalAcrApproved;
    private String advanceApprovedUser;
    private LocalDateTime advanceApprovedDateTime;
    private String forwardedEngineer;
} 
