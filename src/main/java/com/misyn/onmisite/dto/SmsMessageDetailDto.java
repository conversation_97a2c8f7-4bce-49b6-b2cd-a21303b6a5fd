package com.misyn.onmisite.dto;

import com.misyn.onmisite.enums.RecordStatus;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
public class SmsMessageDetailDto implements Serializable {
    private Long smsDetailId;
    private String destination;
    private String message;
    private String messageType;
    private Integer messagePriority;
    private String messageStatus;
    private LocalDateTime sendDateTime;

    private String createdBy = "";
    private String modifiedBy;
    private RecordStatus recordStatus = RecordStatus.ACTIVE;
    private LocalDateTime createdDate;
    private LocalDateTime modifiedDate;
}
