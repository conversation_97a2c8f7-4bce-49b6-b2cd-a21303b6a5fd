package com.misyn.onmisite.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.misyn.onmisite.controller.v1.errors.ErrorDto;
import com.misyn.onmisite.enums.ApiResponseStatus;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@Builder
public class ApiResponseDto<T> {
    private ApiResponseStatus status = ApiResponseStatus.SUCCESS;
    private List<ErrorDto> errors;
    private String message;
    private T results;
}
