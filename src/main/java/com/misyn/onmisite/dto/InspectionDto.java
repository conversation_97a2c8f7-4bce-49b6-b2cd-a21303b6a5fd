package com.misyn.onmisite.dto;

import lombok.Data;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
public class InspectionDto {
    
    private Integer inspectionId;
    private Integer refNo;
    private String jobNo;
    private Integer claimNo;
    private String makeConfirm;
    private String modelConfirm;
    private String engNoConfirm;
    private String chassisNoConfirm;
    private String chassisNo;
    private Integer notCheckedReason;
    private String yearMakeConfirm;
    private LocalDateTime inspectDatetime;
    private BigDecimal pav;
    private String damageDetails;
    private String pad;
    private String genunOfAccid;
    private String firstStatementRqed;
    private String firstStatementReqReason;
    private String investRqed;
    private String assessorRemark;
    private Integer inspectionType;
    private Integer jobType;
    private String assignedLocation;
    private String currentLocation;
    private String placeOfInspection;
    private Integer mileage;
    private BigDecimal costOfCall;
    private BigDecimal otherFee;
    private BigDecimal totalAssessorFee;
    private String feeDesc;
    private Integer recordStatus;
    private String inpUser;
    private LocalDateTime inpDatetime;
    private String assFeeAprStatus;
    private String assFeeAprUser;
    private LocalDateTime assFeeAprDatetime;
    private String assEstiAprStatus;
    private String assEstiAprUser;
    private LocalDateTime assEstiAprDatetime;
    private String assignAssessorUser;
    private LocalDateTime assignAssessorDatetime;
    private String assignRteUser;
    private LocalDateTime assignRteDatetime;
    private String approveAssignRteUser;
    private LocalDateTime approveAssignDatetime;
    private String fwdRteDesktopUser;
    private LocalDateTime fwdRteDesktopDatetime;
    private String assignTcUser;
    private LocalDateTime assignTcDatetime;
    private String fwdTcUser;
    private LocalDateTime fwdTcDatetime;
    private String fwdTcDesktopUser;
    private LocalDateTime fwdTcDesktopDatetime;
    private String vehicleAvailable;
}
