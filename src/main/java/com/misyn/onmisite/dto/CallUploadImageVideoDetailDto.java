package com.misyn.onmisite.dto;

import com.misyn.onmisite.enums.RecordStatus;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Data
public class CallUploadImageVideoDetailDto implements Serializable {
    private int callIndex;
    private String title;
    private CallDetailsDto callDetailsDto;
    private List<UploadDocumentTypeDto> uploadDocumentTypeList = new ArrayList<>();
    private List<UploadImageDetailsDto> uploadImageList = new ArrayList<>();
    private List<UploadVideoDetailsDto> uploadVideoList = new ArrayList<>();

    private String createdBy = "";
    private String modifiedBy;
    private RecordStatus recordStatus = RecordStatus.ACTIVE;
    private LocalDateTime createdDate;
    private LocalDateTime modifiedDate;
}
