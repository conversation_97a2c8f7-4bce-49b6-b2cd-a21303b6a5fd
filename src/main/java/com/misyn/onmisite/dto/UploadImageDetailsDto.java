package com.misyn.onmisite.dto;


import com.misyn.onmisite.enums.RecordStatus;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;

@Data
public class UploadImageDetailsDto  implements Serializable {
    private Long uploadImageDetailsRefNo;
    private Long jobId;
    private Long callId;
    private String imageName;
    private String imagePath;
    private String thumbImageName;
    private String thumbImagePath;
    private String editImageName;
    private String editImagePath;
    private String thumbEditImageName;
    private String thumbEditImagePath;
    private String inputDateTime;
    private String latitude;
    private String longitude;
    private BigDecimal distance;
    private BigDecimal fileSize;
    private BigDecimal thumbFileSize;
    private Integer imageIndex;
    private Long companyId;
    private String mapIcon;
    private String mapTitle;
    private String mapUrl;
    private double latitudeVal = 0;
    private double longitudeVal = 0;
    private Integer documentTypeId;

    private String createdBy = "";
    private String modifiedBy;
    private RecordStatus recordStatus = RecordStatus.ACTIVE;
    private LocalDateTime createdDate;
    private LocalDateTime modifiedDate;


}
