package com.misyn.onmisite.dto;

import lombok.Data;

import java.io.Serializable;

@Data
public class ImageData implements Serializable {
    private Integer index;
    private String fileName = "myImage.png";
    private String accessToken;
    private String imageData;
    private String uploadStatus;
    private Integer callIndex;
    private Long uploadImageDetailsRefNo;
    private String latitude;
    private String longitude;
    private Long callId;
    private Integer mainDocumentTypeId;
    private Integer subDocumentTypeId;
    private Integer  claimNumber;
}
