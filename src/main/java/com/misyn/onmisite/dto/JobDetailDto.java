package com.misyn.onmisite.dto;


import com.misyn.onmisite.enums.JobStatus;
import com.misyn.onmisite.enums.RecordStatus;
import jakarta.persistence.Column;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

@Data
public class JobDetailDto implements Serializable {

    private Long jobId;
    @NotBlank(message = "Job Number is mandatory")
    private String jobNumber;
    private String policyNo;
    @NotBlank(message = "Vehicle Number is mandatory")
    private String vehicleNo;
    private String vehicleMake;
    private String vehicleModel;

    @NotBlank(message = "Insured Name is mandatory")
    private String insuredName;
    private String insuredMobileNo;
    private String idNo;
    private String insuredEmail;
    private String insuredAddress1;
    private String insuredAddress2;
    private String insuredAddress3;
    private JobStatus jobStatus;
    private Long companyId;
    private String jobDescription;
    private String callAccessToken;
    private List<CallDetailsDto> callDetailsDtoList;

    private String createdBy = "";
    private String modifiedBy;
    private RecordStatus recordStatus = RecordStatus.ACTIVE;
    private LocalDateTime createdDate;
    private LocalDateTime modifiedDate;
}
