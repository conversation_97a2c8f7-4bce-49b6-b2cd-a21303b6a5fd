package com.misyn.onmisite.jobprocess;


import com.misyn.onmisite.service.TwilioService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

@RequiredArgsConstructor
@Slf4j
@Component
public class RecordCompositionJob {
    private final TwilioService twilioService;

    @Scheduled(fixedRate = 1000 * 5)
    public void compositionVideo() {
        try {
            twilioService.compositionVideo();
        } catch (Exception e) {
            log.error(e.getMessage());
        }
    }

    @Scheduled(fixedRate = 1000 * 6)
    public void downloadVideoFile() {
        try {
            twilioService.downloadVideoFiles();
        } catch (Exception e) {
            log.error(e.getMessage());
        }
    }
}
