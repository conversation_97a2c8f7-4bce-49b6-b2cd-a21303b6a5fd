package com.misyn.onmisite.exception;

public class ClientAuthException extends BaseException {
    public ClientAuthException() {
    }

    public ClientAuthException(String message) {
        super(message);
    }

    public ClientAuthException(String message, Throwable cause) {
        super(message, cause);
    }

    public ClientAuthException(Throwable cause) {
        super(cause);
    }

    public ClientAuthException(String message, Throwable cause, boolean enableSuppression, boolean writableStackTrace) {
        super(message, cause, enableSuppression, writableStackTrace);
    }
}
