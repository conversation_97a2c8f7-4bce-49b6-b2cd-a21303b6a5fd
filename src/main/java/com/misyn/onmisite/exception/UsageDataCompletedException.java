package com.misyn.onmisite.exception;

public class UsageDataCompletedException extends BaseException {
    public UsageDataCompletedException() {
    }

    public UsageDataCompletedException(String message) {
        super(message);
    }

    public UsageDataCompletedException(String message, Throwable cause) {
        super(message, cause);
    }

    public UsageDataCompletedException(Throwable cause) {
        super(cause);
    }

    public UsageDataCompletedException(String message, Throwable cause, boolean enableSuppression, boolean writableStackTrace) {
        super(message, cause, enableSuppression, writableStackTrace);
    }
}
