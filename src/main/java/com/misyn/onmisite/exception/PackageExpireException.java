package com.misyn.onmisite.exception;

public class PackageExpireException extends BaseException {
    public PackageExpireException() {
    }

    public PackageExpireException(String message) {
        super(message);
    }

    public PackageExpireException(String message, Throwable cause) {
        super(message, cause);
    }

    public PackageExpireException(Throwable cause) {
        super(cause);
    }

    public PackageExpireException(String message, Throwable cause, boolean enableSuppression, boolean writableStackTrace) {
        super(message, cause, enableSuppression, writableStackTrace);
    }
}
