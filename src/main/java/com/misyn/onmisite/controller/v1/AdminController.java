package com.misyn.onmisite.controller.v1;

import com.misyn.onmisite.dto.*;
import com.misyn.onmisite.dto.form.AdminVideoForm;
import com.misyn.onmisite.service.ClaimService;
import com.misyn.onmisite.service.JobService;
import com.misyn.onmisite.service.StorageService;
import com.misyn.onmisite.util.AppConstant;
import jakarta.servlet.http.HttpSession;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.security.oauth2.core.oidc.user.OidcUser;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

@RequiredArgsConstructor
@Slf4j
@Controller
public class AdminController extends BaseController {
    private final JobService jobService;
    private final StorageService storageService;
    private final ClaimService claimService;

    @GetMapping("/admin-view/{token}")
    public String view(@PathVariable String token, HttpSession session) {
        session.setAttribute("redirectUuid", token);
        return "redirect:/oauth2/authorization/keycloak"; // Redirect to Keycloak login
    }

    @GetMapping("/redirect-after-login")
    public String redirectAfterLogin(HttpSession session) {
        String uuid = (String) session.getAttribute("redirectUuid");

        if (uuid != null) {
            session.removeAttribute("redirectUuid"); // Clean up session
            return "redirect:/view/" + uuid;
        }

        return "redirect:/"; // Default redirect if UUID is missing
    }

    @GetMapping("/")
    public String adminHomePage() {
        return AppURL.PAGE_403;
    }


    @GetMapping("/view/{token}")
    public String adminHomePage(Model model, @AuthenticationPrincipal OidcUser user, @PathVariable String token) {
        UserDto userDto = getUserDto(user);
        AdminVideoForm agentVideoForm = jobService.getAgentVideoForm(token, userDto.getUsername());
        ClaimsDto allClaimDetail = claimService.getAllClaimDetail(agentVideoForm);
//        ClaimInspectionInfoMainMeDTO allInspectionDetail = claimService.getAllInspectionDetail(agentVideoForm);
        model.addAttribute(AppConstant.ADMIN_VIDEO_FORM, agentVideoForm);
        model.addAttribute(AppConstant.CLAIM_DETAIL, allClaimDetail);
        model.addAttribute(AppConstant.USER_DTO, user);
        return AppURL.ADMIN_HOME_PAGE;
    }

    @PostMapping("/update/call-end-date-time")
    public ResponseEntity<Integer> updateCallEndDateTime(@RequestBody CallDetailsDto callDetailsDto, @AuthenticationPrincipal OidcUser user) {
        UserDto userDto = getUserDto(user);
        int result = jobService.updateCallEndDateTime(callDetailsDto.getAccessToken(), userDto.getUsername());
        return ResponseEntity.status(HttpStatus.OK).body(result);
    }

    @PostMapping("/update/call-end-date-time/{accessToken}")
    public String updateCallEndDateTime(@PathVariable String accessToken, @AuthenticationPrincipal OidcUser user) {
        UserDto userDto = getUserDto(user);
        jobService.updateCallEndDateTime(accessToken, userDto.getUsername());
        return "redirect:/list";
    }

    @GetMapping("/view-map/{latitude}/{longitude}")
    public ModelAndView getViewMap(@PathVariable String latitude, @PathVariable String longitude) {
        ModelAndView modelAndView = new ModelAndView(AppURL.MAP_VIEW_PAGE);
        modelAndView.addObject("latitude", latitude);
        modelAndView.addObject("longitude", longitude);
        return modelAndView;
    }


    @GetMapping("/join-view/{jobId}")
    public ModelAndView jobDetailView(Model model, @AuthenticationPrincipal OidcUser user, @PathVariable Long jobId) {
        UserDto userDto = getUserDto(user);
        ModelAndView modelAndView = new ModelAndView(AppURL.ADMIN_HOME_PAGE);
        AdminVideoForm agentVideoForm = jobService.getAgentVideoForm(userDto.getCompanyId(), jobId);
        model.addAttribute(AppConstant.ADMIN_VIDEO_FORM, agentVideoForm);
        return modelAndView;
    }


 /*   @GetMapping("/join/{jobId}")
    public String join(@AuthenticationPrincipal OidcUser user, @PathVariable Long jobId) {
        UserDto userDto = getUserDto(user);
        JobDetailDto savedJobDetailDto = jobService.saveAndJoin(jobId, userDto.getUsername(), userDto.getCompanyId());
        if (savedJobDetailDto != null && savedJobDetailDto.getCallAccessToken() != null) {
            return "redirect:/view/" + savedJobDetailDto.getCallAccessToken();
        }
        return "redirect:/list";
    }*/

    @GetMapping("/join/{jobId}/{accessToken}")
    public String joinExistingCall(@AuthenticationPrincipal OidcUser user, @PathVariable Long jobId, @PathVariable String accessToken) {
        UserDto userDto = getUserDto(user);
        JobDetailDto savedJobDetailDto = jobService.saveAndJoinExistingCall(jobId, userDto.getUsername(), userDto.getCompanyId(), accessToken);
        if (savedJobDetailDto != null && savedJobDetailDto.getCallAccessToken() != null) {
            return "redirect:/view/" + savedJobDetailDto.getCallAccessToken();
        }
        return "redirect:/list";
    }
}
