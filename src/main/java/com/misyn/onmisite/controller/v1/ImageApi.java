package com.misyn.onmisite.controller.v1;


import com.misyn.onmisite.dto.CallUploadImageVideoDetailDto;
import com.misyn.onmisite.dto.ImageData;
import com.misyn.onmisite.dto.UploadImageDetailsDto;
import com.misyn.onmisite.dto.UserDto;
import com.misyn.onmisite.dto.form.AdminVideoForm;
import com.misyn.onmisite.service.ImageService;
import com.misyn.onmisite.service.JobService;
import com.misyn.onmisite.service.StorageService;
import com.misyn.onmisite.util.AppConstant;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.tomcat.util.http.fileupload.IOUtils;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.security.oauth2.core.oidc.user.OidcUser;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import java.io.BufferedInputStream;
import java.io.IOException;
import java.io.InputStream;

@RequiredArgsConstructor
@Slf4j
@RequestMapping("/api/v1/image")
@RestController
public class ImageApi extends BaseController {
    private final StorageService storageService;
    private final ImageService imageService;
    private final JobService jobService;


    @PostMapping("/upload")
    @PreAuthorize("hasAnyRole('ROLE_SUPER_ADMIN','ROLE_COMPANY_ADMIN','ROLE_AGENT_USER')")
    public ResponseEntity<ImageData> imageUpload(@RequestBody ImageData imageData, @AuthenticationPrincipal OidcUser oidcuser) {
        UserDto userDto = this.getUserDto(oidcuser);
        ImageData saveImageData = imageService.saveImageData(imageData, userDto);
        saveImageData.setUploadStatus("Success");
        return ResponseEntity.status(HttpStatus.OK).body(saveImageData);
    }

    @PostMapping("/upload-edit-image")
    @PreAuthorize("hasAnyRole('ROLE_SUPER_ADMIN','ROLE_COMPANY_ADMIN','ROLE_AGENT_USER')")
    public ResponseEntity<ImageData> editImageUpload(@RequestBody ImageData imageData, @AuthenticationPrincipal OidcUser oidcuser) {
        UserDto user = this.getUserDto(oidcuser);
        ImageData saveImageData = imageService.saveEditImageData(imageData, user);
        saveImageData.setUploadStatus("Success");
        return ResponseEntity.status(HttpStatus.OK).body(saveImageData);
    }


    @RequestMapping(value = "/view-thumb-image/{uploadImageDetailsRefNo}", method = RequestMethod.GET)
    @PreAuthorize("hasAnyRole('ROLE_SUPER_ADMIN','ROLE_COMPANY_ADMIN','ROLE_AGENT_USER')")
    public void getThumbImageAsByteArray(HttpServletResponse response, @PathVariable Long uploadImageDetailsRefNo) throws IOException {
        UploadImageDetailsDto uploadImageDetailsDto = imageService.getUploadImageDetailsDto(uploadImageDetailsRefNo);
        String imagePath = StringUtils.hasLength(uploadImageDetailsDto.getThumbEditImagePath())
                ? uploadImageDetailsDto.getThumbEditImagePath()
                : uploadImageDetailsDto.getThumbImagePath();

        setImageStream(response, uploadImageDetailsDto, imagePath);
    }

    private void setImageStream(HttpServletResponse response, UploadImageDetailsDto uploadImageDetailsDto, String imagePath) throws IOException {
        InputStream in = new BufferedInputStream(storageService.downloadFile(imagePath));
        if (uploadImageDetailsDto.getImagePath().endsWith("jpg")) {
            response.setContentType(MediaType.IMAGE_JPEG_VALUE);
        } else if (uploadImageDetailsDto.getImagePath().endsWith("png")) {
            response.setContentType(MediaType.IMAGE_PNG_VALUE);
        } else if (uploadImageDetailsDto.getImagePath().endsWith("gif")) {
            response.setContentType(MediaType.IMAGE_GIF_VALUE);
        } else {
            response.setContentType(MediaType.APPLICATION_OCTET_STREAM_VALUE);
        }
        IOUtils.copy(in, response.getOutputStream());
    }

    @RequestMapping(value = "/view-original-image/{uploadImageDetailsRefNo}", method = RequestMethod.GET)
    @PreAuthorize("hasAnyRole('ROLE_SUPER_ADMIN','ROLE_COMPANY_ADMIN','ROLE_AGENT_USER')")
    public void getImageAsByteArray(HttpServletResponse response, @PathVariable Long uploadImageDetailsRefNo) throws IOException {
        UploadImageDetailsDto uploadImageDetailsDto = imageService.getUploadImageDetailsDto(uploadImageDetailsRefNo);
        String imagePath = StringUtils.hasLength(uploadImageDetailsDto.getEditImagePath())
                ? uploadImageDetailsDto.getEditImagePath()
                : uploadImageDetailsDto.getImagePath();

        setImageStream(response, uploadImageDetailsDto, imagePath);
    }

    @PostMapping("/delete")
    @PreAuthorize("hasAnyRole('ROLE_SUPER_ADMIN','ROLE_COMPANY_ADMIN','ROLE_AGENT_USER')")
    public ModelAndView delete(@RequestBody ImageData imageData, HttpServletRequest request) throws IOException {
        ModelAndView modelAndView = new ModelAndView(AppURL.IMAGE_CONTAINER_PAGE);
        UploadImageDetailsDto uploadImageDetailsDto = imageService.deleteByUploadImageDetailsRefNo(imageData.getUploadImageDetailsRefNo());
        CallUploadImageVideoDetailDto callUploadImageVideoDetailDto = jobService.getCallUploadImageVideoDetailDto(uploadImageDetailsDto.getJobId(), uploadImageDetailsDto.getCallId(), imageData.getCallIndex());
        modelAndView.addObject("prevCallImageVideo", callUploadImageVideoDetailDto);
        return modelAndView;
    }

    @PostMapping("/reload-image-viewer")
    @PreAuthorize("hasAnyRole('ROLE_SUPER_ADMIN','ROLE_COMPANY_ADMIN','ROLE_AGENT_USER')")
    public ModelAndView reloadImageViewer(@RequestBody ImageData imageData, HttpServletRequest request) throws IOException {
        ModelAndView modelAndView = new ModelAndView(AppURL.IMAGE_CONTAINER_PAGE);
        CallUploadImageVideoDetailDto callUploadImageVideoDetailDto = jobService.getCallUploadImageVideoDetailDto(imageData.getCallId());
        modelAndView.addObject("prevCallImageVideo", callUploadImageVideoDetailDto);
        return modelAndView;
    }

    @PostMapping("/reload-previous-image-viewer")
    @PreAuthorize("hasAnyRole('ROLE_SUPER_ADMIN','ROLE_COMPANY_ADMIN','ROLE_AGENT_USER')")
    public ModelAndView reloadPreviousImageViewer(@RequestBody ImageData imageData, @AuthenticationPrincipal OidcUser user) throws IOException {
        UserDto userDto = getUserDto(user);
        ModelAndView modelAndView = new ModelAndView(AppURL.PREVIOUS_IMAGE_CONTAINER_PAGE);
        AdminVideoForm agentVideoForm = jobService.getAgentVideoForm(imageData.getAccessToken(), userDto.getUsername());

        // Extract claim number from the request or from the job details
        Integer claimNumber = imageData.getClaimNumber();
        if (claimNumber == null && agentVideoForm.getJobDetailDto() != null) {
            // If claim number is not provided in request, try to get it from job number
            try {
                claimNumber = Integer.parseInt(agentVideoForm.getJobDetailDto().getJobNumber());
            } catch (NumberFormatException e) {
                // Handle case where job number is not a valid claim number
                log.warn("Could not parse claim number from job number: " + agentVideoForm.getJobDetailDto().getJobNumber());
            }
        }

        // Fetch previous photos organized by inspection type if claim number is available
        if (claimNumber != null) {
            try {
                var previousPhotos = imageService.getPreviousPhotosByClaimNumber(claimNumber);
                modelAndView.addObject("previousPhotos", previousPhotos);
            } catch (Exception e) {
                log.error("Error fetching previous photos for claim number: " + claimNumber, e);
                // Continue without previous photos data
            }
        }

        modelAndView.addObject(AppConstant.ADMIN_VIDEO_FORM, agentVideoForm);
        return modelAndView;
    }
}
