package com.misyn.onmisite.controller.v1;

import com.misyn.onmisite.dto.CallDetailsDto;
import com.misyn.onmisite.dto.UploadVideoDetailsDto;
import com.misyn.onmisite.service.StorageService;
import com.misyn.onmisite.service.TwilioService;
import com.misyn.onmisite.service.VideoService;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;

@RequiredArgsConstructor
@Slf4j
@RequestMapping("/api/v1/video")
@RestController
public class VideoApi extends BaseController {
    private final VideoService videoService;
    private final TwilioService twilioService;
    private final StorageService storageService;

    @GetMapping("/play/{uploadVideoDetailsRefNo}")
    @PreAuthorize("hasAnyRole('ROLE_SUPER_ADMIN','ROLE_COMPANY_ADMIN','ROLE_AGENT_USER')")
    public void playVideoStream(HttpServletRequest request, HttpServletResponse response, @PathVariable Long uploadVideoDetailsRefNo) {
        try {
            UploadVideoDetailsDto uploadVideoDetailsDto = videoService.getUploadVideoDetailsDto(uploadVideoDetailsRefNo);
            try (InputStream is = storageService.downloadFile(uploadVideoDetailsDto.getVideoPath())) {
                response.setHeader("Accept-Ranges", "bytes");
                response.setContentType("video/mp4");
                byte[] buffer = new byte[8192];
                OutputStream os = response.getOutputStream();
                int bytesRead;
                while ((bytesRead = is.read(buffer)) != -1) {
                    os.write(buffer, 0, bytesRead);
                }
                os.flush();
            } catch (Exception e) {
                response.setStatus(HttpServletResponse.SC_NOT_FOUND);
                response.getWriter().write("Video not found");
            }
        } catch (IOException e) {
            log.error(e.getMessage());
        }
    }


    @PostMapping("/recording/start")
    public ResponseEntity<String> startRecording(@RequestBody CallDetailsDto callDetailsDto) {
        twilioService.startAllRecording(callDetailsDto.getAccessToken());
        return ResponseEntity.status(HttpStatus.OK).body("Start");
    }

    @PostMapping("/recording/stop")
    public ResponseEntity<String> stopRecording(@RequestBody CallDetailsDto callDetailsDto) {
        twilioService.stopAllRecording(callDetailsDto.getAccessToken());
        return ResponseEntity.status(HttpStatus.OK).body("Stop");
    }
}
