package com.misyn.onmisite.controller.v1;


import com.misyn.onmisite.dto.CallDetailsDto;
import com.misyn.onmisite.dto.ImageData;
import com.misyn.onmisite.dto.UserDto;
import com.misyn.onmisite.dto.form.AdminVideoForm;
import com.misyn.onmisite.service.ImageService;
import com.misyn.onmisite.service.JobService;
import com.misyn.onmisite.util.AppConstant;
import com.misyn.onmisite.util.UrlServiceUtil;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;

import java.net.URI;

@RequiredArgsConstructor
@Controller
@RequestMapping("/api/v1/client")
public class ClientController {

    private final UrlServiceUtil urlServiceUtil;
    private final JobService jobService;
    private final ImageService imageService;


    @GetMapping("/view/{id}")
    public String clientVideoView(Model model, @PathVariable String id, HttpServletRequest request) {
        /*if (twilioService.isLoggedParticipantOne(id) == false) {
            request.getSession().removeAttribute(AppConstant.FACING_MODE);
            model.addAttribute("id", id);
            return AppURL.CLIENT_SESSION_END_PAGE;
        }*/
        AdminVideoForm clientVideoForm = jobService.getClientVideoForm(id);
        jobService.updateCallStartDateTime(id);
        String facingMode = (String) request.getSession().getAttribute(AppConstant.FACING_MODE);
        if (facingMode != null && !facingMode.equals(AppConstant.FACING_MODE_USER)) {
            facingMode = AppConstant.FACING_MODE_USER;
        } else {
            facingMode = AppConstant.FACING_MODE_ENVIRONMENT;
        }
        request.getSession().setAttribute(AppConstant.FACING_MODE, facingMode);
        model.addAttribute(AppConstant.ADMIN_VIDEO_FORM, clientVideoForm);
        return AppURL.CLIENT_VIDEO_VIEW_PAGE;
    }

    @GetMapping(value = "/{shortUrl}")
    @Cacheable(value = "urls", key = "#shortUrl", sync = true)
    public ResponseEntity<Void> getAndRedirect(@PathVariable String shortUrl) {
        String url = urlServiceUtil.getOriginalUrl(shortUrl);
        return ResponseEntity.status(HttpStatus.FOUND)
                .location(URI.create(url))
                .build();
    }

    @PostMapping("/update/geolocation")
    public ResponseEntity<Integer> updateGeolocation(@RequestBody CallDetailsDto callDetailsDto) {
        int result = jobService.updateGeolocation(callDetailsDto);
        return ResponseEntity.status(HttpStatus.OK).body(result);
    }

    @GetMapping("/join/session-end/{accessToken}")
    public String sessionEnd(Model model, HttpServletRequest request, @PathVariable String accessToken) {
        request.getSession().removeAttribute(AppConstant.FACING_MODE);
        model.addAttribute("id", accessToken);
        return AppURL.CLIENT_SESSION_END_PAGE;
    }

    @GetMapping("/join/session-end")
    public String sessionEnd(Model model, HttpServletRequest request) {
        request.getSession().removeAttribute(AppConstant.FACING_MODE);
        return AppURL.CLIENT_SESSION_END_PAGE;
    }

    @GetMapping(value = "/rejoin/{id}")
    @Cacheable(value = "urls", key = "#shortUrl", sync = true)
    public ResponseEntity<Void> getRejoin(@PathVariable String id) {
        CallDetailsDto callDetails = jobService.getCallDetails(id);
        String url = callDetails.getLink();
        return ResponseEntity.status(HttpStatus.FOUND)
                .location(URI.create(url))
                .build();
    }

    @PostMapping("/upload")
    public ResponseEntity<ImageData> imageUpload(@RequestBody ImageData imageData) {
        UserDto user = new UserDto();
        user.setCompanyId(1L);
        ImageData saveImageData = imageService.saveImageData(imageData, user);
        saveImageData.setUploadStatus("Success");
        return ResponseEntity.status(HttpStatus.OK).body(saveImageData);
    }


}
