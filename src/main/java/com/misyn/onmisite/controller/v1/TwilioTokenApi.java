package com.misyn.onmisite.controller.v1;

import com.misyn.onmisite.dto.TwilioCallbackDto;
import com.misyn.onmisite.dto.TwilioTokenDto;
import com.misyn.onmisite.service.TwilioService;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping("/api/v1/twilio")
public class TwilioTokenApi {


    private final TwilioService twilioService;
    @Value("${application.twilio.key}")
    private String apiKeySid;
    @Value("${application.twilio.secret}")
    private String apiKeySecret;

    public TwilioTokenApi(TwilioService twilioService) {
        this.twilioService = twilioService;
    }


    @PostMapping("/client/token")
    public ResponseEntity<TwilioTokenDto> tokenByClient(@RequestBody TwilioTokenDto twilioTokenRequest) {
        return ResponseEntity.ok().body(this.twilioService.getTwilioTokenByClient(twilioTokenRequest));
    }

    @PostMapping("/admin/token")
    public ResponseEntity<TwilioTokenDto> tokenByAdmin(@RequestBody TwilioTokenDto twilioTokenRequest) {
        return ResponseEntity.ok().body(this.twilioService.getTwilioTokenByAdmin(twilioTokenRequest));
    }

    @PostMapping(value = "/webhook")
    public String receiveWebRtc(HttpServletRequest request, HttpServletResponse response) {

        Integer seq = Integer.parseInt(request.getParameter("SequenceNumber") == null ? "-1" : request.getParameter("SequenceNumber"));
        TwilioCallbackDto twilioCallbackDto = TwilioCallbackDto.builder()
                .roomStatus(request.getParameter("RoomStatus"))
                .roomType(request.getParameter("RoomType"))
                .roomSid(request.getParameter("RoomSid"))
                .roomName(request.getParameter("RoomName"))
                .participantDuration(request.getParameter("ParticipantDuration"))
                .participantStatus(request.getParameter("ParticipantStatus"))
                .participantIdentity(request.getParameter("ParticipantIdentity"))
                .sequenceNumber(seq)
                .statusCallbackEvent(request.getParameter("StatusCallbackEvent"))
                .sourceSid(request.getParameter("SourceSid"))
                .timestamp(request.getParameter("Timestamp"))
                .participantSid(request.getParameter("ParticipantSid"))
                .accountSid(request.getParameter("AccountSid")).build();

        return twilioService.saveTwilioCallbackDetail(twilioCallbackDto).getRoomSid();
    }


}
