package com.misyn.onmisite.controller.v1;

import com.misyn.onmisite.dto.*;
import com.misyn.onmisite.dto.input.InputJobDetailDto;
import com.misyn.onmisite.enums.ApiResponseStatus;
import com.misyn.onmisite.service.JobService;
import com.misyn.onmisite.util.AppConstant;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;

@RequiredArgsConstructor
@Slf4j
@RestController
@RequestMapping("/api/v1/jobs")
public class JobApi {
    private final JobService jobService;

    @GetMapping("/messages")
    public String getMessages() {
        return "Hello World";
    }

    @PostMapping("/save")
    public ResponseEntity<ApiResponseDto<JobDetailResponseDto>> save(@RequestBody InputJobDetailDto inputJobDetailDto) {
        JobDetailDto savedJobDetailDto = jobService.saveJobDetail(inputJobDetailDto, 1L, true);
        if (savedJobDetailDto != null && savedJobDetailDto.getCallAccessToken() != null) {

            ApiResponseDto<JobDetailResponseDto> responseDto = ApiResponseDto.<JobDetailResponseDto>builder()
                    .status(ApiResponseStatus.SUCCESS)
                    .results(new JobDetailResponseDto(savedJobDetailDto.getCallAccessToken(), savedJobDetailDto.getJobId(), "Saved successfully"))
                    .build();
            return new ResponseEntity<>(responseDto, HttpStatus.OK);
        } else {
            ApiResponseDto<JobDetailResponseDto> responseDto = ApiResponseDto.<JobDetailResponseDto>builder()
                    .status(ApiResponseStatus.FAILED)
                    .results(new JobDetailResponseDto("N/A", 0L, "Failed to save"))
                    .build();
            return new ResponseEntity<>(responseDto, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @PostMapping("/saveTyreDetail")
    public ResponseEntity<String> saveTyreData(@RequestBody TyreConditionDataMapDto data) {
        try {
            jobService.saveTyreConditionDetail(data);
            return new ResponseEntity<>(AppConstant.EMPTY_STRING, HttpStatus.OK);
        }catch (Exception e){
            log.error(e.getMessage());
            return new ResponseEntity<>(AppConstant.EMPTY_STRING, HttpStatus.INTERNAL_SERVER_ERROR);
        }

    }

    @PostMapping("/saveInspectionDetail")
    public ResponseEntity<String> saveInspectionData(@RequestBody ClaimInspectionInfoMainMeDTO data) {
        try {
            jobService.saveInspectionDetail(data);
            return new ResponseEntity<>(AppConstant.EMPTY_STRING, HttpStatus.OK);
        }catch (Exception e){
            log.error(e.getMessage());
            return new ResponseEntity<>(AppConstant.EMPTY_STRING, HttpStatus.INTERNAL_SERVER_ERROR);
        }

    }
    @PostMapping("/calculateOnsiteInspectionValues")
    public ResponseEntity<BigDecimal> calculateOnsiteInspectionValues(@RequestBody FeeCalculationRequestDto data) {
        String calType = data.getCalType();
        try {
            BigDecimal totalFee;
            if ("ACR".equals(calType)) {
                String costPart = getCalculableAmount(data.getCostPart());
                String costLabour = getCalculableAmount(data.getCostLabour());
                totalFee = new BigDecimal(costPart).add(new BigDecimal(costLabour));
            } else if ("BaldTyrePenaltyAmount".equals(calType)) {
                String acr = getCalculableAmount(data.getAcr());
                String boldPercent = getCalculableAmount(data.getBoldPercent());
                totalFee = new BigDecimal(acr).multiply(new BigDecimal(boldPercent)).divide(new BigDecimal(100)).setScale(0, RoundingMode.HALF_UP);
            } else if ("UnderPenaltyAmount".equals(calType)) {
                String acr = getCalculableAmount(data.getAcr());
                String underPenaltyPercent = getCalculableAmount(data.getUnderPenaltyPercent());
                totalFee = new BigDecimal(acr).multiply(new BigDecimal(underPenaltyPercent)).divide(new BigDecimal(100)).setScale(0, RoundingMode.HALF_UP);
            } else if ("PayableAmount".equals(calType)) {
                String acr = getCalculableAmount(data.getAcr());
                String excess = getCalculableAmount(data.getExcess());
                String underPenaltyAmount = getCalculableAmount(data.getUnderPenaltyAmount());
                String boldTirePenaltyAmount = getCalculableAmount(data.getBoldTirePenaltyAmount());
                totalFee = new BigDecimal(acr).subtract((new BigDecimal(excess)).add(new BigDecimal(underPenaltyAmount).add(new BigDecimal(boldTirePenaltyAmount))));
            } else {
                totalFee = BigDecimal.ZERO;
            }

                BigDecimal scaled = totalFee.setScale(2, RoundingMode.HALF_UP);
                return new ResponseEntity<>(scaled, HttpStatus.OK);


        } catch (Exception ex) {
            throw ex;
        }
    }
    private String getCalculableAmount(String parameter) {
        if (null != parameter && !parameter.isEmpty()) {
            String value = parameter.trim();
            return value.replaceAll(",", "");
        } else {
            return "0";
        }
    }

}
