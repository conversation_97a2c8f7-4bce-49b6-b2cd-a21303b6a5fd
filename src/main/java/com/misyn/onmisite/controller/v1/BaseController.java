package com.misyn.onmisite.controller.v1;

import com.misyn.onmisite.dto.UserDto;
import com.misyn.onmisite.util.Utility;
import org.springframework.security.oauth2.core.oidc.user.OidcUser;

import java.util.Objects;

public abstract class BaseController {
    protected UserDto getUserDto(OidcUser user) {
        UserDto userDto = new UserDto();
        if(Objects.isNull(user.getClaim("username"))){
            userDto.setUsername(Utility.getUsername(user.getPreferredUsername()));
        }else {
            userDto.setUsername(Utility.getUsername(user.getClaim("username")));
        }
        userDto.setPreferredUsername(user.getPreferredUsername());
        userDto.setEmail(user.getEmail());
        return userDto;
    }
}
