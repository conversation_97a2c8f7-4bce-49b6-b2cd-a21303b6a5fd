package com.misyn.onmisite.controller.v1.errors;


import com.misyn.onmisite.exception.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.validation.FieldError;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.context.request.NativeWebRequest;
import org.springframework.web.servlet.ModelAndView;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.ConstraintViolation;
import jakarta.validation.ConstraintViolationException;
import org.springframework.web.servlet.resource.NoResourceFoundException;

import java.util.Set;

@Slf4j
@ControllerAdvice
public class ErrorTranslator {


    @ExceptionHandler(NoResourceFoundException.class)
    public ModelAndView handleNoResourceFoundException(HttpServletRequest request, Exception e) {
        log.error(e.getMessage());
        ModelAndView mav = new ModelAndView("error/default");
        mav.addObject("exception", e.getMessage());
        return mav;
    }

    @ExceptionHandler(RecordNotFoundException.class)
    public ModelAndView handleError404(HttpServletRequest request, Exception e) {
        log.error(e.getMessage(), e);
        ModelAndView mav = new ModelAndView("error/404");
        mav.addObject("exception", e.getMessage());
        return mav;
    }

    @ExceptionHandler(ClientAuthException.class)
    public ModelAndView handleErrorClient(HttpServletRequest request, Exception e) {
        log.error(e.getMessage(), e);
        ModelAndView mav = new ModelAndView("error/client-error");
        mav.addObject("exception", e.getMessage());
        return mav;
    }

    @ExceptionHandler(PasswordGenerateException.class)
    public ModelAndView handleErrorClientPasswordGenerate(HttpServletRequest request, Exception e) {
        log.error(e.getMessage(), e);
        ModelAndView mav = new ModelAndView("error/client-error");
        mav.addObject("exception", e.getMessage());
        return mav;
    }


    @ExceptionHandler(HttpRequestMethodNotSupportedException.class)
    public ModelAndView handleError405(HttpServletRequest request, Exception e) {
        log.error(e.getMessage(), e);
        ModelAndView mav = new ModelAndView("error/405");
        mav.addObject("exception", e.getMessage());
        return mav;
    }

    @ExceptionHandler(Exception.class)
    public ModelAndView handleError500(HttpServletRequest request, Exception e) {
        log.error(e.getMessage(), e);
        ModelAndView mav = new ModelAndView("error/default");
        mav.addObject("exception", e.getMessage());
        return mav;
    }

    @ExceptionHandler(AccessDeniedException.class)
    public ModelAndView handleError403(HttpServletRequest request, Exception e) {
        log.error(e.getMessage(), e);
        ModelAndView mav = new ModelAndView("error/default");
        mav.addObject("exception", e.getMessage());
        return mav;
    }

    @ExceptionHandler(JdbcException.class)
    public ModelAndView handleErrorJdbc(HttpServletRequest request, Exception e) {
        log.error(e.getMessage(), e);
        ModelAndView mav = new ModelAndView("error/default");
        mav.addObject("exception", e.getMessage());
        return mav;
    }

    @ExceptionHandler(BadRequestException.class)
    public ModelAndView handleError400(HttpServletRequest request, Exception e) {
        log.error(e.getMessage(), e);
        ModelAndView mav = new ModelAndView("error/400");
        mav.addObject("exception", e.getMessage());
        return mav;
    }

    @ExceptionHandler(InvalidFileTypeException.class)
    public ModelAndView handleErrorInvalidFileType(HttpServletRequest request, Exception e) {
        log.error(e.getMessage(), e);
        ModelAndView mav = new ModelAndView("error/default");
        mav.addObject("exception", e.getMessage());
        return mav;
    }

    @ExceptionHandler(RequiredValueException.class)
    public ModelAndView handleErrorRequiredValue(HttpServletRequest request, Exception e) {
        log.error(e.getMessage(), e);
        ModelAndView mav = new ModelAndView("error/default");
        mav.addObject("exception", e.getMessage());
        return mav;
    }


    @ExceptionHandler
    public ResponseEntity<ErrorDto> handleBadRequestAlertException(MethodArgumentNotValidException e, NativeWebRequest request) {
        log.error(e.getMessage(), e);
        ValidationErrorDto errorDto = new ValidationErrorDto(HttpStatus.BAD_REQUEST.value(), "Validation failure");
        for (FieldError error : e.getBindingResult().getFieldErrors()) {
            InvalidField invalidField = new InvalidField();
            invalidField.setField(error.getField());
            invalidField.setErrorMessage(error.getDefaultMessage());
            errorDto.getInvalidFields().add(invalidField);
        }
        return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(errorDto);
    }

    @ExceptionHandler(ConstraintViolationException.class)
    public ResponseEntity<ErrorDto> handle(ConstraintViolationException e) {
        log.error(e.getMessage(), e);
        ValidationErrorDto errorDto = new ValidationErrorDto(HttpStatus.BAD_REQUEST.value(), "Validation failure");
        Set<ConstraintViolation<?>> violations = e.getConstraintViolations();

        if (!violations.isEmpty()) {
            violations.forEach(violation -> {
                InvalidField invalidField = new InvalidField();
                invalidField.setField(violation.getPropertyPath().toString());
                invalidField.setErrorMessage(violation.getMessage());
                errorDto.getInvalidFields().add(invalidField);
            });
        } else {
            InvalidField invalidField = new InvalidField();
            invalidField.setErrorMessage("ConstraintViolationException occurred.");
        }
        return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(errorDto);
    }
}
