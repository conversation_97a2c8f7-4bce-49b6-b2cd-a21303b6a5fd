package com.misyn.onmisite.util;

import com.misyn.onmisite.exception.PasswordGenerateException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.springframework.security.crypto.encrypt.Encryptors;
import org.springframework.security.crypto.encrypt.TextEncryptor;

import java.io.File;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.text.*;
import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Calendar;
import java.util.Date;
import java.util.Objects;

@Slf4j
public class Utility {
    private final static String ENC_KY = "onmisitemisynergy";
    private final static String ENC_SLT = "09324a16c78ffc95";

    public static long getDirectorySize(File dir) {
        return FileUtils.sizeOfDirectory(dir);
    }

    public static long getDirectorySize(String path) {
        Path folder = Paths.get(path);
        long size = 0;
        try {
            size = Files.walk(folder)
                    .filter(p -> p.toFile().isFile())
                    .mapToLong(p -> p.toFile().length())
                    .sum();
        } catch (IOException e) {
            log.error(e.getMessage(), e);
        }
        return size;
    }


    public static boolean isValidDate(String dateStr, String dateFormat) {
        DateFormat sdf = new SimpleDateFormat(dateFormat);
        sdf.setLenient(false);
        try {
            sdf.parse(dateStr);
        } catch (ParseException e) {
            return false;
        }
        return true;
    }

    public static Date addDay(Date date, int addNoOfDay) {
        Date d = null;
        DateFormat dateFormat = new SimpleDateFormat(AppConstant.DATE_FORMAT);
        try {
            dateFormat.format(date);
            Calendar c = Calendar.getInstance();
            c.setTime(date);
            c.add(Calendar.DATE, addNoOfDay);
            d = c.getTime();
        } catch (Exception e) {
            log.error(e.getMessage());
        }
        return d;
    }

    public static Date getDate(String date, String format) {
        Date d1 = null;
        try {
            DateFormat df = new SimpleDateFormat(format);
            d1 = df.parse(date);
        } catch (Exception e) {
            log.error(e.getMessage());
        }
        return d1;
    }

    public static String getDate(Date date, String format) {
        if (date == null) return null;
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat(format);
        return simpleDateFormat.format(date);
    }

    public static Date addMonth(Date date, int addNoOfMonth) {
        Date d = null;
        DateFormat dateFormat = new SimpleDateFormat(AppConstant.DATE_FORMAT);
        try {
            dateFormat.format(date);
            Calendar c = Calendar.getInstance();
            c.setTime(date);
            c.add(Calendar.MONTH, addNoOfMonth);
            d = c.getTime();
        } catch (Exception e) {
            log.error(e.getMessage());
        }
        return d;
    }

    public static Date subDay(Date date, int subNoOfDay) {
        return addDay(date, -subNoOfDay);
    }

    public static BigDecimal getPercentage(Long total, Long part) {
        BigDecimal percentage = BigDecimal.ZERO;
        try {
            if (part > 0) {
                percentage = (new BigDecimal(part).divide(new BigDecimal(total), 4, RoundingMode.HALF_UP)).multiply(new BigDecimal(100)).setScale(2, RoundingMode.HALF_UP);
            }
        } catch (Exception e) {
            return percentage;
        }
        return percentage;
    }


    public static String getCustomDateFormat(String currentDate, String currentFormat, String newFormat) {
        DateFormat formatter = new SimpleDateFormat(currentFormat);
        SimpleDateFormat parse = new SimpleDateFormat(newFormat);
        Date date;
        try {
            date = formatter.parse(currentDate);
            return parse.format(date);
        } catch (ParseException e) {
            log.error(e.getMessage());
        }
        return null;
    }

    public static String getCustomDateFormat(Date currentDate, String newFormat) {
        SimpleDateFormat parse = new SimpleDateFormat(newFormat);
        try {
            return parse.format(currentDate);
        } catch (Exception e) {
            log.error(e.getMessage());
        }
        return null;
    }

    public static String getCustomDateFormat(String date, String format) {
        SimpleDateFormat dateFormat = new SimpleDateFormat(format);
        String _date = "";
        try {
            _date = dateFormat.format(dateFormat.parse(date));
        } catch (Exception e) {
            log.error(e.getMessage());
        }
        return _date;
    }

    public static long getDiffDays(Date maxDate, Date minDate) {
        long diff = maxDate.getTime() - minDate.getTime();
        long d = diff / (24 * 60 * 60 * 1000);
        return d;
    }

    public static long getDiffMinute(LocalDateTime maxDate, LocalDateTime minDate) {
        return Duration.between(minDate, maxDate).toMinutes();
    }


    public static String getOrdinal(int number) {
        String[] sufixes = new String[]{"th", "st", "nd", "rd", "th", "th", "th", "th", "th", "th"};
        return switch (number % 100) {
            case 11, 12, 13 -> number + "th";
            default -> number + sufixes[number % 10];
        };
    }


    public static String humanReadableByteCountBin(long bytes) {
        long absB = bytes == Long.MIN_VALUE ? Long.MAX_VALUE : Math.abs(bytes);
        if (absB < 1024) {
            return bytes + " B";
        }
        long value = absB;
        CharacterIterator ci = new StringCharacterIterator("KMGTPE");
        for (int i = 40; i >= 0 && absB > 0xfffccccccccccccL >> i; i -= 10) {
            value >>= 10;
            ci.next();
        }
        value *= Long.signum(bytes);
        return String.format("%.1f %ciB", value / 1024.0, ci.current());
    }


    public static double getDistance(double lat1, double lon1, double lat2, double lon2) {
        final double R = 6371e3;
        double φ1 = lat1 * Math.PI / 180;
        double φ2 = lat2 * Math.PI / 180;

        double Δφ = (lat2 - lat1) * Math.PI / 180;
        double Δλ = (lon2 - lon1) * Math.PI / 180;

        double a = Math.sin(Δφ / 2) * Math.sin(Δφ / 2) +
                Math.cos(φ1) * Math.cos(φ2) *
                        Math.sin(Δλ / 2) * Math.sin(Δλ / 2);
        double c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
        double d = R * c;

        return d;
    }

    public static String formatDate(LocalDate date) {
        if (date == null) {
            return "";
        }
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("MM/dd/yyyy");
        return date.format(formatter);
    }
    public static String decrypt(String str, String key) throws PasswordGenerateException {
        String decrypt;
        try {
            TextEncryptor text = Encryptors.text(key, ENC_SLT);
            decrypt = text.decrypt(str);
        } catch (Exception e) {
            log.error(e.getMessage());
            throw new PasswordGenerateException(e);
        }
        return decrypt;
    }

    public static String encrypt(String str, String key) throws PasswordGenerateException {
        String encrypt;
        try {
            TextEncryptor text = Encryptors.text(key, ENC_SLT);
            encrypt = text.encrypt(str);

        } catch (Exception e) {
            log.error(e.getMessage());
            throw new PasswordGenerateException(e);
        }
        return encrypt;
    }

    public static String getUsername(String email) {
        if (Objects.isNull(email)) {
            return "N/A";
        }

        int i = email.lastIndexOf("@");
        if (i > 0) {
            return email.substring(0, i).trim();
        }
        return email.trim();

    }
}
