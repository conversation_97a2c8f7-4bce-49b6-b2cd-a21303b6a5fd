package com.misyn.onmisite.util;

import com.misyn.onmisite.dto.UrlLongRequest;
import com.misyn.onmisite.entity.UrlDetail;
import com.misyn.onmisite.exception.PasswordGenerateException;
import com.misyn.onmisite.exception.RecordNotFoundException;
import com.misyn.onmisite.repository.UrlRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import java.util.Date;

@RequiredArgsConstructor
@Component
public class UrlServiceUtil {
    private static final String ENC_KY = "hnbmisynergy";
    private final UrlRepository urlRepository;

    public String convertToShortUrl(UrlLongRequest request) {
        UrlDetail url = new UrlDetail();
        url.setLongUrl(request.getLongUrl());
        url.setExpiresDate(request.getExpiresDate());
        url.setCreatedDate(new Date());
        UrlDetail entity = urlRepository.save(url);

        return Utility.encrypt(String.valueOf(entity.getId()), ENC_KY);
    }

    public String getOriginalUrl(String shortUrl) {
        String decrypt;
        try {
            decrypt = Utility.decrypt(shortUrl, ENC_KY);
        } catch (Exception e) {
            throw new PasswordGenerateException("Invalid Hash value");
        }
        long id = Long.parseLong(decrypt);
        UrlDetail entity = urlRepository.findById(id)
                .orElseThrow(() -> new RecordNotFoundException("There is no entity with " + shortUrl));

        if (entity.getExpiresDate() != null && entity.getExpiresDate().before(new Date())) {
            urlRepository.delete(entity);
            throw new RecordNotFoundException("Link expired!");
        }
        return entity.getLongUrl();
    }


}
