package com.misyn.onmisite.mappers;

import com.misyn.onmisite.dto.UploadImageDetailsDto;
import com.misyn.onmisite.dto.UploadVideoDetailsDto;
import com.misyn.onmisite.entity.CallDetails;
import com.misyn.onmisite.entity.Company;
import com.misyn.onmisite.entity.JobDetail;
import com.misyn.onmisite.entity.UploadVideoDetails;
import lombok.RequiredArgsConstructor;
import org.modelmapper.ModelMapper;
import org.springframework.stereotype.Component;

@RequiredArgsConstructor
@Component
public class UploadVideoDetailMapper extends ToMapper<UploadVideoDetailsDto, UploadVideoDetails> {
    private final ModelMapper modelMapper;

    @Override
    public UploadVideoDetailsDto toDto(UploadVideoDetails entity) {
        UploadVideoDetailsDto dto = modelMapper.map(entity, UploadVideoDetailsDto.class);
        dto.setJobId(entity.getJobDetail().getJobId());
        dto.setCallId(entity.getCallDetails().getCallId());
        dto.setCompanyId(entity.getCompany().getCompanyId());
        return dto;
    }

    @Override
    public UploadVideoDetails toEntity(UploadVideoDetailsDto dto) {
        UploadVideoDetails uploadVideoDetails = modelMapper.map(dto, UploadVideoDetails.class);
        JobDetail jobDetail = new JobDetail();
        jobDetail.setJobId(dto.getJobId());
        uploadVideoDetails.setJobDetail(jobDetail);

        CallDetails callDetails = new CallDetails();
        callDetails.setCallId(dto.getCallId());
        uploadVideoDetails.setCallDetails(callDetails);

        Company company = new Company();
        company.setCompanyId(dto.getCompanyId());
        uploadVideoDetails.setCompany(company);
        return uploadVideoDetails;
    }
}
