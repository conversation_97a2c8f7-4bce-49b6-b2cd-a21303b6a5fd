package com.misyn.onmisite.mappers;

import com.misyn.onmisite.dto.CallDetailsDto;
import com.misyn.onmisite.entity.CallDetails;
import com.misyn.onmisite.entity.JobDetail;
import lombok.RequiredArgsConstructor;
import org.modelmapper.ModelMapper;
import org.springframework.stereotype.Component;

@RequiredArgsConstructor
@Component
public class CallDetailsMapper extends ToMapper<CallDetailsDto, CallDetails> {
    private final ModelMapper modelMapper;

    @Override
    public CallDetailsDto toDto(CallDetails entity) {
        CallDetailsDto callDetailsDto = modelMapper.map(entity, CallDetailsDto.class);
        callDetailsDto.setJobId(entity.getJobDetail().getJobId());
        return callDetailsDto;
    }

    @Override
    public CallDetails toEntity(CallDetailsDto dto) {
        CallDetails callDetails = modelMapper.map(dto, CallDetails.class);
        JobDetail jobDetail = new JobDetail();
        jobDetail.setJobId(dto.getJobId());
        callDetails.setJobDetail(jobDetail);
        return callDetails;
    }
}
