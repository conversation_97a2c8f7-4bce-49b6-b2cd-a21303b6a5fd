package com.misyn.onmisite.mappers;

import com.misyn.onmisite.dto.UploadImageDetailsDto;
import com.misyn.onmisite.entity.CallDetails;
import com.misyn.onmisite.entity.Company;
import com.misyn.onmisite.entity.JobDetail;
import com.misyn.onmisite.entity.UploadImageDetails;
import lombok.RequiredArgsConstructor;
import org.modelmapper.ModelMapper;
import org.springframework.stereotype.Component;

@RequiredArgsConstructor
@Component
public class UploadImageDetailMapper extends ToMapper<UploadImageDetailsDto, UploadImageDetails> {
    private final ModelMapper modelMapper;


    @Override
    public UploadImageDetailsDto toDto(UploadImageDetails entity) {
        UploadImageDetailsDto dto = modelMapper.map(entity, UploadImageDetailsDto.class);
        dto.setJobId(entity.getJobDetail().getJobId());
        dto.setCallId(entity.getCallDetails().getCallId());
        dto.setCompanyId(entity.getCompany().getCompanyId());
        return dto;
    }

    @Override
    public UploadImageDetails toEntity(UploadImageDetailsDto dto) {
        UploadImageDetails uploadImageDetails = modelMapper.map(dto, UploadImageDetails.class);
        JobDetail jobDetail = new JobDetail();
        jobDetail.setJobId(dto.getJobId());
        uploadImageDetails.setJobDetail(jobDetail);

        CallDetails callDetails = new CallDetails();
        callDetails.setCallId(dto.getCallId());
        uploadImageDetails.setCallDetails(callDetails);

        Company company = new Company();
        company.setCompanyId(dto.getCompanyId());
        uploadImageDetails.setCompany(company);
        return uploadImageDetails;
    }
}


