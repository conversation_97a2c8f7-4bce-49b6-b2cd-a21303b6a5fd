package com.misyn.onmisite.mappers;

import com.misyn.onmisite.dto.JobDetailDto;
import com.misyn.onmisite.entity.Company;
import com.misyn.onmisite.entity.JobDetail;
import lombok.RequiredArgsConstructor;
import org.modelmapper.ModelMapper;
import org.springframework.stereotype.Component;

@RequiredArgsConstructor
@Component
public class JobDetailMapper extends ToMapper<JobDetailDto, JobDetail> {
    private final ModelMapper modelMapper;

    @Override
    public JobDetailDto toDto(JobDetail entity) {
        JobDetailDto dto = modelMapper.map(entity, JobDetailDto.class);
        dto.setCompanyId(entity.getCompany().getCompanyId());
        return dto;
    }

    @Override
    public JobDetail toEntity(JobDetailDto dto) {
        JobDetail jobDetail = modelMapper.map(dto, JobDetail.class);
        Company company = new Company();
        company.setCompanyId(dto.getCompanyId());
        jobDetail.setCompany(company);
        return jobDetail;
    }
}
