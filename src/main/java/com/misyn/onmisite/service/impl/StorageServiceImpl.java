package com.misyn.onmisite.service.impl;

import com.misyn.onmisite.dto.ListItem;
import com.misyn.onmisite.repository.DocumentTypeDetailRepository;
import com.misyn.onmisite.service.StorageService;
import com.oracle.bmc.objectstorage.ObjectStorage;
import com.oracle.bmc.objectstorage.requests.DeleteObjectRequest;
import com.oracle.bmc.objectstorage.requests.GetObjectRequest;
import com.oracle.bmc.objectstorage.requests.PutObjectRequest;
import com.oracle.bmc.objectstorage.responses.GetObjectResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.InputStream;
import java.util.List;


@RequiredArgsConstructor
@Service
public class StorageServiceImpl implements StorageService {

    private final ObjectStorage objectStorage;
    private final DocumentTypeDetailRepository documentTypeDetailRepository;
    @Value("${oci.bucket-name}")
    private String bucketName;


    @Override
    public void uploadFile(MultipartFile file, String objectName) {
        try {
            String namespace = objectStorage.getNamespace(
                    com.oracle.bmc.objectstorage.requests.GetNamespaceRequest.builder().build()
            ).getValue();
            PutObjectRequest request = PutObjectRequest.builder()
                    .namespaceName(namespace)
                    .bucketName(bucketName)
                    .objectName(objectName)
                    .putObjectBody(file.getInputStream())
                    .contentLength(file.getSize())
                    .build();
            objectStorage.putObject(request);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public void uploadFile(InputStream inputStream, String objectName, long contentLength) {
        try {
            String namespace = objectStorage.getNamespace(
                    com.oracle.bmc.objectstorage.requests.GetNamespaceRequest.builder().build()
            ).getValue();
            PutObjectRequest request = PutObjectRequest.builder()
                    .namespaceName(namespace)
                    .bucketName(bucketName)
                    .objectName(objectName)
                    .putObjectBody(inputStream)
                    .contentLength(contentLength)
                    .build();
            objectStorage.putObject(request);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public void uploadMp4File(InputStream inputStream, String objectName, long contentLength) {
        try {
            String namespace = objectStorage.getNamespace(
                    com.oracle.bmc.objectstorage.requests.GetNamespaceRequest.builder().build()
            ).getValue();
            PutObjectRequest request = PutObjectRequest.builder()
                    .namespaceName(namespace)
                    .bucketName(bucketName)
                    .objectName(objectName)
                    .putObjectBody(inputStream)
                    .contentLength(contentLength)
                    .contentType("video/mp4")
                    .build();
            objectStorage.putObject(request);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public void deleteFile(String objectName) {
        String namespace = objectStorage.getNamespace(
                com.oracle.bmc.objectstorage.requests.GetNamespaceRequest.builder().build()
        ).getValue();
        DeleteObjectRequest request = DeleteObjectRequest.builder()
                .namespaceName(namespace).bucketName(bucketName)
                .objectName(objectName).build();
        objectStorage.deleteObject(request);
    }


    @Override
    public InputStream downloadFile(String objectName) {
        String namespace = objectStorage.getNamespace(
                com.oracle.bmc.objectstorage.requests.GetNamespaceRequest.builder().build()
        ).getValue();
        GetObjectRequest request = GetObjectRequest.builder()
                .namespaceName(namespace)
                .bucketName(bucketName)
                .objectName(objectName)
                .build();

        GetObjectResponse response = objectStorage.getObject(request);
        return response.getInputStream();
    }

    @Override
    public List<ListItem> getDocumentTypes() {
        return documentTypeDetailRepository.findAllDocumentTypes().stream()
                .map(documentTypeDetail ->
                        ListItem.builder().label(documentTypeDetail.getDocumentTypeName())
                                .value(Long.valueOf(documentTypeDetail.getDocumentTypeId())).build())
                .toList();
    }
}
