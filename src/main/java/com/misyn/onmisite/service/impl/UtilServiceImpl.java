package com.misyn.onmisite.service.impl;

import com.misyn.onmisite.entity.CallDetails;
import com.misyn.onmisite.exception.RecordNotFoundException;
import com.misyn.onmisite.repository.CallDetailsRepository;
import com.misyn.onmisite.service.UtilService;
import com.misyn.onmisite.util.AppConstant;
import com.misyn.onmisite.util.Utility;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;


@Component
public class UtilServiceImpl implements UtilService {
    private final CallDetailsRepository callDetailsRepository;

    public UtilServiceImpl(CallDetailsRepository callDetailsRepository) {
        this.callDetailsRepository = callDetailsRepository;
    }

    public BigDecimal getDistance(Long jobId, String latitude, String longitude) {
        if (latitude == null || longitude == null) {
            return BigDecimal.ZERO;
        }
        CallDetails callDetails = callDetailsRepository.getFirstByJobId(jobId).orElseThrow(() -> new RecordNotFoundException("Record Not Found"));
        double _latitude = Double.parseDouble(latitude);
        double _longitude = Double.parseDouble(longitude);
        double firstLatitude = Double.parseDouble(callDetails.getLatitude());
        double firstLongitude = Double.parseDouble(callDetails.getLongitude());
        return BigDecimal.valueOf(Utility.getDistance(firstLatitude, firstLongitude, _latitude, _longitude));
    }

    @Override
    public String getMapIcon(int index, double distance) {
        if (index == 1) {
            return AppConstant.MAP_ICON_GREEN;
        }
        if (distance > 100) {
            return AppConstant.MAP_ICON_ORANGE;
        }
        return AppConstant.MAP_ICON_BLUE;
    }

    @Override
    public String getMapIconTitle(int index, double distance) {
        if (index == 1) {
            return AppConstant.EMPTY_STRING;
        }
        if (distance > 999) {
            return "View Location [Diff : " + (distance / 1000) + " Km]";
        }
        return "View Location [Diff : " + (distance) + " m]";
    }

    @Override
    public String getMapViewUrl(String latitude, String longitude) {
        if (latitude == null || longitude.isEmpty()) {
            latitude = "0";
            longitude = "0";
        }
        return "/view-map/" + latitude + "/" + longitude;
    }
}
