package com.misyn.onmisite.service.impl;

import com.misyn.onmisite.dto.UploadVideoDetailsDto;
import com.misyn.onmisite.entity.UploadVideoDetails;
import com.misyn.onmisite.mappers.UploadVideoDetailMapper;
import com.misyn.onmisite.repository.CallDetailsRepository;
import com.misyn.onmisite.repository.UploadVideoDetailsRepository;
import com.misyn.onmisite.service.UtilService;
import com.misyn.onmisite.service.VideoService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

@RequiredArgsConstructor
@Slf4j
@Service
public class VideoServiceImpl implements VideoService {

    private static final String VIDEOS_DIRECTORY = "videos";
    private static final String VIDEO_EXTENSION = "mp4";
    private final CallDetailsRepository callDetailsRepository;
    private final UploadVideoDetailsRepository uploadVideoDetailsRepository;
    private final UtilService utilService;
    private final UploadVideoDetailMapper uploadVideoDetailMapper;
    @Value("${application.image.path}")
    private String rootLocation;


    @Override
    public UploadVideoDetailsDto getUploadVideoDetailsDto(Long uploadVideoDetailsRefNo) {
        return uploadVideoDetailMapper.toDto(uploadVideoDetailsRepository.findById(uploadVideoDetailsRefNo).orElseGet(UploadVideoDetails::new));
    }


}
