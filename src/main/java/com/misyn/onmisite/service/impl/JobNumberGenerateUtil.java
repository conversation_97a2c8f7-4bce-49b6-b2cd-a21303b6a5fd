package com.misyn.onmisite.service.impl;

import com.misyn.onmisite.entity.Company;
import com.misyn.onmisite.entity.JobNumberSequence;
import com.misyn.onmisite.repository.JobNumberSequenceRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

@Component()
@Scope()
public class JobNumberGenerateUtil {
    private static final Object LOCK = new Object();
    private JobNumberSequenceRepository jobNumberSequenceRepository;

    @Autowired
    public void setJobNumberSequenceRepository(JobNumberSequenceRepository jobNumberSequenceRepository) {
        this.jobNumberSequenceRepository = jobNumberSequenceRepository;
    }


    @Transactional
    public Long getNextJobNo(Long companyId) {
        synchronized (LOCK) {
            JobNumberSequence byCompanyCompanyId = jobNumberSequenceRepository.findByCompanyCompanyId(companyId);
            if (byCompanyCompanyId == null) {
                JobNumberSequence jobNumberSequence = new JobNumberSequence();
                Company company = new Company();
                company.setCompanyId(companyId);
                jobNumberSequence.setCompany(company);
                jobNumberSequence.setNextJobNumber(1L);
                jobNumberSequenceRepository.save(jobNumberSequence);
                return jobNumberSequence.getNextJobNumber();
            } else {
                jobNumberSequenceRepository.updateNextJobNumber(companyId);
                return byCompanyCompanyId.getNextJobNumber() + 1;
            }
        }

    }
}
