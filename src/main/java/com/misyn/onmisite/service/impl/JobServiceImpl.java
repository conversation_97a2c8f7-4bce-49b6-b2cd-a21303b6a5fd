package com.misyn.onmisite.service.impl;

import com.misyn.onmisite.dto.*;
import com.misyn.onmisite.dto.form.AdminVideoForm;
import com.misyn.onmisite.dto.input.InputJobDetailDto;
import com.misyn.onmisite.entity.*;
import com.misyn.onmisite.enums.JobStatus;
import com.misyn.onmisite.enums.RecordStatus;
import com.misyn.onmisite.exception.ClientAuthException;
import com.misyn.onmisite.exception.RecordNotFoundException;
import com.misyn.onmisite.mappers.CallDetailsMapper;
import com.misyn.onmisite.mappers.JobDetailMapper;
import com.misyn.onmisite.mappers.UploadImageDetailMapper;
import com.misyn.onmisite.mappers.UploadVideoDetailMapper;
import com.misyn.onmisite.repository.*;
import com.misyn.onmisite.service.JobService;
import com.misyn.onmisite.service.UtilService;
import com.misyn.onmisite.util.AppConstant;
import com.misyn.onmisite.util.UrlServiceUtil;
import com.misyn.onmisite.util.Utility;
import com.twilio.rest.microvisor.v1.App;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;
import java.util.zip.DataFormatException;

import static java.lang.Math.E;

@RequiredArgsConstructor
@Slf4j
@Service
public class JobServiceImpl implements JobService {

    private static final String MESSAGE = "Join with a onmisite. click here link: ";
    private final JobDetailRepository jobDetailRepository;
    private final CallDetailsRepository callDetailsRepository;
    private final SmsMessageDetailRepository smsMessageDetailRepository;
    private final UploadImageDetailsRepository uploadImageDetailsRepository;
    private final UploadVideoDetailsRepository uploadVideoDetailsRepository;
    private final DocumentTypeDetailRepository documentTypeDetailRepository;
    private final ClaimInspectionInfoMainMeRepository claimInspectionInfoMainMeRepository;
    private final TireDetailRepository tireDetailRepository;
    private final SpecialCommentRepository specialCommentRepository;
    private final UrlServiceUtil urlService;
    private final UtilService utilService;
    private final JobDetailMapper jobDetailMapper;
    private final CallDetailsMapper callDetailsMapper;
    private final UploadVideoDetailMapper uploadVideoDetailMapper;
    private final UploadImageDetailMapper uploadImageDetailMapper;

    @Value("${application.client.video-join-url}")
    private String videoJoinUrl;
    @Value("${application.client.call-token-expired-time}")
    private long callTokenExpiredTime;

    private static JobDetailDto getJobDetailDto(InputJobDetailDto inputJobDetailDto) {
        JobDetailDto jobDetailDto = new JobDetailDto();
        jobDetailDto.setJobNumber(inputJobDetailDto.claimNumber());
        jobDetailDto.setCompanyId(1L);
        jobDetailDto.setVehicleNo(inputJobDetailDto.vehicleNo());
        jobDetailDto.setPolicyNo(inputJobDetailDto.policyNo());
        jobDetailDto.setVehicleMake(inputJobDetailDto.vehicleMake());
        jobDetailDto.setVehicleModel(inputJobDetailDto.vehicleModel());
        jobDetailDto.setJobStatus(JobStatus.PENDING);
        jobDetailDto.setInsuredName(inputJobDetailDto.insuredName());
        jobDetailDto.setInsuredEmail(inputJobDetailDto.insuredEmail());
        jobDetailDto.setInsuredMobileNo(inputJobDetailDto.insuredMobileNo());
        jobDetailDto.setInsuredAddress1(inputJobDetailDto.insuredAddress1());
        jobDetailDto.setInsuredAddress2(inputJobDetailDto.insuredAddress2());
        jobDetailDto.setInsuredAddress3(inputJobDetailDto.insuredAddress3());
        return jobDetailDto;
    }

    private CallDetails getSaveCallDetails(JobDetailDto savedJobDetailDto, InputJobDetailDto inputJobDetailDto) {
        CallDetails callDetails = new CallDetails();
        JobDetail jobDetail = new JobDetail();
        jobDetail.setJobId(savedJobDetailDto.getJobId());
        callDetails.setJobDetail(jobDetail);
        callDetails.setAccessToken(UUID.randomUUID().toString());
        callDetails.setTokenGenerateDateTime(LocalDateTime.now());
        callDetails.setLink(getJoinLink(callDetails.getAccessToken()));
        callDetails.setAssignee(inputJobDetailDto.assignee());
        callDetails.setCallingMobileNo(inputJobDetailDto.callingMobileNo());
        callDetails.setInspectionTypeId(inputJobDetailDto.inspectionTypeId());
        callDetails.setInspectionJobNumber(inputJobDetailDto.inspectionJobNumber());
        return callDetails;
    }

    @Transactional
    @Override
    public JobDetailDto saveJobDetail(InputJobDetailDto inputJobDetailDto, Long companyId, boolean isJoinCall) {

        try {
            JobDetailDto jobDetailDto = getJobDetailDto(inputJobDetailDto);
            jobDetailDto.setCompanyId(companyId);
            jobDetailRepository.findByJobNumber(inputJobDetailDto.claimNumber())
                    .ifPresentOrElse(jobDetail -> jobDetailDto.setJobId(jobDetail.getJobId()),
                            () -> jobDetailDto.setJobId(null));

            JobDetailDto savedJobDetailDto = jobDetailMapper.toDto(jobDetailRepository.save(jobDetailMapper.toEntity(jobDetailDto)));
            if (savedJobDetailDto != null && isJoinCall) {
                CallDetails saveCallDetails = getSaveCallDetails(savedJobDetailDto, inputJobDetailDto);

                callDetailsRepository.findByJobDetailJobIdAndInspectionJobNumber(savedJobDetailDto.getJobId(), inputJobDetailDto.inspectionJobNumber())
                        .ifPresentOrElse(callDetails -> saveCallDetails.setCallId(callDetails.getCallId()),
                                () -> saveCallDetails.setCallId(null));

                CallDetails savedCallDetails = callDetailsRepository.save(saveCallDetails);
                savedJobDetailDto.setCallAccessToken(savedCallDetails.getAccessToken());
                smsMessageDetailRepository.save(this.getSendSmsMessageDetail(inputJobDetailDto.callingMobileNo(), savedCallDetails.getLink()));
            }
            return savedJobDetailDto;
        } catch (RecordNotFoundException e) {
            log.error(e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw e;
        }
    }


    @Override
    public JobDetailDto saveAndJoinExistingCall(Long jobId, String userName, Long companyId, String accessToken) {
        try {
            JobDetail jobDetail = jobDetailRepository
                    .findByJobIdAndCompanyCompanyId(jobId, companyId)
                    .orElseThrow(() -> new RecordNotFoundException("Record not found"));

            JobDetailDto jobDetailDto = jobDetailMapper.toDto(jobDetail);
            CallDetails callDetails = callDetailsRepository
                    .findByAccessToken(accessToken)
                    .orElseThrow(() -> new RecordNotFoundException("Record Not Found"));
            if (callDetails != null) {
                jobDetailDto.setCallAccessToken(callDetails.getAccessToken());
            }
            return jobDetailDto;
        } catch (RecordNotFoundException e) {
            log.error(e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw e;
        }
    }


    @Override
    public AdminVideoForm getAgentVideoForm(String accessToken, String createdBy) throws RecordNotFoundException {
        AdminVideoForm agentVideoForm;
        try {
            CallDetails callDetails = callDetailsRepository
                    .findByAccessTokenAndCreatedBy(accessToken, createdBy).orElseThrow(() -> new RecordNotFoundException("Record Not Found"));
            JobDetail jobDetail = callDetails.getJobDetail();
            if (jobDetail == null) {
                throw new RecordNotFoundException("Record Is Unavailable");
            }
            agentVideoForm = new AdminVideoForm();
            agentVideoForm.setJobDetailDto(jobDetailMapper.toDto(jobDetail));
            agentVideoForm.setCallDetailsDto(callDetailsMapper.toDto(callDetails));
            List<CallDetails> callList = callDetailsRepository.findAllByJobDetailJobIdOrderByCallId(jobDetail.getJobId());
            int index = 1;
            for (CallDetails details : callList) {
                CallUploadImageVideoDetailDto callUploadImageVideoDetailDto = new CallUploadImageVideoDetailDto();
                callUploadImageVideoDetailDto.setCallIndex(index++);
                callUploadImageVideoDetailDto.setTitle(Utility.getOrdinal(callUploadImageVideoDetailDto.getCallIndex()));
                callUploadImageVideoDetailDto.setCallDetailsDto(callDetailsMapper.toDto(details));

                if (!callDetails.getCallId().equals(details.getCallId())) {
                    CallUploadImageVideoDetailDto dto = this.getCallUploadImageVideoDetailDto(details.getCallId());
                    dto.setCallIndex(callUploadImageVideoDetailDto.getCallIndex());
                    dto.setTitle(callUploadImageVideoDetailDto.getTitle());
                    dto.setCallDetailsDto(callUploadImageVideoDetailDto.getCallDetailsDto());
                    agentVideoForm.getPrevCallImageVideoList().add(dto);
                    this.setCallUploadVideoDetail(callUploadImageVideoDetailDto);
                }

            }

            List<ListItem> documnetTypeList = documentTypeDetailRepository.findAllDocumentTypes().stream()
                    .map(documentTypeDetail ->
                            ListItem.builder().label(documentTypeDetail.getDocumentTypeName())
                                    .value(Long.valueOf(documentTypeDetail.getDocumentTypeId())).build())
                    .toList();
            agentVideoForm.setDocumnetTypeList(documnetTypeList);

        } catch (RecordNotFoundException e) {
            log.error(e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw e;
        }
        return agentVideoForm;
    }

    @Override
    public AdminVideoForm getAgentVideoForm(Long companyId, Long jobId) throws RecordNotFoundException {
        AdminVideoForm agentVideoForm;
        try {
            JobDetail jobDetail = jobDetailRepository.findByJobIdAndCompanyCompanyId(jobId, companyId).orElseThrow(() -> new RecordNotFoundException("Record Not Found"));
            agentVideoForm = new AdminVideoForm();
            agentVideoForm.setJobDetailDto(jobDetailMapper.toDto(jobDetail));
            List<CallDetails> callList = callDetailsRepository.findAllByJobDetailJobIdOrderByCallId(jobDetail.getJobId());
            int index = 1;
            for (CallDetails details : callList) {
                CallUploadImageVideoDetailDto dto = this.getCallUploadImageVideoDetailDto(details.getCallId());
                dto.setCallIndex(index++);
                dto.setTitle(Utility.getOrdinal(dto.getCallIndex()));
                dto.setCallDetailsDto(callDetailsMapper.toDto(details));
                agentVideoForm.getPrevCallImageVideoList().add(dto);
                this.setCallUploadVideoDetail(dto);
            }

        } catch (RecordNotFoundException e) {
            log.error(e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw e;
        }
        return agentVideoForm;
    }


    private void setCallUploadVideoDetail(CallUploadImageVideoDetailDto callUploadImageVideoDetailDto) {
        CallDetailsDto callDetailsDto = callUploadImageVideoDetailDto.getCallDetailsDto();
        List<UploadVideoDetails> uploadVideoDetailList = uploadVideoDetailsRepository.findAllByJobDetailJobIdAndCallDetailsCallIdOrderByUploadVideoDetailsRefNo(callDetailsDto.getJobId(), callDetailsDto.getCallId());
        for (UploadVideoDetails uploadVideoDetails : uploadVideoDetailList) {
            UploadVideoDetailsDto uploadVideoDetailsDto = uploadVideoDetailMapper.toDto(uploadVideoDetails);
            uploadVideoDetailsDto.setMapIcon(utilService.getMapIcon(callUploadImageVideoDetailDto.getCallIndex(), uploadVideoDetailsDto.getDistance().doubleValue()));
            uploadVideoDetailsDto.setMapTitle(utilService.getMapIconTitle(callUploadImageVideoDetailDto.getCallIndex(), uploadVideoDetailsDto.getDistance().doubleValue()));
            uploadVideoDetailsDto.setMapUrl(utilService.getMapViewUrl(uploadVideoDetailsDto.getLatitude(), uploadVideoDetailsDto.getLongitude()));
            uploadVideoDetailsDto.setLatitudeVal(uploadVideoDetails.getLatitude() == null || uploadVideoDetails.getLatitude().isEmpty() ? 0 : Double.parseDouble(uploadVideoDetails.getLatitude()));
            uploadVideoDetailsDto.setLongitudeVal(uploadVideoDetails.getLongitude() == null || uploadVideoDetails.getLongitude().isEmpty() ? 0 : Double.parseDouble(uploadVideoDetails.getLongitude()));

            callUploadImageVideoDetailDto.getUploadVideoList().add(uploadVideoDetailsDto);
        }
    }

    @Override
    public AdminVideoForm getClientVideoForm(String accessToken) throws ClientAuthException {
        AdminVideoForm agentVideoForm;

        try {
            CallDetails callDetails = callDetailsRepository
                    .findByAccessToken(accessToken).orElseThrow(() -> new ClientAuthException("Record Is Unavailable"));
            JobDetail jobDetail = callDetails.getJobDetail();
            if (jobDetail == null) {
                throw new ClientAuthException("Record Is Unavailable");
            }
            if (callDetails.getRecordStatus() == RecordStatus.EXPIRED) {
                throw new ClientAuthException("Sorry,your session has expired.");
            }

            long diffMinute = Utility.getDiffMinute(LocalDateTime.now(), callDetails.getTokenGenerateDateTime());
            if (diffMinute > callTokenExpiredTime) {
                throw new ClientAuthException("Sorry,your session has expired.");
            }

            agentVideoForm = new AdminVideoForm();
            agentVideoForm.setJobDetailDto(jobDetailMapper.toDto(jobDetail));
            agentVideoForm.setCallDetailsDto(callDetailsMapper.toDto(callDetails));

        } catch (ClientAuthException e) {
            log.error(e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw e;
        }
        return agentVideoForm;
    }

    @Transactional
    @Override
    public CallUploadImageVideoDetailDto getCallUploadImageVideoDetailDto(Long jobId, Long callId, int callIndex) {
        CallUploadImageVideoDetailDto callUploadImageVideoDetailDto = new CallUploadImageVideoDetailDto();
        callUploadImageVideoDetailDto.setCallIndex(callIndex);

        callDetailsRepository.findById(callId).ifPresent(callDetailsDto ->
                callUploadImageVideoDetailDto.setCallDetailsDto(callDetailsMapper.toDto(callDetailsDto)));
        List<UploadImageDetails> uploadImageDetailList = uploadImageDetailsRepository
                .findAllByJobDetailJobIdAndCallDetailsCallIdOrderByUploadImageDetailsRefNo(jobId, callId);

        return getCallUploadImageVideoDetailDto(callUploadImageVideoDetailDto, uploadImageDetailList);
    }

    private CallUploadImageVideoDetailDto getCallUploadImageVideoDetailDto(CallUploadImageVideoDetailDto callUploadImageVideoDetailDto, List<UploadImageDetails> uploadImageDetailList) {

        List<UploadImageDetailsDto> uploadImageDetailsDtoList = uploadImageDetailList.stream().map(uploadImageDetailMapper::toDto).toList();

        Map<Integer, List<UploadImageDetailsDto>> uploadImageDetailByDocumentType = uploadImageDetailsDtoList.stream().peek(uploadImageDetailsDto -> {
            uploadImageDetailsDto.setMapIcon(utilService.getMapIcon(callUploadImageVideoDetailDto.getCallIndex(), uploadImageDetailsDto.getDistance().doubleValue()));
            uploadImageDetailsDto.setMapTitle(utilService.getMapIconTitle(callUploadImageVideoDetailDto.getCallIndex(), uploadImageDetailsDto.getDistance().doubleValue()));
            uploadImageDetailsDto.setMapUrl(utilService.getMapViewUrl(uploadImageDetailsDto.getLatitude(), uploadImageDetailsDto.getLongitude()));
            uploadImageDetailsDto.setLatitudeVal(Double.parseDouble(uploadImageDetailsDto.getLatitude()));
            uploadImageDetailsDto.setLongitudeVal(Double.parseDouble(uploadImageDetailsDto.getLongitude()));
            callUploadImageVideoDetailDto.getUploadImageList().add(uploadImageDetailsDto);

        }).collect(Collectors.groupingBy(UploadImageDetailsDto::getDocumentTypeId));


        documentTypeDetailRepository.findAll(Sort.by("orderId")).forEach(documentTypeDetail -> {
            List<UploadImageDetailsDto> uploadImageDetailDto = uploadImageDetailByDocumentType.get(documentTypeDetail.getDocumentTypeId());
            if (uploadImageDetailDto != null) {
                UploadDocumentTypeDto uploadDocumentTypeDto = new UploadDocumentTypeDto();
                uploadDocumentTypeDto.setDocumentTypeId(documentTypeDetail.getDocumentTypeId());
                uploadDocumentTypeDto.setDocumentTypeName(documentTypeDetail.getDocumentTypeName());
                uploadDocumentTypeDto.setUploadImageList(uploadImageDetailDto);
                callUploadImageVideoDetailDto.getUploadDocumentTypeList().add(uploadDocumentTypeDto);
            }
        });

        return callUploadImageVideoDetailDto;
    }

    @Override
    public CallUploadImageVideoDetailDto getCallUploadImageVideoDetailDto(Long callId) {
        CallUploadImageVideoDetailDto callUploadImageVideoDetailDto = new CallUploadImageVideoDetailDto();
        callUploadImageVideoDetailDto.setCallIndex(0);
        callDetailsRepository.findById(callId).ifPresent(callDetailsDto -> callUploadImageVideoDetailDto.setCallDetailsDto(callDetailsMapper.toDto(callDetailsDto)));
        List<UploadImageDetails> uploadImageDetailList = uploadImageDetailsRepository
                .findAllByCallDetailsCallIdOrderByUploadImageDetailsRefNo(callId);
        callUploadImageVideoDetailDto.setTitle(Utility.getOrdinal(callUploadImageVideoDetailDto.getCallIndex()));
        return getCallUploadImageVideoDetailDto(callUploadImageVideoDetailDto, uploadImageDetailList);
    }

    @Transactional
    @Override
    public void updateCallStartDateTime(String accessToken) {
        callDetailsRepository.updateCallStartDate(LocalDateTime.now(), accessToken);
    }

    @Transactional
    @Override
    public int updateCallEndDateTime(String accessToken, String createdBy) {
        return callDetailsRepository.updateCallEndDate(LocalDateTime.now(), accessToken, createdBy);
    }

    @Transactional
    @Override
    public int updateGeolocation(CallDetailsDto callDetailsDto) {
        return callDetailsRepository.updateGeolocation(callDetailsDto.getLatitude(), callDetailsDto.getLongitude(), callDetailsDto.getAccessToken());
    }

    @Override
    public CallDetailsDto getCallDetails(String accessToken) {
        return callDetailsMapper.toDto(callDetailsRepository.findByAccessToken(accessToken).orElseThrow(() -> new RecordNotFoundException("Record not found")));
    }

    @Override
    @Transactional
    public void saveTyreConditionDetail(TyreConditionDataMapDto dataMapDto) throws Exception {
        List<TireCondtionDto> tyres = dataMapDto.getTyreDetails();
        ClaimTireConditionMe conditions = new ClaimTireConditionMe();
        ClaimTireConditionMe sizes = new ClaimTireConditionMe();
        ClaimTireConditionMe makes = new ClaimTireConditionMe();
        ClaimTireConditionMe newRbs = new ClaimTireConditionMe();
        try{

            if (AppConstant.EMPTY_STRING.equals(dataMapDto.getClaimNo()) || AppConstant.EMPTY_STRING.equals(dataMapDto.getJobRef())) {
                throw new RecordNotFoundException("Claim No and JobRef are empty");
            }
            for (int i = 0; i < tyres.size(); i++) {

                String value = tyres.get(i).getCondition();
                conditions.setPosition(0);
                conditions.setClaimNo(Integer.valueOf(dataMapDto.getClaimNo()));
                conditions.setRefNo(Integer.valueOf(dataMapDto.getJobRef()));
                switch (i) {
                    case 0 -> conditions.setLeftFront(value);
                    case 1 -> conditions.setRightFront(value);
                    case 2 -> conditions.setLeftRear(value);
                    case 3 -> conditions.setRightRear(value);
                    case 4 -> conditions.setLeftRearInner(value);
                    case 5 -> conditions.setRightRearInner(value);
                    case 6 -> conditions.setOther(value);
                    default -> throw new DataFormatException("Unexpected tire position: " + i);
                }

            }
            tireDetailRepository.save(conditions);
            for (int i = 0; i < tyres.size(); i++) {

                String value = tyres.get(i).getSize();
                sizes.setPosition(1);
                sizes.setClaimNo(Integer.valueOf(dataMapDto.getClaimNo()));
                sizes.setRefNo(Integer.valueOf(dataMapDto.getJobRef()));
                switch (i) {
                    case 0 -> sizes.setLeftFront(value);
                    case 1 -> sizes.setRightFront(value);
                    case 2 -> sizes.setLeftRear(value);
                    case 3 -> sizes.setRightRear(value);
                    case 4 -> sizes.setLeftRearInner(value);
                    case 5 -> sizes.setRightRearInner(value);
                    case 6 -> sizes.setOther(value);
                    default -> throw new DataFormatException("Unexpected tire position: " + i);
                }

            }
            tireDetailRepository.save(sizes);
            for (int i = 0; i < tyres.size(); i++) {

                String value = tyres.get(i).getMake();
                makes.setPosition(2);
                makes.setClaimNo(Integer.valueOf(dataMapDto.getClaimNo()));
                makes.setRefNo(Integer.valueOf(dataMapDto.getJobRef()));
                switch (i) {
                    case 0 -> makes.setLeftFront(value);
                    case 1 -> makes.setRightFront(value);
                    case 2 -> makes.setLeftRear(value);
                    case 3 -> makes.setRightRear(value);
                    case 4 -> makes.setLeftRearInner(value);
                    case 5 -> makes.setRightRearInner(value);
                    case 6 -> makes.setOther(value);
                    default -> throw new DataFormatException("Unexpected tire position: " + i);
                }

            }
            tireDetailRepository.save(makes);
            for (int i = 0; i < tyres.size(); i++) {

                String value = tyres.get(i).getNewRb();
                newRbs.setPosition(3);
                newRbs.setClaimNo(Integer.valueOf(dataMapDto.getClaimNo()));
                newRbs.setRefNo(Integer.valueOf(dataMapDto.getJobRef()));
                switch (i) {
                    case 0 -> newRbs.setLeftFront(value);
                    case 1 -> newRbs.setRightFront(value);
                    case 2 -> newRbs.setLeftRear(value);
                    case 3 -> newRbs.setRightRear(value);
                    case 4 -> newRbs.setLeftRearInner(value);
                    case 5 -> newRbs.setRightRearInner(value);
                    case 6 -> newRbs.setOther(value);
                    default -> throw new DataFormatException("Unexpected tire position: " + i);
                }
            }
            tireDetailRepository.save(newRbs);
            ClaimSpecialRemark claimSpecialRemark = new ClaimSpecialRemark();
            claimSpecialRemark.setRemark(dataMapDto.getRemarks());
            claimSpecialRemark.setDepartmentId(4);
            claimSpecialRemark.setSectionName("Motor Engineer Module-Online Inspection");
            claimSpecialRemark.setInputDateTime(LocalDateTime.now());
            claimSpecialRemark.setInputUserId(dataMapDto.getUserId());
            claimSpecialRemark.setClaimNo(Integer.valueOf(dataMapDto.getClaimNo()));

            specialCommentRepository.save(claimSpecialRemark);

        }catch(Exception e){
            throw new Exception();
        }
    }

    @Override
    public void saveInspectionDetail(ClaimInspectionInfoMainMeDTO data) throws Exception {
        try {
            ClaimInspectionInfoMainMe entity = inspectionDetailToEntity(data);
            Optional<ClaimInspectionInfoMainMe> existing = claimInspectionInfoMainMeRepository.findByJobNo(entity.getJobNo());
            existing.ifPresent(claimInspectionInfoMainMe -> entity.setInspectionId(claimInspectionInfoMainMe.getInspectionId()));
            claimInspectionInfoMainMeRepository.save(entity);
        }catch (Exception e){
            throw new Exception();
        }
    }


    private SmsMessageDetail getSendSmsMessageDetail(String mobileNumber, String link) {

        SmsMessageDetail smsMessageDetail = new SmsMessageDetail();
        smsMessageDetail.setDestination(mobileNumber);
        smsMessageDetail.setMessage(MESSAGE.concat(link));
        smsMessageDetail.setMessagePriority(1);
        smsMessageDetail.setMessageStatus(AppConstant.MESSAGE_SEND_STATUS);
        smsMessageDetail.setSendDateTime(LocalDateTime.now());
        return smsMessageDetail;
    }

    private String getJoinLink(String accessToken) {
        String link;
        UrlLongRequest request = new UrlLongRequest();
        request.setLongUrl(videoJoinUrl.concat("api/v1/client/view/").concat(accessToken));
        request.setExpiresDate(Utility.addDay(new Date(), 1));
        String shortUrl = urlService.convertToShortUrl(request);
        link = videoJoinUrl.concat("api/v1/client/").concat(shortUrl);
        return link;
    }




    public static ClaimInspectionInfoMainMe inspectionDetailToEntity(ClaimInspectionInfoMainMeDTO dto) throws Exception {
        try {
            if (dto == null) return null;
            ClaimInspectionInfoMainMe entity = new ClaimInspectionInfoMainMe();
            entity.setInspectionId(dto.getInspectionId());
            entity.setRefNo(dto.getRefNo());
            entity.setJobNo(dto.getJobNo());
            entity.setClaimNo(dto.getClaimNo());
            entity.setMakeConfirm(dto.getMakeConfirm());
            entity.setModelConfirm(dto.getModelConfirm());
            entity.setEngNoConfirm(dto.getEngNoConfirm());
            entity.setChassisNoConfirm(dto.getChassisNoConfirm());
            entity.setChassisNo(dto.getChassisNo());
            entity.setNotCheckedReason(dto.getNotCheckedReason());
            entity.setYearMakeConfirm(dto.getYearMakeConfirm());
            entity.setInspectDatetime(LocalDateTime.parse(dto.getInspectDatetime()));
            entity.setPav(dto.getPav());
            entity.setDamageDetails(dto.getDamageDetails());
            entity.setPad(dto.getPad());
            entity.setGenunOfAccid(dto.getGenunOfAccid());
            entity.setFirstStatementRqed(dto.getFirstStatementRqed());
            entity.setFirstStatementReqReason(dto.getFirstStatementReqReason());
            entity.setInvestRqed(dto.getInvestRqed());
            entity.setAssessorRemark(dto.getAssessorRemark());
            entity.setInspectionType(dto.getInspectionType());
            entity.setJobType(dto.getJobType());
            entity.setAssignedLocation(dto.getAssignedLocation());
            entity.setCurrentLocation(dto.getCurrentLocation());
            entity.setPlaceOfInspection(dto.getPlaceOfInspection());
            entity.setMileage(dto.getMileage());
            entity.setCostOfCall(dto.getCostOfCall());
            entity.setOtherFee(dto.getOtherFee());
            entity.setDeductions(dto.getDeductions());
            entity.setReasonOfDeduction(dto.getReasonOfDeduction());
            entity.setTotalAssessorFee(dto.getTotalAssessorFee());
            entity.setFeeDesc(dto.getFeeDesc());
            entity.setRecordStatus(dto.getRecordStatus());
            entity.setInpUser(dto.getInpUser());
            entity.setInpDatetime(dto.getInpDatetime());
            entity.setAssFeeAprStatus(dto.getAssFeeAprStatus());
            entity.setAssFeeAprUser(dto.getAssFeeAprUser());
            entity.setAssFeeAprDatetime(dto.getAssFeeAprDatetime());
            entity.setAssEstiAprStatus(dto.getAssEstiAprStatus());
            entity.setAssEstiAprUser(dto.getAssEstiAprUser());
            entity.setAssEstiAprDatetime(dto.getAssEstiAprDatetime());
            return entity;
        }catch (Exception e){
            throw e;
        }
    }
}
