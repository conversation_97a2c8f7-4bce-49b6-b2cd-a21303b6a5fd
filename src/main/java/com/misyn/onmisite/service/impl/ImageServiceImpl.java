package com.misyn.onmisite.service.impl;

import com.misyn.onmisite.dto.*;
import com.misyn.onmisite.dto.UploadImageDetailsDto;
import com.misyn.onmisite.dto.UserDto;
import com.misyn.onmisite.entity.CallDetails;
import com.misyn.onmisite.entity.JobDetail;
import com.misyn.onmisite.entity.UploadImageDetails;
import com.misyn.onmisite.exception.FileUploadException;
import com.misyn.onmisite.exception.JdbcException;
import com.misyn.onmisite.exception.RecordNotFoundException;
import com.misyn.onmisite.mappers.UploadImageDetailMapper;
import com.misyn.onmisite.repository.CallDetailsRepository;
import com.misyn.onmisite.repository.UploadImageDetailsRepository;
import com.misyn.onmisite.service.ImageService;
import com.misyn.onmisite.service.StorageService;
import com.misyn.onmisite.service.UtilService;
import com.misyn.onmisite.util.AppConstant;
import jakarta.xml.bind.DatatypeConverter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.*;
import java.math.BigDecimal;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.*;
import java.util.List;

@RequiredArgsConstructor
@Slf4j
@Service
public class ImageServiceImpl implements ImageService {

    private static final String IMAGE_EXTENSION = "png";
    private static final String IMAGES_DIRECTORY = "images";
    private static final String THUMB_DIRECTORY = "thumb";
    private final CallDetailsRepository callDetailsRepository;
    private final UploadImageDetailsRepository uploadImageDetailsRepository;
    private final JdbcTemplate jdbcTemplate;
    private final UtilService utilService;
    private final UploadImageDetailMapper uploadImageDetailMapper;
    private final StorageService storageService;
    private final String CLAIM_IMAGE_FOLDER = "images";
    @Value("${application.image.path}")
    private String rootLocation;
    @Value("${application.image.claim-document-directory}")
    private String claimDocumentDirectory;

    static void setDirectory(Long jobId, String rootLocation) {
        try {
            Path root = Paths.get(rootLocation);
            Path jobDirectory = Paths.get(rootLocation.concat("/" + jobId));
            Path imageDirectory = Paths.get(rootLocation.concat("/" + jobId).concat("/").concat(ImageServiceImpl.IMAGES_DIRECTORY));
            if (!Files.exists(root)) {
                Files.createDirectory(root);
            }
            if (!Files.exists(jobDirectory)) {
                Files.createDirectory(jobDirectory);
            }
            if (!Files.exists(imageDirectory)) {
                Files.createDirectory(imageDirectory);
            }

        } catch (Exception e) {
            ImageServiceImpl.log.error(e.getMessage(), e);
            throw new RuntimeException("Could not initialize folder for upload!");
        }
    }

    @Override
    public ImageData saveImageData(ImageData imageData, UserDto user) {

        ImageData saveImageData = new ImageData();
        if (imageData == null) {
            throw new RecordNotFoundException("Record not found");
        }

        try {
            CallDetails callDetails = callDetailsRepository
                    .findByAccessToken(imageData.getAccessToken())
                    .orElseThrow(() -> new RecordNotFoundException("Record Not Found"));
            JobDetail jobDetail = callDetails.getJobDetail();
            UploadImageDetailsDto uploadImageDetailsDto = new UploadImageDetailsDto();
            uploadImageDetailsDto.setJobId(jobDetail.getJobId());
            uploadImageDetailsDto.setCallId(callDetails.getCallId());
            uploadImageDetailsDto.setImageIndex(imageData.getIndex());
            uploadImageDetailsDto.setLatitude(AppConstant.EMPTY_STRING.equals(imageData.getLatitude()) ? "0" : imageData.getLatitude());
            uploadImageDetailsDto.setLongitude(AppConstant.EMPTY_STRING.equals(imageData.getLongitude()) ? "0" : imageData.getLongitude());
            uploadImageDetailsDto.setCompanyId(user.getCompanyId());
            uploadImageDetailsDto.setDistance(utilService.getDistance(jobDetail.getJobId(), callDetails.getLatitude(), callDetails.getLongitude()));
            uploadImageDetailsDto.setDocumentTypeId(0);
            this.saveImageToCloud(imageData, callDetails, jobDetail, uploadImageDetailsDto);
            uploadImageDetailsRepository.save(uploadImageDetailMapper.toEntity(uploadImageDetailsDto));
            saveImageData.setUploadStatus("Success");
            return saveImageData;
        } catch (RecordNotFoundException e) {
            log.error(e.getMessage(), e);
            throw e;
        }
    }

    @Transactional
    @Override
    public ImageData saveEditImageData(ImageData imageData, UserDto user) {
        ImageData saveImageData = new ImageData();
        if (imageData == null) {
            throw new RecordNotFoundException("Record not found");
        }
        try {
            UploadImageDetails uploadImageDetails = uploadImageDetailsRepository.findById(imageData.getUploadImageDetailsRefNo())
                    .orElseThrow(() -> new RecordNotFoundException("Record Not Found"));
            CallDetails callDetails = callDetailsRepository
                    .findById(uploadImageDetails.getCallDetails().getCallId())
                    .orElseThrow(() -> new RecordNotFoundException("Record Not Found"));
            JobDetail jobDetail = callDetails.getJobDetail();
            UploadImageDetailsDto uploadImageDetailsDto = uploadImageDetailMapper.toDto(uploadImageDetails);
            this.saveEditImageToCloud(imageData, callDetails, jobDetail, uploadImageDetailsDto);
            uploadImageDetailsDto.setDocumentTypeId(imageData.getMainDocumentTypeId());
            UploadImageDetails entity = uploadImageDetailMapper.toEntity(uploadImageDetailsDto);
            uploadImageDetailsRepository.save(entity);
            saveImageData.setUploadStatus("Success");
            return saveImageData;
        } catch (RecordNotFoundException e) {
            log.error(e.getMessage(), e);
            throw e;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new JdbcException(e.getMessage());
        }
    }

    @Override
    public UploadImageDetailsDto getUploadImageDetailsDto(Long uploadImageDetailsRefNo) {
        return uploadImageDetailMapper.toDto(uploadImageDetailsRepository.findById(uploadImageDetailsRefNo).orElseGet(UploadImageDetails::new));
    }

    @Transactional
    @Override
    public UploadImageDetailsDto deleteByUploadImageDetailsRefNo(Long uploadImageDetailsRefNo) {
        UploadImageDetails uploadImageDetails = uploadImageDetailsRepository
                .findById(uploadImageDetailsRefNo)
                .orElseGet(UploadImageDetails::new);
        UploadImageDetailsDto dto = uploadImageDetailMapper.toDto(uploadImageDetails);
        storageService.deleteFile(uploadImageDetails.getImagePath());
        storageService.deleteFile(uploadImageDetails.getThumbImagePath());
        if (Objects.nonNull(uploadImageDetails.getEditImagePath())) {
            storageService.deleteFile(uploadImageDetails.getEditImagePath());
        }
        if (Objects.nonNull(uploadImageDetails.getThumbEditImagePath())) {
            storageService.deleteFile(uploadImageDetails.getThumbEditImagePath());
        }
        uploadImageDetailsRepository.deleteByUploadImageDetailsRefNo(uploadImageDetailsRefNo);
        return dto;
    }

    protected synchronized String getImagePath(String claimNo) {

        return claimDocumentDirectory
                .concat(AppConstant.STRING_BACKSLASH_SIGN)
                .concat(String.valueOf(claimNo))
                .concat(AppConstant.STRING_BACKSLASH_SIGN)
                .concat(CLAIM_IMAGE_FOLDER)
                .concat(AppConstant.STRING_BACKSLASH_SIGN);
    }

    protected synchronized String getImageThumbPath(String claimNo) {

        return claimDocumentDirectory
                .concat(AppConstant.STRING_BACKSLASH_SIGN)
                .concat(String.valueOf(claimNo))
                .concat(AppConstant.STRING_BACKSLASH_SIGN)
                .concat(CLAIM_IMAGE_FOLDER)
                .concat(AppConstant.STRING_BACKSLASH_SIGN)
                .concat(THUMB_DIRECTORY)
                .concat(AppConstant.STRING_BACKSLASH_SIGN);
    }

    private void saveImageToCloud(ImageData imageData, CallDetails callDetails, JobDetail jobDetail, UploadImageDetailsDto uploadImageDetailsDto) {

        try {
            String fileName = "image_" + callDetails.getCallId() + "_" + System.nanoTime() + ".".concat(IMAGE_EXTENSION);
            String thumbFileName = "thumb_image_" + callDetails.getCallId() + "_" + System.nanoTime() + ".".concat(IMAGE_EXTENSION);
            uploadImageDetailsDto.setImageName(fileName);
            uploadImageDetailsDto.setThumbImageName(thumbFileName);
            uploadImageDetailsDto.setImagePath(getImagePath(jobDetail.getJobNumber()).concat(fileName));
            uploadImageDetailsDto.setThumbImagePath(getImageThumbPath(jobDetail.getJobNumber()).concat(thumbFileName));

            byte[] decodedBytes = DatatypeConverter.parseBase64Binary(imageData.getImageData());
            BufferedImage bfi = ImageIO.read(new ByteArrayInputStream(decodedBytes));

            InputStream inputStream = new ByteArrayInputStream(decodedBytes);
            long contentLength = decodedBytes.length;
            storageService.uploadFile(inputStream, uploadImageDetailsDto.getImagePath(), contentLength);

            InputStream imageThumbInputStream = getImageThumbInputStream(bfi);
            storageService.uploadFile(imageThumbInputStream, uploadImageDetailsDto.getThumbImagePath(), imageThumbInputStream.available());

            inputStream.close();
            imageThumbInputStream.close();


        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new FileUploadException("Can not be upload");
        }
    }

    private void saveEditImageToCloud(ImageData imageData, CallDetails callDetails, JobDetail jobDetail, UploadImageDetailsDto uploadImageDetailsDto) {

        try {
            String fileName = "image_" + callDetails.getCallId() + "_" + System.nanoTime() + ".".concat(IMAGE_EXTENSION);
            String thumbFileName = "thumb_image_" + callDetails.getCallId() + "_" + System.nanoTime() + ".".concat(IMAGE_EXTENSION);
            uploadImageDetailsDto.setEditImageName(fileName);
            uploadImageDetailsDto.setThumbEditImageName(thumbFileName);
            uploadImageDetailsDto.setEditImagePath(getImagePath(jobDetail.getJobNumber())
                    .concat(fileName));
            uploadImageDetailsDto.setThumbEditImagePath(getImageThumbPath(jobDetail.getJobNumber())
                    .concat(thumbFileName));

            byte[] decodedBytes = DatatypeConverter.parseBase64Binary(imageData.getImageData());
            BufferedImage bfi = ImageIO.read(new ByteArrayInputStream(decodedBytes));

            InputStream inputStream = new ByteArrayInputStream(decodedBytes);
            long contentLength = decodedBytes.length;
            storageService.uploadFile(inputStream, uploadImageDetailsDto.getEditImagePath(), contentLength);

            InputStream imageThumbInputStream = getImageThumbInputStream(bfi);
            storageService.uploadFile(imageThumbInputStream, uploadImageDetailsDto.getThumbEditImagePath(), imageThumbInputStream.available());

            inputStream.close();
            imageThumbInputStream.close();

        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new FileUploadException("Can not be upload");
        }
    }

    private void saveImageToFile(ImageData imageData, CallDetails callDetails, JobDetail jobDetail, UploadImageDetailsDto uploadImageDetailsDto) {
        File outputFile = null;
        File outputThumbFile = null;
        try {
            this.createDirectory(jobDetail.getJobId());
            String fileName = "image_" + callDetails.getCallId() + "_" + System.nanoTime() + ".".concat(IMAGE_EXTENSION);
            String thumbFileName = "thumb_image_" + callDetails.getCallId() + "_" + System.nanoTime() + ".".concat(IMAGE_EXTENSION);

            uploadImageDetailsDto.setImageName(fileName);
            uploadImageDetailsDto.setThumbImageName(thumbFileName);
            uploadImageDetailsDto.setImagePath("".concat("" + jobDetail.getJobId()).concat("/").concat(IMAGES_DIRECTORY).concat("/").concat(fileName));
            uploadImageDetailsDto.setThumbImagePath("".concat("" + jobDetail.getJobId()).concat("/").concat(IMAGES_DIRECTORY).concat("/").concat(thumbFileName));

            byte[] decodedBytes = DatatypeConverter.parseBase64Binary(imageData.getImageData());
            BufferedImage bfi = ImageIO.read(new ByteArrayInputStream(decodedBytes));
            // BufferedImage resizeBfi = ImageIO.read(getResizeImageInputStream(bfi, IMAGE_EXTENSION, IMAGE_RESIZE_PERCENT));
            BufferedImage thumbBfi = ImageIO.read(getImageThumbInputStream(bfi));
            outputFile = new File(rootLocation.concat(uploadImageDetailsDto.getImagePath()));
            outputThumbFile = new File(rootLocation.concat(uploadImageDetailsDto.getThumbImagePath()));
            ImageIO.write(bfi, IMAGE_EXTENSION, outputFile);
            ImageIO.write(thumbBfi, IMAGE_EXTENSION, outputThumbFile);

        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new FileUploadException("Can not be upload");
        } finally {
            if (outputFile != null && outputThumbFile != null) {
                uploadImageDetailsDto.setFileSize(BigDecimal.valueOf(outputFile.length()));
                uploadImageDetailsDto.setThumbFileSize(BigDecimal.valueOf(outputThumbFile.length()));
            }
        }
    }

    private void saveEditImageToFile(ImageData imageData, CallDetails callDetails, JobDetail jobDetail, UploadImageDetailsDto uploadImageDetailsDto) {
        File outputFile = null;
        File outputThumbFile = null;
        try {
            this.createDirectory(jobDetail.getJobId());
            String fileName = "image_" + callDetails.getCallId() + "_" + System.nanoTime() + ".".concat(IMAGE_EXTENSION);
            String thumbFileName = "thumb_image_" + callDetails.getCallId() + "_" + System.nanoTime() + ".".concat(IMAGE_EXTENSION);

            uploadImageDetailsDto.setEditImageName(fileName);
            uploadImageDetailsDto.setThumbEditImageName(thumbFileName);
            uploadImageDetailsDto.setEditImagePath("".concat("" + jobDetail.getJobId()).concat("/").concat(IMAGES_DIRECTORY).concat("/").concat(fileName));
            uploadImageDetailsDto.setThumbEditImagePath("".concat("" + jobDetail.getJobId()).concat("/").concat(IMAGES_DIRECTORY).concat("/").concat(thumbFileName));

            byte[] decodedBytes = DatatypeConverter.parseBase64Binary(imageData.getImageData());
            BufferedImage bfi = ImageIO.read(new ByteArrayInputStream(decodedBytes));
            // BufferedImage resizeBfi = ImageIO.read(getResizeImageInputStream(bfi, IMAGE_EXTENSION, IMAGE_RESIZE_PERCENT));
            BufferedImage thumbBfi = ImageIO.read(getImageThumbInputStream(bfi));
            outputFile = new File(rootLocation.concat(uploadImageDetailsDto.getEditImagePath()));
            outputThumbFile = new File(rootLocation.concat(uploadImageDetailsDto.getThumbEditImagePath()));
            ImageIO.write(bfi, IMAGE_EXTENSION, outputFile);
            ImageIO.write(thumbBfi, IMAGE_EXTENSION, outputThumbFile);

        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new FileUploadException("Can not be upload");
        } finally {
            if (outputFile != null && outputThumbFile != null) {
                uploadImageDetailsDto.setFileSize(BigDecimal.valueOf(outputFile.length()));
                uploadImageDetailsDto.setThumbFileSize(BigDecimal.valueOf(outputThumbFile.length()));
            }
        }
    }

    private void createDirectory(Long jobId) {
        setDirectory(jobId, rootLocation);
    }

    private InputStream getImageThumbInputStream(BufferedImage originalImage) {
        InputStream isResize = null;
        try {
            int type = originalImage.getType() == 0 ? BufferedImage.TYPE_INT_ARGB : originalImage.getType();
            BufferedImage thumb = new BufferedImage(100, 100, type);

            Graphics2D g2d = (Graphics2D) thumb.getGraphics();
            g2d.drawImage(originalImage, 0, 0, thumb.getWidth() - 1, thumb.getHeight() - 1, 0, 0,
                    originalImage.getWidth() - 1, originalImage.getHeight() - 1, null);
            g2d.dispose();
            isResize = new ByteArrayInputStream(toByteArrayAutoClosable(thumb));
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return isResize;
    }

    private byte[] toByteArrayAutoClosable(BufferedImage image) throws IOException {
        try (ByteArrayOutputStream out = new ByteArrayOutputStream()) {
            ImageIO.write(image, ImageServiceImpl.IMAGE_EXTENSION, out);
            return out.toByteArray();
        }
    }

    @Override
    public ClaimPreviousPhotosResponseDto getPreviousPhotosByClaimNumber(Integer claimNumber) {
        try {
            String sql = """
                SELECT
                    ci.N_CLIM_NO as claimNumber,
                    cim.n_inspection_id as inspectionId,
                    cit.inspection_type_desc as inspectionType,
                    ci.N_REF_NO as referenceNumber,
                    ci.V_DOC_NAME as documentName,
                    ci.V_DOC_PATH as documentPath,
                    cdt.N_DOC_TYPE_ID as documentTypeId,
                    cdt.V_DOC_TYPE_NAME as documentTypeName
                FROM
                    claim_upload_images ci
                    INNER JOIN claim_inspection_info_main cim ON ci.N_CLIM_NO = cim.n_claim_no
                    INNER JOIN claim_inspection_type cit ON cim.n_inspection_type = cit.inspection_type_id
                    INNER JOIN claim_document_type cdt ON ci.N_DOC_TYPE_ID = cdt.N_DOC_TYPE_ID
                WHERE
                    ci.N_CLIM_NO = ?
                ORDER BY
                    ci.N_REF_NO
                """;

            List<ClaimPreviousPhotoDto> photos = jdbcTemplate.query(sql, new Object[]{claimNumber}, (rs, rowNum) -> {
                ClaimPreviousPhotoDto photo = new ClaimPreviousPhotoDto();
                photo.setClaimNumber(rs.getInt("claimNumber"));
                photo.setInspectionId(rs.getInt("inspectionId"));
                photo.setInspectionType(rs.getString("inspectionType"));
                photo.setReferenceNumber(rs.getInt("referenceNumber"));
                photo.setDocumentName(rs.getString("documentName"));
                photo.setDocumentPath(rs.getString("documentPath"));
                photo.setDocumentTypeId(rs.getInt("documentTypeId"));
                photo.setDocumentTypeName(rs.getString("documentTypeName"));
                return photo;
            });

            ClaimPreviousPhotosResponseDto response = new ClaimPreviousPhotosResponseDto();
            response.setClaimNumber(claimNumber);

            // Group photos by inspection type
            Map<String, InspectionTypePhotoDto> inspectionTypeMap = new LinkedHashMap<>();

            for (ClaimPreviousPhotoDto photo : photos) {
                String inspectionType = photo.getInspectionType();
                InspectionTypePhotoDto inspectionTypeDto = inspectionTypeMap.computeIfAbsent(inspectionType, k -> {
                    InspectionTypePhotoDto dto = new InspectionTypePhotoDto();
                    dto.setInspectionId(photo.getInspectionId());
                    dto.setInspectionType(inspectionType);
                    return dto;
                });

                inspectionTypeDto.getPhotos().add(photo);
            }

            response.setInspectionTypes(new ArrayList<>(inspectionTypeMap.values()));
            return response;

        } catch (Exception e) {
            log.error("Error fetching previous photos for claim number: " + claimNumber, e);
            throw new RuntimeException("Failed to fetch previous photos", e);
        }
    }

}
