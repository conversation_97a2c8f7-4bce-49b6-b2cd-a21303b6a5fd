package com.misyn.onmisite.service.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.misyn.onmisite.config.AsyncConfiguration;
import com.misyn.onmisite.dto.TwilioCallbackDto;
import com.misyn.onmisite.dto.TwilioTokenDto;
import com.misyn.onmisite.dto.UploadVideoDetailsDto;
import com.misyn.onmisite.dto.VideoData;
import com.misyn.onmisite.entity.CallDetails;
import com.misyn.onmisite.entity.TwilioLog;
import com.misyn.onmisite.entity.TwilioVideoRecordingLog;
import com.misyn.onmisite.exception.BadRequestException;
import com.misyn.onmisite.exception.RecordNotFoundException;
import com.misyn.onmisite.mappers.UploadVideoDetailMapper;
import com.misyn.onmisite.repository.CallDetailsRepository;
import com.misyn.onmisite.repository.TwilioLogRepository;
import com.misyn.onmisite.repository.TwilioVideoRecordingLogRepository;
import com.misyn.onmisite.repository.UploadVideoDetailsRepository;
import com.misyn.onmisite.service.StorageService;
import com.misyn.onmisite.service.TwilioService;
import com.misyn.onmisite.util.AppConstant;
import com.twilio.Twilio;
import com.twilio.base.ResourceSet;
import com.twilio.http.*;
import com.twilio.jwt.accesstoken.AccessToken;
import com.twilio.jwt.accesstoken.VideoGrant;
import com.twilio.rest.Domains;
import com.twilio.rest.video.v1.Composition;
import com.twilio.rest.video.v1.Recording;
import com.twilio.rest.video.v1.room.Participant;
import com.twilio.rest.video.v1.room.RecordingRules;
import com.twilio.type.RecordingRule;
import com.twilio.type.RecordingRulesUpdate;
import jakarta.annotation.PostConstruct;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.impl.client.HttpClientBuilder;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.net.URI;
import java.net.URL;
import java.net.URLConnection;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;

@RequiredArgsConstructor
@Slf4j
@Service
public class TwilioServiceImpl implements TwilioService {

    private static final String VIDEO_DIRECTORY = "video";
    private final CallDetailsRepository callDetailsRepository;
    private final TwilioVideoRecordingLogRepository twilioVideoRecordingLogRepository;
    private final TwilioLogRepository twilioLogRepository;
    private final UploadVideoDetailsRepository uploadVideoDetailsRepository;
    private final StorageService storageService;
    private final UploadVideoDetailMapper uploadVideoDetailMapper;
    private final ObjectMapper objectMapper;
    private final String CLAIM_VIDEO_FOLDER = "video";
    @Value("${application.twilio.sid}")
    private String accountSid;
    @Value("${application.twilio.key}")
    private String apiKeySid;
    @Value("${application.twilio.secret}")
    private String apiKeySecret;
    @Value("${application.twilio.call-back-url}")
    private String callBackUrl;
    @Value("${application.image.path}")
    private String imagePath;
    @Value("${application.image.claim-document-directory}")
    private String claimDocumentDirectory;

    private static void deleteTwilioRecordingFiles(String roomSid) {
        ResourceSet<Recording> recordings = Recording.reader().setGroupingSid(Collections.singletonList(roomSid)).limit(20).read();
        for (Recording record : recordings) {
            Recording.deleter(record.getSid()).delete();
            log.info("Delete a Recording instance: RM{} -> RT{}", roomSid, record.getSid());
        }
    }

    private static void deleteTwilioCompositionFile(String roomSid) {
        boolean didDelete;
        try {
            ResourceSet<Composition> compositions = Composition.reader().setRoomSid(roomSid).read();
            for (Composition c : compositions) {
                didDelete = Composition.deleter(c.getSid()).delete();
                log.info("Delete Composition with SID={}->{}", c.getSid(), didDelete);
            }
        } catch (Exception e) {
            log.error(e.getMessage());
        }
    }

    @Transactional
    @Override
    public TwilioTokenDto getTwilioTokenByAdmin(TwilioTokenDto twilioTokenRequest) {

        TwilioTokenDto twilioTokenDto = new TwilioTokenDto();
        try {
            setTwilioTokenRequest(twilioTokenRequest, twilioTokenDto);
            int i = callDetailsRepository.updateRoomDetails(twilioTokenRequest.getRoom(), twilioTokenRequest.getCallId());
            log.info("Call Details Updated {}", i);

        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return twilioTokenDto;
    }

    private void setTwilioTokenRequest(TwilioTokenDto twilioTokenRequest, TwilioTokenDto twilioTokenDto) {
        VideoGrant grant = new VideoGrant();
        grant.setRoom(twilioTokenRequest.getRoom());

        AccessToken token = new AccessToken.Builder(
                accountSid,
                apiKeySid,
                apiKeySecret
        ).identity(twilioTokenRequest.getIdentity()).grant(grant).build();

        twilioTokenDto.setRoom(twilioTokenRequest.getRoom());
        twilioTokenDto.setIdentity(twilioTokenRequest.getIdentity());
        twilioTokenDto.setToken(token.toJwt());
    }

    @Override
    public TwilioTokenDto getTwilioTokenByClient(TwilioTokenDto twilioTokenRequest) {
        TwilioTokenDto twilioTokenDto = new TwilioTokenDto();
        try {
            setTwilioTokenRequest(twilioTokenRequest, twilioTokenDto);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return twilioTokenDto;
    }

    @PostConstruct
    public void init() {
        Twilio.init(apiKeySid, apiKeySecret, accountSid);
    }

    @Override
    public void startAllRecording(String accessToken) {
        String roomSid;
        try {
            roomSid = this.getRoomSid(accessToken);
            ArrayList<RecordingRule> rules = new ArrayList<>();
            rules.add(RecordingRule.builder()
                    //.withType(RecordingRule.Type.INCLUDE).withKind(Rule.Kind.AUDIO)
                    .withType(RecordingRule.Type.INCLUDE).withAll()
                    .build());

            RecordingRules recordingRules =
                    RecordingRules.updater(roomSid)
                            .setRules(objectMapper.convertValue(new RecordingRulesUpdate(rules), Map.class))
                            .update();

            log.info("Start Recording Rule Updated:{}", recordingRules.getRoomSid());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }


    @Override
    public void stopAllRecording(String accessToken) {
        String roomSid;
        try {
            roomSid = this.getRoomSid(accessToken);
            ArrayList<RecordingRule> rules = new ArrayList<>();
            rules.add(RecordingRule.builder()
                    .withType(RecordingRule.Type.EXCLUDE).withAll()
                    .build());

            RecordingRules recordingRules =
                    RecordingRules.updater(roomSid)
                            .setRules(objectMapper.convertValue(new RecordingRulesUpdate(rules), Map.class))
                            .update();

            log.info("Stop Recording Rule Updated:{}", recordingRules.getRoomSid());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }


    @Async(AsyncConfiguration.TASK_EXECUTOR_SERVICE)
    @Override
    public void compositionVideo() {
        twilioVideoRecordingLogRepository.findAllByRemoteParticipant(true).forEach(this::requestAndUpdateCompositionVideo);
    }

    @Transactional
    public void requestAndUpdateCompositionVideo(TwilioVideoRecordingLog twilioVideoRecordingLog) {
        String compositionSid = this.videoComposition(twilioVideoRecordingLog.getParticipantSid(), twilioVideoRecordingLog.getRoomSid());
        twilioVideoRecordingLogRepository.updateLocalParticipantSid(compositionSid, twilioVideoRecordingLog.getParticipantSid(), twilioVideoRecordingLog.getRoomSid());
    }

    @Async(AsyncConfiguration.TASK_EXECUTOR_SERVICE)
    @Override
    public void downloadVideoFiles() {
        try {
            twilioVideoRecordingLogRepository.findAllByPendingDownload().forEach(this::processDownload);
        } catch (Exception e) {
            log.error(e.getMessage());
        }
    }

    @Transactional
    public void processDownload(TwilioVideoRecordingLog twilioVideoRecordingLog) {
        try {
            CallDetails callDetails = callDetailsRepository.findByIdWithJobDetail(twilioVideoRecordingLog.getCallId())
                    .orElseThrow(() -> new RecordNotFoundException("Call Id Not Found"));

            this.mediaFile(twilioVideoRecordingLog.getCompositionSid(),
                    callDetails.getJobDetail().getJobId(),
                    callDetails.getCallId(),
                    twilioVideoRecordingLog.getRoomSid(),
                    callDetails.getJobDetail().getJobNumber());
            twilioVideoRecordingLogRepository.updateFileDownloadedStatus(twilioVideoRecordingLog.getCompositionSid());
        } catch (BadRequestException e) {
            log.info("Twilio Video CompositionSid is still not available : {}", e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }

    private String getRoomSid(String accessToken) {
        CallDetails callDetails = callDetailsRepository.findByAccessToken(accessToken).orElseThrow(() -> new RecordNotFoundException("Record Not Found"));
        return callDetails.getRoomName();
    }


    private String videoComposition(String participantSid, String roomSid) {

        HashMap<String, Object> videoLayoutData = new HashMap<>() {{
            put("single", new HashMap<String, Object>() {{
                put("video_sources", new String[]{participantSid});
            }});
        }};

        Composition composition = Composition.creator(roomSid).setTrim(true).setAudioSources("*")
                .setVideoLayout(videoLayoutData).setStatusCallback(callBackUrl).setStatusCallbackMethod(HttpMethod.GET).setFormat(Composition.Format.MP4).create();

        log.info("Created composition with SID = {}  participantSid = {}", composition.getSid(), participantSid);
        return composition.getSid();
    }


    private void mediaFile(String compositionSid, Long jobId, Long callId, String roomSid, String jobNo) {
        createDirectory(jobNo);
        HttpClientBuilder clientBuilder = HttpClientBuilder.create();
        clientBuilder.disableRedirectHandling();

        TwilioRestClient restClient = new TwilioRestClient.Builder(apiKeySid, apiKeySecret).httpClient(new NetworkHttpClient(clientBuilder)).build();

        Request request = new Request(HttpMethod.GET, Domains.VIDEO.toString(), "/v1/Compositions/" + compositionSid + "/Media?Ttl=3600", restClient.getRegion());
        Response response = restClient.request(request);
        JSONObject json = new JSONObject(response.getContent());
        try {
            String mediaLocation = json.getString("redirect_to");
            URI uri = new URI(mediaLocation);
            String recordingPath = getRecordingPath(jobNo).concat(compositionSid).concat(".mp4");
            saveVideoOci(uri, recordingPath);
            VideoData videoData = VideoData.builder().jobId(jobId).callId(callId).videoPath(recordingPath).fileName(compositionSid + ".mp4").build();
            saveUploadVideoDetails(videoData);
        } catch (Exception e) {
            throw new BadRequestException("CompositionSid : " + compositionSid, e);
        }

        try {
            boolean didDelete = Composition.deleter(compositionSid).delete();
            log.info("Delete a Composition instance:{}", didDelete);
            deleteTwilioRecordingFiles(roomSid);
            deleteTwilioCompositionFile(roomSid);
        } catch (Exception e) {
            log.error(e.getMessage());
        }
    }

    private void saveVideoOci(URI uri, String recordingPath) throws IOException {
        try (InputStream twilioStream = uri.toURL().openStream()) {
            long contentLength = getContentLengthFromURL(uri.toURL());
            storageService.uploadMp4File(twilioStream, recordingPath, contentLength);
        }
    }

    private long getContentLengthFromURL(URL url) throws IOException {
        URLConnection connection = url.openConnection();
        return connection.getContentLengthLong();
    }

    protected String getRecordingPath(String claimNo) {

        return claimDocumentDirectory
                .concat(AppConstant.STRING_BACKSLASH_SIGN)
                .concat(String.valueOf(claimNo))
                .concat(AppConstant.STRING_BACKSLASH_SIGN)
                .concat(CLAIM_VIDEO_FOLDER)
                .concat(AppConstant.STRING_BACKSLASH_SIGN);
    }

    private void createDirectory(String jobId) {
        try {
            Path root = Paths.get(imagePath);
            Path jobDirectory = Paths.get(imagePath.concat("/" + jobId));
            Path imageDirectory = Paths.get(imagePath.concat("/" + jobId).concat("/").concat(VIDEO_DIRECTORY));
            if (!Files.exists(root)) {
                Files.createDirectory(root);
            }
            if (!Files.exists(jobDirectory)) {
                Files.createDirectory(jobDirectory);
            }
            if (!Files.exists(imageDirectory)) {
                Files.createDirectory(imageDirectory);
            }

        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new RuntimeException("Could not create folder for video record upload!");
        }
    }

    private void saveUploadVideoDetails(VideoData videoData) {
        try {
            UploadVideoDetailsDto uploadVideoDetailsDto = new UploadVideoDetailsDto();
            uploadVideoDetailsDto.setJobId(videoData.getJobId());
            uploadVideoDetailsDto.setCallId(videoData.getCallId());
            uploadVideoDetailsDto.setVideoIndex(videoData.getIndex());
            uploadVideoDetailsDto.setLatitude(videoData.getLatitude());
            uploadVideoDetailsDto.setLongitude(videoData.getLongitude());
            uploadVideoDetailsDto.setVideoPath(videoData.getVideoPath());
            uploadVideoDetailsDto.setVideoName(videoData.getFileName());
            uploadVideoDetailsDto.setDistance(BigDecimal.ZERO);
            uploadVideoDetailsDto.setCompanyId(1L);
            uploadVideoDetailsRepository.save(uploadVideoDetailMapper.toEntity(uploadVideoDetailsDto));

        } catch (RecordNotFoundException e) {
            log.error(e.getMessage(), e);
            throw e;
        }
    }


    @Transactional
    @Override
    public TwilioCallbackDto saveTwilioCallbackDetail(TwilioCallbackDto twilioCallbackDto) {
        try {
            log.info("twilioCallbackDto.setSequenceNumber() | Service --> sequence no {}, room name {}", twilioCallbackDto.getSequenceNumber(), twilioCallbackDto.getRoomName());
            TwilioLog twilioLog = new TwilioLog();
            twilioLog.setInpDateTime(LocalDateTime.now());
            twilioLog.setRoomName(twilioCallbackDto.getRoomName());
            twilioLog.setRoomSid(twilioCallbackDto.getRoomSid());
            twilioLog.setParticipantSid(twilioCallbackDto.getParticipantSid());
            twilioLog.setSequenceNumber(String.valueOf(twilioCallbackDto.getSequenceNumber()));
            twilioLog.setStatusCallbackEvent(twilioCallbackDto.getStatusCallbackEvent());
            log.info("twilioCallbackDto.setStatusCallbackEvent() | Service {}", twilioCallbackDto.getStatusCallbackEvent());
            twilioLogRepository.save(twilioLog);

            if (Objects.nonNull(twilioCallbackDto.getSourceSid())) {
                TwilioVideoRecordingLog twilioVideoRecordingLog = new TwilioVideoRecordingLog();
                Optional<TwilioVideoRecordingLog> bySourceSid = twilioVideoRecordingLogRepository.findBySourceSid(twilioCallbackDto.getSourceSid());
                if (bySourceSid.isPresent()) {
                    twilioVideoRecordingLog.setLogId(bySourceSid.get().getLogId());
                    twilioVideoRecordingLog.setCreatedDate(bySourceSid.get().getCreatedDate());
                    twilioVideoRecordingLog.setModifiedDate(LocalDateTime.now());
                } else {
                    twilioVideoRecordingLog.setCreatedDate(LocalDateTime.now());
                    twilioVideoRecordingLog.setModifiedDate(LocalDateTime.now());
                }
                AtomicInteger noOfParticipant = new AtomicInteger(0);
                ResourceSet<Participant> read = Participant.reader(twilioCallbackDto.getRoomSid()).read();
                read.forEach(participant -> {
                    noOfParticipant.set(noOfParticipant.get() + 1);
                });

                CallDetails callDetails = callDetailsRepository.findByRoomName(twilioCallbackDto.getRoomName()).orElseThrow(() -> new RecordNotFoundException("Call Room Sid Not Found"));
                if (noOfParticipant.get() == 2) {
                    twilioVideoRecordingLog.setCallId(callDetails.getCallId());
                    twilioVideoRecordingLog.setParticipantSid(twilioCallbackDto.getParticipantSid());
                    twilioVideoRecordingLog.setSourceSid(twilioCallbackDto.getSourceSid());
                    twilioVideoRecordingLog.setRoomSid(twilioCallbackDto.getRoomSid());
                    twilioVideoRecordingLog.setRoomType(twilioCallbackDto.getRoomType());
                    twilioVideoRecordingLog.setSequenceNumber(twilioCallbackDto.getSequenceNumber());
                    twilioVideoRecordingLog.setStatusCallbackEvent(twilioCallbackDto.getStatusCallbackEvent());
                    twilioVideoRecordingLog.setRemoteParticipant(true);
                    twilioVideoRecordingLog.setRunComposition(false);
                    twilioVideoRecordingLog.setFileDownloaded(false);
                    twilioVideoRecordingLogRepository.save(twilioVideoRecordingLog);
                    log.info("Both participants are connected call id {}", callDetails.getCallId());
                } else {
                    log.info("Local ParticipantSid {}", twilioCallbackDto.getParticipantSid());
                }
            }
        } catch (Exception e) {
            log.error("saveTwilioCallbackDetail() | Error : {}", e.getMessage());
            throw e;
        }
        return twilioCallbackDto;
    }


}
