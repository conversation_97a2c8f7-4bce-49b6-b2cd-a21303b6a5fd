package com.misyn.onmisite.service.impl;

import com.misyn.onmisite.dto.*;
import com.misyn.onmisite.dto.form.AdminVideoForm;
import com.misyn.onmisite.entity.*;
import com.misyn.onmisite.exception.RecordNotFoundException;
import com.misyn.onmisite.repository.*;
import com.misyn.onmisite.service.ClaimService;
import com.misyn.onmisite.util.AppConstant;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Service
public class ClaimServiceImpl implements ClaimService {

    @Autowired
    private ClaimDetailRepository claimInfoRepository;
    @Autowired
    private PolicyDetailRepository policyDetailRepository;
    @Autowired
    private InspectionDetailRepository inspectionDetailRepository;
    @Autowired
    private TireDetailRepository tireDetailRepository;
    @Autowired
    ClaimHandlerDetailRepository claimHandlerDetailRepository;
    @Autowired
    ClaimInspectionInfoMainMeRepository claimInspectionInfoMainMeRepository;

    @Override
    public ClaimsDto getAllClaimDetail(AdminVideoForm adminVideoForm) {
        ClaimsDto returnDto = new ClaimsDto();
        ClaimDetail claimDetail = claimInfoRepository.findByClaimNo(Integer.parseInt(adminVideoForm.getJobDetailDto().getJobNumber())).orElseThrow(() -> new RecordNotFoundException("Claim detail not found"));

        PolicyDetail policyDetail = policyDetailRepository.findById(claimDetail.getPolRefNo()).orElseThrow(() -> new RecordNotFoundException("Policy detail not found"));

        ClaimInspectionDetail claimInspectionDetail = inspectionDetailRepository.findById(adminVideoForm.getCallDetailsDto().getInspectionTypeId()).orElseThrow(() -> new RecordNotFoundException("Inspection detail not found"));

        List<ClaimTireConditionMe> tireConditionMeList = tireDetailRepository.findByClaimNoAndRefNo(claimDetail.getClaimNo(), claimInspectionDetail.getInspectionId());

        ClaimAssignClaimHandler claimAssignClaimHandler = claimHandlerDetailRepository.findByClaimNo(claimDetail.getClaimNo());


        this.claimToDto(returnDto, claimDetail, policyDetail, claimInspectionDetail,tireConditionMeList,claimAssignClaimHandler);

        return returnDto;


    }

    @Override
    public ClaimInspectionInfoMainMeDTO getAllInspectionDetail(AdminVideoForm agentDto) {
       return inspectionDetailToDto(claimInspectionInfoMainMeRepository.findByJobNo(agentDto.getCallDetailsDto().getInspectionJobNumber()).orElseThrow(() -> new RecordNotFoundException("Inspection detail not found"))) ;
    }

    public ClaimInspectionInfoMainMeDTO inspectionDetailToDto(ClaimInspectionInfoMainMe entity) {
        if (entity == null) return null;

        ClaimInspectionInfoMainMeDTO dto = new ClaimInspectionInfoMainMeDTO();
        dto.setInspectionId(entity.getInspectionId());
        dto.setRefNo(entity.getRefNo());
        dto.setJobNo(entity.getJobNo());
        dto.setClaimNo(entity.getClaimNo());
        dto.setMakeConfirm(entity.getMakeConfirm());
        dto.setModelConfirm(entity.getModelConfirm());
        dto.setEngNoConfirm(entity.getEngNoConfirm());
        dto.setChassisNoConfirm(entity.getChassisNoConfirm());
        dto.setChassisNo(entity.getChassisNo());
        dto.setNotCheckedReason(entity.getNotCheckedReason());
        dto.setYearMakeConfirm(entity.getYearMakeConfirm());
        dto.setInspectDatetime(String.valueOf(entity.getInspectDatetime()));
        dto.setPav(entity.getPav());
        dto.setDamageDetails(entity.getDamageDetails());
        dto.setPad(entity.getPad());
        dto.setGenunOfAccid(entity.getGenunOfAccid());
        dto.setFirstStatementRqed(entity.getFirstStatementRqed());
        dto.setFirstStatementReqReason(entity.getFirstStatementReqReason());
        dto.setInvestRqed(entity.getInvestRqed());
        dto.setAssessorRemark(entity.getAssessorRemark());
        dto.setInspectionType(entity.getInspectionType());
        dto.setJobType(entity.getJobType());
        dto.setAssignedLocation(entity.getAssignedLocation());
        dto.setCurrentLocation(entity.getCurrentLocation());
        dto.setPlaceOfInspection(entity.getPlaceOfInspection());
        dto.setMileage(entity.getMileage());
        dto.setCostOfCall(entity.getCostOfCall());
        dto.setOtherFee(entity.getOtherFee());
        dto.setDeductions(entity.getDeductions());
        dto.setReasonOfDeduction(entity.getReasonOfDeduction());
        dto.setTotalAssessorFee(entity.getTotalAssessorFee());
        dto.setFeeDesc(entity.getFeeDesc());
        dto.setRecordStatus(entity.getRecordStatus());
        dto.setInpUser(entity.getInpUser());
        dto.setInpDatetime(entity.getInpDatetime());
        dto.setAssFeeAprStatus(entity.getAssFeeAprStatus());
        dto.setAssFeeAprUser(entity.getAssFeeAprUser());
        dto.setAssFeeAprDatetime(entity.getAssFeeAprDatetime());
        dto.setAssEstiAprStatus(entity.getAssEstiAprStatus());
        dto.setAssEstiAprUser(entity.getAssEstiAprUser());
        dto.setAssEstiAprDatetime(entity.getAssEstiAprDatetime());

        return dto;
    }

    public void claimToDto(ClaimsDto returnDto, ClaimDetail claimDetail, PolicyDetail policyDetail, ClaimInspectionDetail claimInspectionDetail,List<ClaimTireConditionMe> tireConditionMeList,ClaimAssignClaimHandler claimAssignClaimHandler) {
        returnDto.setClaimNo(getSafeInt(claimDetail.getClaimNo()));
        returnDto.setRefNo(getSafeInt(claimDetail.getRefNo()));
        returnDto.setIntimationNo(getSafeString(claimDetail.getIntimationNo()));
        returnDto.setIsfClaimNo(getSafeString(claimDetail.getIsfClaimNo()));
        returnDto.setPreferredLanguage(getSafeString(claimDetail.getPreferredLanguage()));
        returnDto.setPolicyBranch(getSafeString(claimDetail.getPolicyBranch()));
        returnDto.setPolicyType(getSafeString(claimDetail.getPolicyType()));
        returnDto.setPolicyNumber(getSafeString(claimDetail.getPolicyNumber()));
        returnDto.setVehicleNo(getSafeString(claimDetail.getVehicleNo()));
        returnDto.setReleshipInsurd(getSafeInt(claimDetail.getReleshipInsurd()));
        returnDto.setReporterTitle(getSafeInt(claimDetail.getReporterTitle()));
        returnDto.setReporterName(getSafeString(claimDetail.getReporterName()));
        returnDto.setReporterId(getSafeString(claimDetail.getReporterId()));
        returnDto.setCliNo(getSafeString(claimDetail.getCliNo()));
        returnDto.setOtherContNo(getSafeString(claimDetail.getOtherContNo()));
        returnDto.setInsurdMobNo(getSafeString(claimDetail.getInsurdMobNo()));
        returnDto.setIsSameReportDriver(claimDetail.getIsSameReportDriver());
        returnDto.setDriverStatus(getSafeInt(claimDetail.getDriverStatus()));
        returnDto.setDriverTitle(getSafeInt(claimDetail.getDriverTitle()));
        returnDto.setDriverName(getSafeString(claimDetail.getDriverName()));
        returnDto.setDriverReleshipInsurd(getSafeInt(claimDetail.getDriverReleshipInsurd()));
        returnDto.setDriverNic(getSafeString(claimDetail.getDriverNic()));
        returnDto.setDlNo(getSafeString(claimDetail.getDlNo()));
        returnDto.setDriverLicenceType(getSafeString(claimDetail.getDriverLicenceType()));
        returnDto.setReporterRemark(getSafeString(claimDetail.getReporterRemark()));
        returnDto.setIntimationType(getSafeInt(claimDetail.getIntimationType()));
        returnDto.setLateIntimateReason(getSafeInt(claimDetail.getLateIntimateReason()));
        returnDto.setAccidDate(claimDetail.getAccidDate());
        returnDto.setAccidTime(claimDetail.getAccidTime());
        returnDto.setDateOfReport(claimDetail.getDateOfReport());
        returnDto.setTimeOfReport(claimDetail.getTimeOfReport());
        returnDto.setIsCatEvent(claimDetail.getIsCatEvent());
        returnDto.setCatEventCode(getSafeInt(claimDetail.getCatEventCode()));
        returnDto.setCatEvent(getSafeString(claimDetail.getCatEvent()));
        returnDto.setCauseOfLoss(getSafeInt(claimDetail.getCauseOfLoss()));
        returnDto.setAccidDesc(getSafeString(claimDetail.getAccidDesc()));
        returnDto.setPlaceOfAccid(getSafeString(claimDetail.getPlaceOfAccid()));
        returnDto.setNearestCity(getSafeInt(claimDetail.getNearestCity()));
        returnDto.setCurrentLocation(getSafeString(claimDetail.getCurrentLocation()));
        returnDto.setNearPoliceStation(getSafeInt(claimDetail.getNearPoliceStation()));
        returnDto.setIsFirstStatementReq(claimDetail.getIsFirstStatementReq());
        returnDto.setFirstStatementReqReason(getSafeInt(claimDetail.getFirstStatementReqReason()));
        returnDto.setFirstStatementRemark(getSafeString(claimDetail.getFirstStatementRemark()));
        returnDto.setInspectionType(getSafeString(claimDetail.getInspectionType()));
        returnDto.setInspectionTypeReason(getSafeInt(claimDetail.getInspectionTypeReason()));
        returnDto.setDraftReason(getSafeInt(claimDetail.getDraftReason()));
        returnDto.setFollowCallUserId(getSafeString(claimDetail.getFollowCallUserId()));
        returnDto.setFollowCallDoneDateTime(claimDetail.getFollowCallDoneDateTime());
        returnDto.setFollowCallContactPersonTitle(getSafeInt(claimDetail.getFollowCallContactPersonTitle()));
        returnDto.setFollowCallContactPersonName(getSafeString(claimDetail.getFollowCallContactPersonName()));
        returnDto.setFollowCallContactNumber(getSafeString(claimDetail.getFollowCallContactNumber()));
        returnDto.setNcbProm(getSafeString(claimDetail.getNcbProm()));
        returnDto.setNcbReason(getSafeInt(claimDetail.getNcbReason()));
        returnDto.setNcbRemark(getSafeString(claimDetail.getNcbRemark()));
        returnDto.setLomoProm(getSafeString(claimDetail.getLomoProm()));
        returnDto.setLomoReason(getSafeInt(claimDetail.getLomoReason()));
        returnDto.setCallUser(getSafeString(claimDetail.getCallUser()));
        returnDto.setClaimStatus(getSafeInt(claimDetail.getClaimStatus()));
        returnDto.setVehicleColor(getSafeString(claimDetail.getVehicleColor()));
        returnDto.setCoverNoteNo(getSafeString(claimDetail.getCoverNoteNo()));
        returnDto.setPolRefNo(getSafeInt(claimDetail.getPolRefNo()));
        returnDto.setIsfsUpdateStatus(getSafeString(claimDetail.getIsfsUpdateStatus()));
        returnDto.setPolChkStatus(getSafeString(claimDetail.getPolChkStatus()));
        returnDto.setIsNoDamage(claimDetail.getIsNoDamage());
        returnDto.setDamageRemark(getSafeString(claimDetail.getDamageRemark()));
        returnDto.setIsHugeDamage(claimDetail.getIsHugeDamage());
        returnDto.setHugeRemark(getSafeString(claimDetail.getHugeRemark()));
        returnDto.setDamageNotGiven(claimDetail.getDamageNotGiven());
        returnDto.setPrintLetter(claimDetail.getPrintLetter());
        returnDto.setNegPremOut(claimDetail.getNegPremOut());
        returnDto.setLandMark(getSafeString(claimDetail.getLandMark()));
        returnDto.setVehClsId(getSafeInt(claimDetail.getVehClsId()));
        returnDto.setIsDoubt(claimDetail.getIsDoubt());
        returnDto.setIsDoubtRemark(getSafeString(claimDetail.getIsDoubtRemark()));
        returnDto.setIsFutherDamage(claimDetail.getIsFutherDamage());
        returnDto.setDraftRemark(getSafeString(claimDetail.getDraftRemark()));
        returnDto.setAccessusrType(getSafeInt(claimDetail.getAccessusrType()));
        returnDto.setRecStatus(getSafeString(claimDetail.getRecStatus()));
        returnDto.setInpUser(getSafeString(claimDetail.getInpUser()));
        returnDto.setInpTime(claimDetail.getInpTime());
        returnDto.setIsFollowupCallDone(claimDetail.getIsFollowupCallDone());
        returnDto.setDriverDetailNotRelevant(claimDetail.getDriverDetailNotRelevant());
        returnDto.setDriverDetailSubmit(claimDetail.getDriverDetailSubmit());
        returnDto.setPolicyChannelType(getSafeString(claimDetail.getPolicyChannelType()));
        returnDto.setIsTheftAndFound(claimDetail.getIsTheftAndFound());
        policyToDto(returnDto, policyDetail, claimInspectionDetail, tireConditionMeList,claimAssignClaimHandler);
    }

    public void policyToDto(ClaimsDto returnDto, PolicyDetail policyDetail, ClaimInspectionDetail claimInspectionDetail,List<ClaimTireConditionMe> tireConditionMeList, ClaimAssignClaimHandler claimAssignClaimHandler) {
        PolicyDto dto = new PolicyDto();

        // Basic Policy Information
        dto.setPolicyRefNo(getSafeInt(policyDetail.getPolicyRefNo()));
        dto.setPolicyBranch(getSafeString(policyDetail.getPolicyBranch()));
        dto.setPolicyType(getSafeString(policyDetail.getPolicyType()));
        dto.setPolicyNumber(getSafeString(policyDetail.getPolicyNumber()));
        dto.setRenewalCount(policyDetail.getRenewalCount());
        dto.setEndorsementCount(policyDetail.getEndorsementCount());
        dto.setVehicleNumber(getSafeString(policyDetail.getVehicleNumber()));

        // Date fields
        dto.setExpireDate(policyDetail.getExpireDate());
        dto.setInspectionDate(policyDetail.getInspectionDate());
        dto.setOriginalInspectionDate(policyDetail.getOriginalInspectionDate());

        // Policy Status
        dto.setPolicyStatus(getSafeString(policyDetail.getPolicyStatus()));

        // Customer Information
        dto.setCustomerName(getSafeString(policyDetail.getCustomerName()));
        dto.setCustomerNic(getSafeString(policyDetail.getCustomerNic()));
        dto.setCustomerAddressLine1(getSafeString(policyDetail.getCustomerAddressLine1()));
        dto.setCustomerAddressLine2(getSafeString(policyDetail.getCustomerAddressLine2()));
        dto.setCustomerAddressLine3(getSafeString(policyDetail.getCustomerAddressLine3()));
        dto.setCustomerMobileNo(getSafeString(policyDetail.getCustomerMobileNo()));
        dto.setCustomerLandNo(getSafeString(policyDetail.getCustomerLandNo()));

        // Financials
        dto.setAnnualPremium(policyDetail.getAnnualPremium());
        dto.setSumInsured(policyDetail.getSumInsured());

        // Claim Dates
        dto.setLatestClaimIntimationDate(policyDetail.getLatestClaimIntimationDate());
        dto.setLatestClaimLossDate(policyDetail.getLatestClaimLossDate());

        // Agent Information
        dto.setAgentBroker(getSafeString(policyDetail.getAgentBroker()));
        dto.setTotalPremiumOutstanding(policyDetail.getTotalPremiumOutstanding());
        dto.setNoOfDaysPremiumOutstanding(policyDetail.getNoOfDaysPremiumOutstanding());

        // Vehicle Details
        dto.setEngineNo(getSafeString(policyDetail.getEngineNo()));
        dto.setChassisNo(getSafeString(policyDetail.getChassisNo()));
        dto.setVehicleMake(getSafeString(policyDetail.getVehicleMake()));
        dto.setVehicleModel(getSafeString(policyDetail.getVehicleModel()));
        dto.setManufactureYear(policyDetail.getManufactureYear());
        dto.setExcess(policyDetail.getExcess());

        // NCB (No Claim Bonus)
        dto.setNcbRate(policyDetail.getNcbRate());
        dto.setNcbAmount(policyDetail.getNcbAmount());

        // Cover Note and Type
        dto.setCoverNoteNo(getSafeString(policyDetail.getCoverNoteNo()));
        dto.setCoverType(getSafeString(policyDetail.getCoverType()));

        // Policy Channel and Flags
        dto.setChannel(getSafeString(policyDetail.getChannel()));
        dto.setUpdateFlag(getSafeString(policyDetail.getUpdateFlag()));
        dto.setInsertFlag(getSafeString(policyDetail.getInsertFlag()));

        // Creation Information
        dto.setCreateUser(getSafeString(policyDetail.getCreateUser()));
        dto.setCreateDate(policyDetail.getCreateDate());
        dto.setCreateTime(policyDetail.getCreateTime());

        // Cancellation and Lapsed Policy Information
        dto.setCancelReason(getSafeString(policyDetail.getCancelReason()));
        dto.setLapsedDate(policyDetail.getLapsedDate());
        dto.setRegisterDate(policyDetail.getRegisterDate());
        dto.setPolicyCancelDate(policyDetail.getPolicyCancelDate());

        // Location and Risk
        dto.setLocation(getSafeString(policyDetail.getLocation()));
        dto.setRisk(getSafeString(policyDetail.getRisk()));
        dto.setBodyType(getSafeString(policyDetail.getBodyType()));

        // Client and Finance Details
        dto.setClientId(getSafeString(policyDetail.getClientId()));
        dto.setIsThirdParty(policyDetail.getIsThirdParty());
        dto.setFinanceCompany(getSafeString(policyDetail.getFinanceCompany()));
        dto.setVehicleUsage(getSafeString(policyDetail.getVehicleUsage()));

        // Fuel and Seating Information
        dto.setFuelType(getSafeString(policyDetail.getFuelType()));
        dto.setNoOfSeat(policyDetail.getNoOfSeat());
        dto.setVehicleAge(policyDetail.getVehicleAge());

        // Suspension and Additional Info
        dto.setPolicySuspend(policyDetail.getPolicySuspend());
        dto.setEngineCapacity(policyDetail.getEngineCapacity());
        dto.setBranchCode(getSafeString(policyDetail.getBranchCode()));
        dto.setProduct(getSafeString(policyDetail.getProduct()));

        // Modification Details
        dto.setLastModifyUser(getSafeString(policyDetail.getLastModifyUser()));
        dto.setLastModifyDateTime(policyDetail.getLastModifyDateTime());
        dto.setCurrentPolicyStatus(getSafeString(policyDetail.getCurrentPolicyStatus()));

        // Vehicle Color and Trade Plate
        dto.setVehicleColor(getSafeString(policyDetail.getVehicleColor()));
        dto.setTradePlateNo(getSafeString(policyDetail.getTradePlateNo()));

        // Last Modified Information
        dto.setLastModifyDate(policyDetail.getLastModifyDate());

        // Company Information
        dto.setIndComFlag(getSafeString(policyDetail.getIndComFlag()));
        dto.setCompanyBranch(getSafeString(policyDetail.getCompanyBranch()));
        dto.setCompanyCode(getSafeString(policyDetail.getCompanyCode()));

        // CMS Update and Finance Info
        dto.setCmsUpdateDateTime(policyDetail.getCmsUpdateDateTime());
        dto.setFinCompanyCode(getSafeString(policyDetail.getFinCompanyCode()));
        dto.setFinCompanyBranch(getSafeString(policyDetail.getFinCompanyBranch()));

        // Loan Information
        dto.setLoanAccountNo(getSafeString(policyDetail.getLoanAccountNo()));

        // Identifier Code
        dto.setIdenCode(getSafeString(policyDetail.getIdenCode()));

        // Last Digit Information
        dto.setVehicleNoLastDigit(getSafeString(policyDetail.getVehicleNoLastDigit()));
        dto.setPolicyNumberLastDigit(getSafeString(policyDetail.getPolicyNumberLastDigit()));

        // Policy Channel Type
        dto.setPolicyChannelType(getSafeString(policyDetail.getPolicyChannelType()));

        // Business Information
        dto.setBusinessType(getSafeString(policyDetail.getBusinessType()));
        dto.setIntroducer(getSafeString(policyDetail.getIntroducer()));
        dto.setBankRefNo(getSafeString(policyDetail.getBankRefNo()));

        // Workflow and Category
        dto.setWorkflow(getSafeString(policyDetail.getWorkflow()));
        dto.setCategoryDesc(getSafeString(policyDetail.getCategoryDesc()));

        // Set Policy detail
        returnDto.setPolicyDto(dto);
        returnDto.setInspectionDto(inspectionMapper(claimInspectionDetail));

        if (tireConditionMeList == null || tireConditionMeList.isEmpty()) {
            List<ClaimTireConditionMe> defaultList = new ArrayList<>();
            for (int i = 0; i < 4; i++) {
                ClaimTireConditionMe condition = new ClaimTireConditionMe();
                condition.setRightFront(condition.getRightFront());
                condition.setLeftFront(condition.getLeftFront());
                condition.setRightRear(condition.getRightRear());
                condition.setLeftRear(condition.getLeftRear());
                condition.setRightRearInner(condition.getRightRearInner());
                condition.setLeftRearInner(condition.getLeftRearInner());
                condition.setOther(condition.getOther());
                condition.setRefNo(condition.getRefNo());
                condition.setClaimNo(condition.getClaimNo());
                condition.setPosition(condition.getPosition());
                defaultList.add(condition);
            }
            returnDto.setTireConditionMeList(defaultList);
        }else{
            returnDto.setTireConditionMeList(tireConditionMeList);
        }
        returnDto.setClaimAssignClaimHandlerDto(handlerMapper(claimAssignClaimHandler));
    }

    private ClaimAssignClaimHandlerDto handlerMapper(ClaimAssignClaimHandler entity) {
        if (entity == null) {
            return null;
        }

        ClaimAssignClaimHandlerDto dto = new ClaimAssignClaimHandlerDto();

        dto.setTxnNo(entity.getTxnNo());
        dto.setClaimPanelAssignUsers(entity.getClaimPanelAssignUsers());
        dto.setClaimPanelAssignUserDateTime(entity.getClaimPanelAssignUserDateTime());
        dto.setClaimPanelDecision(entity.getClaimPanelDecision());
        dto.setClaimPanelDecisionDateTime(entity.getClaimPanelDecisionDateTime());
        dto.setRepudiatedType(entity.getRepudiatedType());

        dto.setRepudiatedLetterPrintUserId(entity.getRepudiatedLetterPrintUserId());

        dto.setFinancialInterest(entity.getFinancialInterest());
        dto.setLeasingRefNo(entity.getLeasingRefNo());
        dto.setFinalizeUserId(entity.getFinalizeUserId());
        dto.setFinalizeDateTime(entity.getFinalizeDateTime());

        dto.setSupplyOrderAssignStatus(entity.getSupplyOrderAssignStatus());
        dto.setSupplyOrderAssignUser(entity.getSupplyOrderAssignUser());
        dto.setTotalAcrApproved(entity.getAprvTotAcrAmount());
        dto.setSupplyOrderAssignDateTime(entity.getSupplyOrderAssignDateTime());
        dto.setSupplyOrderCreateUser(entity.getSupplyOrderCreateUser());
        dto.setSupplyOrderCreateDateTime(entity.getSupplyOrderCreateDateTime());
        dto.setSupplyOrderCreateClose(entity.getSupplyOrderCreateClose());

        dto.setFileUserStoreUserId(entity.getFileUserStoreUserId());
        dto.setFileStoreDateTime(entity.getFileStoreDateTime());
        dto.setReopenAssignUserId(entity.getReopenAssignUserId());
        dto.setReopenAssignUserDateTime(entity.getReopenAssignUserDateTime());
        dto.setReopenUserId(entity.getReopenUserId());
        dto.setReopenDateTime(entity.getReopenDateTime());
        dto.setDecisionMakingAssignUserId(entity.getDecisionMakingAssignUserId());
        dto.setDecisionMakingAssignDateTime(entity.getDecisionMakingAssignDateTime());
        dto.setInvestigationStatus(entity.getInvestigationStatus());
        dto.setInvestigationAssignUserId(entity.getInvestigationAssignUserId());
        dto.setInvestigationAssignDateTime(entity.getInvestigationAssignDateTime());
        dto.setInvestigationArrangeUserId(entity.getInvestigationArrangeUserId());
        dto.setInvestigationArrangeDateTime(entity.getInvestigationArrangeDateTime());
        dto.setInvestigationCompletedUserId(entity.getInvestigationCompletedUserId());
        dto.setInvestigationCompletedDateTime(entity.getInvestigationCompletedDateTime());
        dto.setSpecialApprovalInputUserId(entity.getSpecialApprovalInputUserId());
        dto.setSpecialApprovalInputDateTime(entity.getSpecialApprovalInputDateTime());
        dto.setSpecialApprovalUserId(entity.getSpecialApprovalUserId());
        dto.setSpecialApprovalDateTime(entity.getSpecialApprovalDateTime());
        dto.setOldClaimStatus(entity.getOldClaimStatus());
        dto.setOldEngineerClaimStatus(entity.getOldEngineerClaimStatus());
        dto.setAprvAdvanceAmount(entity.getAprvAdvanceAmount());
        dto.setReopenType(entity.getReopenType());
        dto.setCloseStatus(entity.getCloseStatus());
        dto.setCloseUser(entity.getCloseUser());
        dto.setCloseDateTime(entity.getCloseDateTime());
        dto.setRepudiatedLetterType(entity.getRepudiatedLetterType());
        dto.setInpStatus(entity.getInpStatus());
        dto.setInpUserId(entity.getInpUserId());
        dto.setInpDateTime(entity.getInpDateTime());
        dto.setVersionNo(entity.getVersionNo());
        dto.setIsExcessInclude(entity.getIsExcessInclude());
        dto.setIsProvideOffer(entity.getIsProvideOffer());
        dto.setLetterPanelUserId(entity.getLetterPanelUserId());
        dto.setAdvanceApprovalAssignUser(entity.getAdvanceApprovalAssignUser());
        dto.setAdvanceApprovalAssignDateTime(entity.getAdvanceApprovedDateTime());
        dto.setAdvanceStatus(entity.getAdvanceStatus());
        dto.setAdvanceForwardedUser(entity.getAdvanceForwardedUser());
        dto.setApprovalPendingAdvanceAmount(entity.getApprovalPendingAdvanceAmount());
        dto.setAdvanceApprovedUser(entity.getAdvanceApprovedUser());
        dto.setAdvanceApprovedDateTime(entity.getAdvanceApprovedDateTime());
        dto.setForwardedEngineer(entity.getForwardedEngineer());

        return dto;
    }


    public InspectionDto inspectionMapper(ClaimInspectionDetail entity) {
        if (entity == null) {
            return null;
        }
        InspectionDto dto = new InspectionDto();

        dto.setInspectionId(entity.getInspectionId());
        dto.setRefNo(entity.getRefNo());
        dto.setJobNo(entity.getJobNo());
        dto.setClaimNo(entity.getClaimNo());
        dto.setMakeConfirm(entity.getMakeConfirm());
        dto.setModelConfirm(entity.getModelConfirm());
        dto.setEngNoConfirm(entity.getEngNoConfirm());
        dto.setChassisNoConfirm(entity.getChassisNoConfirm());
        dto.setChassisNo(entity.getChassisNo());
        dto.setNotCheckedReason(entity.getNotCheckedReason());
        dto.setYearMakeConfirm(entity.getYearMakeConfirm());
        dto.setInspectDatetime(entity.getInspectDatetime());
        dto.setPav(entity.getPav());
        dto.setDamageDetails(entity.getDamageDetails());
        dto.setPad(entity.getPad());
        dto.setGenunOfAccid(entity.getGenunOfAccid());
        dto.setFirstStatementRqed(entity.getFirstStatementRqed());
        dto.setFirstStatementReqReason(entity.getFirstStatementReqReason());
        dto.setInvestRqed(entity.getInvestRqed());
        dto.setAssessorRemark(entity.getAssessorRemark());
        dto.setInspectionType(entity.getInspectionType());
        dto.setJobType(entity.getJobType());
        dto.setAssignedLocation(entity.getAssignedLocation());
        dto.setCurrentLocation(entity.getCurrentLocation());
        dto.setPlaceOfInspection(entity.getPlaceOfInspection());
        dto.setMileage(entity.getMileage());
        dto.setCostOfCall(entity.getCostOfCall());
        dto.setOtherFee(entity.getOtherFee());
        dto.setTotalAssessorFee(entity.getTotalAssessorFee());
        dto.setFeeDesc(entity.getFeeDesc());
        dto.setRecordStatus(entity.getRecordStatus());
        dto.setInpUser(entity.getInpUser());
        dto.setInpDatetime(entity.getInpDatetime());
        dto.setAssFeeAprStatus(entity.getAssFeeAprStatus());
        dto.setAssFeeAprUser(entity.getAssFeeAprUser());
        dto.setAssFeeAprDatetime(entity.getAssFeeAprDatetime());
        dto.setAssEstiAprStatus(entity.getAssEstiAprStatus());
        dto.setAssEstiAprUser(entity.getAssEstiAprUser());
        dto.setAssEstiAprDatetime(entity.getAssEstiAprDatetime());
        dto.setAssignAssessorUser(entity.getAssignAssessorUser());
        dto.setAssignAssessorDatetime(entity.getAssignAssessorDatetime());
        dto.setAssignRteUser(entity.getAssignRteUser());
        dto.setAssignRteDatetime(entity.getAssignRteDatetime());
        dto.setApproveAssignRteUser(entity.getApproveAssignRteUser());
        dto.setApproveAssignDatetime(entity.getApproveAssignDatetime());
        dto.setFwdRteDesktopUser(entity.getFwdRteDesktopUser());
        dto.setFwdRteDesktopDatetime(entity.getFwdRteDesktopDatetime());
        dto.setAssignTcUser(entity.getAssignTcUser());
        dto.setAssignTcDatetime(entity.getAssignTcDatetime());
        dto.setFwdTcUser(entity.getFwdTcUser());
        dto.setFwdTcDatetime(entity.getFwdTcDatetime());
        dto.setFwdTcDesktopUser(entity.getFwdTcDesktopUser());
        dto.setFwdTcDesktopDatetime(entity.getFwdTcDesktopDatetime());
        dto.setVehicleAvailable(entity.getVehicleAvailable());

        return dto;

    }


    private Integer getSafeInt(Integer value) {
        return value == null ? -1 : value; // if -1 means "not available"
    }

    private String getSafeString(String value) {
        return (value == null || value.trim().isEmpty()) ? AppConstant.NOT_AVAILABLE : value;
    }

}
