package com.misyn.onmisite.service;

import com.misyn.onmisite.dto.ListItem;
import org.springframework.web.multipart.MultipartFile;

import java.io.InputStream;
import java.util.List;

public interface StorageService {
    void uploadFile(MultipartFile file, String objectName);

    void uploadFile(InputStream inputStream, String objectName, long contentLength);

    void uploadMp4File(InputStream inputStream, String objectName, long contentLength);

    void deleteFile(String objectName);

    InputStream downloadFile(String objectName);

    List<ListItem> getDocumentTypes();
}
