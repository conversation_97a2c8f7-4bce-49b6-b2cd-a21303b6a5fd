package com.misyn.onmisite.service;

import com.misyn.onmisite.dto.*;
import com.misyn.onmisite.dto.form.AdminVideoForm;
import com.misyn.onmisite.dto.input.InputJobDetailDto;
import com.misyn.onmisite.exception.ClientAuthException;
import com.misyn.onmisite.exception.RecordNotFoundException;

import java.util.List;
import java.util.zip.DataFormatException;

public interface JobService {
    JobDetailDto saveJobDetail(InputJobDetailDto inputJobDetailDto, Long companyId, boolean isJoinCall);


    JobDetailDto saveAndJoinExistingCall(Long jobId, String userName, Long companyId, String accessToken);


    AdminVideoForm getAgentVideoForm(String accessToken, String createdBy) throws RecordNotFoundException;

    AdminVideoForm getAgentVideoForm(Long companyId, Long jobId) throws RecordNotFoundException;

    AdminVideoForm getClientVideoForm(String accessToken) throws ClientAuthException;

    CallUploadImageVideoDetailDto getCallUploadImageVideoDetailDto(Long jobId, Long callId, int callIndex);

    CallUploadImageVideoDetailDto getCallUploadImageVideoDetailDto(Long callId);

    void updateCallStartDateTime(String accessToken);

    int updateCallEndDateTime(String accessToken, String createdBy);

    int updateGeolocation(CallDetailsDto callDetailsDto);

    CallDetailsDto getCallDetails(String accessToken);

    void saveTyreConditionDetail(TyreConditionDataMapDto dataMapDto) throws Exception;

    void saveInspectionDetail(ClaimInspectionInfoMainMeDTO dataMapDto) throws Exception;


}
