package com.misyn.onmisite.service;

import com.misyn.onmisite.dto.TwilioCallbackDto;
import com.misyn.onmisite.dto.TwilioTokenDto;

public interface TwilioService {

    TwilioTokenDto getTwilioTokenByAdmin(TwilioTokenDto twilioTokenRequest);

    TwilioTokenDto getTwilioTokenByClient(TwilioTokenDto twilioTokenRequest);


    void startAllRecording(String roomSid);

    void stopAllRecording(String roomSid);

    void compositionVideo();

    void downloadVideoFiles();


    TwilioCallbackDto saveTwilioCallbackDetail(TwilioCallbackDto twilioCallbackDto);


}
