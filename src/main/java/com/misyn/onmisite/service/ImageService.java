package com.misyn.onmisite.service;

import com.misyn.onmisite.dto.ClaimPreviousPhotosResponseDto;
import com.misyn.onmisite.dto.ImageData;
import com.misyn.onmisite.dto.UploadImageDetailsDto;
import com.misyn.onmisite.dto.UserDto;

public interface ImageService {
    ImageData saveImageData(ImageData imageData, UserDto user);

    ImageData saveEditImageData(ImageData imageData, UserDto user);

    UploadImageDetailsDto getUploadImageDetailsDto(Long uploadImageDetailsRefNo);

    UploadImageDetailsDto deleteByUploadImageDetailsRefNo(Long uploadImageDetailsRefNo);

    ClaimPreviousPhotosResponseDto getPreviousPhotosByClaimNumber(Integer claimNumber);
}
