image: registry.misynergy.com/hnb-mcms/library/maven:3.8.8-eclipse-temurin-21

stages:
- package
- docker

variables:
  MAVEN_CLI_OPTS: "-s .m2/settings.xml --batch-mode -Dmaven.repo.local=.m2/repository -DskipTests=true "

cache:
  paths:
  - .m2/repository/
  - target/

package-qa:
  stage: package
  script:
  - mvn $MAVEN_CLI_OPTS package -P qa
  artifacts:
    expire_in: 15 minutes
    paths:
    - target/common-service-api.jar
  only:
    - /^[0-9.]*MS[0-9]*-QA$/
  except:
    - branches

package-uat:
  stage: package
  script:
    - mvn $MAVEN_CLI_OPTS package -P uat
  artifacts:
    expire_in: 15 minutes
    paths:
      - target/common-service-api.jar
  only:
    - /^[0-9.]*MS[0-9]*-UAT$/
  except:
    - branches

package-prd:
  stage: package
  script:
    - mvn $MAVEN_CLI_OPTS package -P prd
  artifacts:
    expire_in: 15 minutes
    paths:
      - target/common-service-api.jar
  only:
    - /^[0-9.]*$/
  except:
    - branches

docker-qa:
  stage: docker
  image: proxy-registry/library/docker:20.10.18-dind
  before_script:
    - docker login -u "$MISYN_REGISTRY_USER" -p "$MISYN_REGISTRY_PASSWORD" $MISYN_REGISTRY
  script:
    - docker build -f ./docker/qa-dockerization -t $MISYN_REGISTRY/$HARBOR_PROJECT/$HARBOR_REPOSITORY:$CI_COMMIT_TAG .
    - docker push $MISYN_REGISTRY/$HARBOR_PROJECT/$HARBOR_REPOSITORY:$CI_COMMIT_TAG
    - docker image rm $MISYN_REGISTRY/$HARBOR_PROJECT/$HARBOR_REPOSITORY:$CI_COMMIT_TAG
  only:
    - /^[0-9.]*MS[0-9]*-QA$/
  except:
    - branches

docker-uat:
  stage: docker
  image: proxy-registry/library/docker:20.10.18-dind
  before_script:
    - docker login -u "$MISYN_REGISTRY_USER" -p "$MISYN_REGISTRY_PASSWORD" $MISYN_REGISTRY
  script:
    - docker build -f ./docker/qa-dockerization -t $MISYN_REGISTRY/$HARBOR_PROJECT/$HARBOR_REPOSITORY:$CI_COMMIT_TAG .
    - docker push $MISYN_REGISTRY/$HARBOR_PROJECT/$HARBOR_REPOSITORY:$CI_COMMIT_TAG
    - docker image rm $MISYN_REGISTRY/$HARBOR_PROJECT/$HARBOR_REPOSITORY:$CI_COMMIT_TAG
  only:
    - /^[0-9.]*MS[0-9]*-UAT$/
  except:
    - branches


