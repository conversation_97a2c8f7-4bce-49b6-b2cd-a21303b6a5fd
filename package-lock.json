{"name": "mcms-onmisite-webapp", "version": "1.0.0", "lockfileVersion": 3, "requires": true, "packages": {"": {"name": "mcms-onmisite-webapp", "version": "1.0.0", "license": "ISC", "dependencies": {"@fortawesome/fontawesome-free": "^6.7.2", "bootstrap": "^5.3.3", "jquery": "^3.7.1", "jquery.magnify": "^1.6.3", "lucide": "^0.483.0"}}, "node_modules/@fortawesome/fontawesome-free": {"version": "6.7.2", "license": "(CC-BY-4.0 AND OFL-1.1 AND MIT)", "engines": {"node": ">=6"}}, "node_modules/@popperjs/core": {"version": "2.11.8", "license": "MIT", "peer": true, "funding": {"type": "opencollective", "url": "https://opencollective.com/popperjs"}}, "node_modules/bootstrap": {"version": "5.3.3", "funding": [{"type": "github", "url": "https://github.com/sponsors/twbs"}, {"type": "opencollective", "url": "https://opencollective.com/bootstrap"}], "license": "MIT", "peerDependencies": {"@popperjs/core": "^2.11.8"}}, "node_modules/jquery": {"version": "3.7.1", "license": "MIT"}, "node_modules/jquery.magnify": {"version": "1.6.3", "license": "MIT"}, "node_modules/lucide": {"version": "0.483.0", "license": "ISC"}}}